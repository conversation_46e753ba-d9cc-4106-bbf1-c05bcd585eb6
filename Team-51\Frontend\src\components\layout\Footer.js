import React from 'react';
import './Footer.css';

function Footer() {
  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-content">
          <div className="footer-section">
            <div className="footer-logo">
              <h3>PANS</h3>
              <p>People's Awareness Network Society</p>
            </div>
            <div className="footer-social">
              <a href="https://facebook.com/pansngo" className="footer-social-link">
                <i className="fab fa-facebook-f"></i>
              </a>
              <a href="https://instagram.com/pansngo" className="footer-social-link">
                <i className="fab fa-instagram"></i>
              </a>
              <a href="https://linkedin.com/company/pansngo" className="footer-social-link">
                <i className="fab fa-linkedin-in"></i>
              </a>
            </div>
          </div>
          
          <div className="footer-section">
            <h4>Quick Links</h4>
            <ul className="footer-links">
              <li><a href="#home">Home</a></li>
              <li><a href="#about">About Us</a></li>
              <li><a href="#programs">Projects</a></li>
              <li><a href="#get-involved">Get Involved</a></li>
              <li><a href="#contact">Contact</a></li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4>Our Projects</h4>
            <ul className="footer-links">
              <li><a href="#she-dares">She Dares Initiative</a></li>
              <li><a href="#youth-adda">Youth Adda (Janshruti)</a></li>
              <li><a href="#skill-training">Skill Training Centres</a></li>
              <li><a href="#teach-for-india">Teach for India Fellowship</a></li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4>Get Involved</h4>
            <ul className="footer-links">
              <li><a href="#volunteer">Volunteer</a></li>
              <li><a href="#donate">Donate</a></li>
              <li><a href="#partner">Partner With Us</a></li>
              <li><a href="#fundraise">Fundraise</a></li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4>Contact Info</h4>
            <div className="footer-contact">
              <p>
                <i className="fas fa-map-marker-alt"></i>
                Jaipur, Rajasthan, India
              </p>
              <p>
                <i className="fas fa-phone"></i>
                +91 9116 498 949
              </p>
              <p>
                <i className="fas fa-envelope"></i>
                <EMAIL>
              </p>
              <p>
                <i className="fas fa-globe"></i>
                pansngo.com
              </p>
            </div>
          </div>
        </div>
        
        <div className="footer-bottom">
          <div className="footer-copyright">
            <p>&copy; 2025 PANS - People's Awareness Network Society. All rights reserved. Registered under Societies Act 1958.</p>
          </div>
          <div className="footer-legal">
            <a href="#privacy">Privacy Policy</a>
            <a href="#terms">Terms of Service</a>
            <a href="#transparency">Impact Report</a>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
