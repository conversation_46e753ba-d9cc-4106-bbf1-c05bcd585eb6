import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { GoogleLogin } from '@react-oauth/google';
import { jwtDecode } from 'jwt-decode';
import './AuthForm.css';

function AuthForm({ isOpen, onClose, initialMode }) {
  const [isSignup, setIsSignup] = useState(initialMode === 'signup');
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setIsSignup(initialMode === 'signup');
    setError('');
    setSuccess('');
    setFormData({
      username: '',
      email: '',
      password: '',
      confirmPassword: ''
    });
  }, [isOpen, initialMode]);

  if (!isOpen) return null;

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    if (isSignup) {
      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match');
        setLoading(false);
        return;
      }
      if (formData.password.length < 6) {
        setError('Password must be at least 6 characters long');
        setLoading(false);
        return;
      }
    }

    const url = isSignup
      ? 'http://localhost:5000/api/auth/signup'
      : 'http://localhost:5000/api/auth/signin';

    try {
      const payload = isSignup 
        ? { username: formData.username, email: formData.email, password: formData.password }
        : { username: formData.username, password: formData.password };

      const response = await axios.post(url, payload);
      
      if (isSignup) {
        setSuccess('Account created successfully! Welcome to PANS NGO community.');
        setIsSignup(false);
        setFormData({
          username: '',
          email: '',
          password: '',
          confirmPassword: ''
        });
      } else {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('username', formData.username);
        setSuccess('Welcome back! Redirecting...');
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }
    } catch (err) {
      setError(err.response?.data?.msg || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSuccess = async (credentialResponse) => {
    try {
      const decoded = jwtDecode(credentialResponse.credential);
      const res = await axios.post('http://localhost:5000/api/auth/google-auth', {
        email: decoded.email,
        name: decoded.name,
        picture: decoded.picture
      });
      
      localStorage.setItem('token', res.data.token);
      localStorage.setItem('username', res.data.username);
      setSuccess('Welcome to PANS NGO! Redirecting...');
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      setError('Google authentication failed. Please try again.');
    }
  };

  return (
    <div className="auth-modal-overlay" onClick={onClose}>
      <div className="auth-modal" onClick={(e) => e.stopPropagation()}>
        <div className="auth-modal-header">
          <h2 className="auth-modal-title">
            {isSignup ? 'Join PANS NGO' : 'Welcome Back'}
          </h2>
          <p className="auth-modal-subtitle">
            {isSignup 
              ? 'Create your account to start making a difference' 
              : 'Sign in to continue your journey with us'
            }
          </p>
          <button className="auth-modal-close" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="auth-modal-body">
          {error && <div className="auth-alert auth-alert-error">{error}</div>}
          {success && <div className="auth-alert auth-alert-success">{success}</div>}

          <form onSubmit={handleSubmit} className="auth-form">
            <div className="auth-form-group">
              <label className="auth-form-label">Username</label>
              <div className="auth-input-wrapper">
                <i className="fas fa-user auth-input-icon"></i>
                <input
                  type="text"
                  name="username"
                  placeholder="Enter your username"
                  className="auth-input"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  autoFocus
                />
              </div>
            </div>

            {isSignup && (
              <div className="auth-form-group">
                <label className="auth-form-label">Email</label>
                <div className="auth-input-wrapper">
                  <i className="fas fa-envelope auth-input-icon"></i>
                  <input
                    type="email"
                    name="email"
                    placeholder="Enter your email"
                    className="auth-input"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
            )}

            <div className="auth-form-group">
              <label className="auth-form-label">Password</label>
              <div className="auth-input-wrapper">
                <i className="fas fa-lock auth-input-icon"></i>
                <input
                  type="password"
                  name="password"
                  placeholder="Enter your password"
                  className="auth-input"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            {isSignup && (
              <div className="auth-form-group">
                <label className="auth-form-label">Confirm Password</label>
                <div className="auth-input-wrapper">
                  <i className="fas fa-lock auth-input-icon"></i>
                  <input
                    type="password"
                    name="confirmPassword"
                    placeholder="Confirm your password"
                    className="auth-input"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
            )}

            <button 
              type="submit" 
              className="auth-btn auth-btn-primary"
              disabled={loading}
            >
              {loading ? (
                <span className="auth-btn-loading">
                  <i className="fas fa-spinner fa-spin"></i>
                  {isSignup ? 'Creating Account...' : 'Signing In...'}
                </span>
              ) : (
                isSignup ? 'Create Account' : 'Sign In'
              )}
            </button>
          </form>

          <div className="auth-divider">
            <span>or continue with</span>
          </div>

          <div className="auth-google-wrapper">
            <GoogleLogin
              onSuccess={handleGoogleSuccess}
              onError={() => setError('Google authentication failed')}
              width="100%"
              text={isSignup ? 'signup_with' : 'signin_with'}
              size="large"
            />
          </div>

          <div className="auth-switch">
            <p>
              {isSignup ? 'Already have an account?' : "Don't have an account?"}{' '}
              <button
                type="button"
                className="auth-switch-btn"
                onClick={() => {
                  setIsSignup(!isSignup);
                  setError('');
                  setSuccess('');
                  setFormData({
                    username: '',
                    email: '',
                    password: '',
                    confirmPassword: ''
                  });
                }}
              >
                {isSignup ? 'Sign In' : 'Join Us'}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthForm;
