[{"C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js": "3", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js": "4", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js": "5", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js": "7", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js": "9"}, {"size": 394, "mtime": 1752339457265, "results": "10", "hashOfConfig": "11"}, {"size": 2167, "mtime": 1752344651803, "results": "12", "hashOfConfig": "11"}, {"size": 3376, "mtime": 1752339457260, "results": "13", "hashOfConfig": "11"}, {"size": 2467, "mtime": 1752339457264, "results": "14", "hashOfConfig": "11"}, {"size": 3568, "mtime": 1752339457263, "results": "15", "hashOfConfig": "11"}, {"size": 2428, "mtime": 1752339457261, "results": "16", "hashOfConfig": "11"}, {"size": 8731, "mtime": 1752339457258, "results": "17", "hashOfConfig": "11"}, {"size": 20877, "mtime": 1752346198590, "results": "18", "hashOfConfig": "11"}, {"size": 16229, "mtime": 1752346143451, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yx484d", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js", ["47", "48", "49", "50"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js", ["51", "52"], [], {"ruleId": "53", "severity": 1, "message": "54", "line": 15, "column": 15, "nodeType": "55", "endLine": 15, "endColumn": 58}, {"ruleId": "53", "severity": 1, "message": "54", "line": 18, "column": 15, "nodeType": "55", "endLine": 18, "endColumn": 58}, {"ruleId": "53", "severity": 1, "message": "54", "line": 21, "column": 15, "nodeType": "55", "endLine": 21, "endColumn": 58}, {"ruleId": "53", "severity": 1, "message": "54", "line": 24, "column": 15, "nodeType": "55", "endLine": 24, "endColumn": 58}, {"ruleId": "56", "severity": 1, "message": "57", "line": 1, "column": 27, "nodeType": "58", "messageId": "59", "endLine": 1, "endColumn": 36}, {"ruleId": "56", "severity": 1, "message": "60", "line": 114, "column": 10, "nodeType": "58", "messageId": "59", "endLine": 114, "endColumn": 24}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'showAssignForm' is assigned a value but never used."]