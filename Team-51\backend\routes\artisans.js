const express = require('express');
const jwt = require('jsonwebtoken');
const Artisan = require('../models/Artisan');
const TeamLeader = require('../models/TeamLeader');
const User = require('../models/User');
const router = express.Router();

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ msg: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, 's0meSup3r$tr0ng!Key2025');
    const user = await User.findById(decoded.id);
    if (!user) {
      return res.status(403).json({ msg: 'User not found' });
    }
    req.user = {
      ...user.toObject(),
      username: decoded.username || user.username
    };
    next();
  } catch (err) {
    return res.status(403).json({ msg: 'Invalid token' });
  }
};

// Get all artisans
router.get('/', authenticateToken, async (req, res) => {
  try {
    const artisans = await Artisan.find().populate('teamLeaderRef');
    res.json(artisans);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get artisans by team leader ID
router.get('/team-leader/:tl_id', authenticateToken, async (req, res) => {
  try {
    const { tl_id } = req.params;
    
    // Check if the requesting user is the team leader or has admin privileges
    const userInfo = req.user;
    if (userInfo.type !== 'admin' && userInfo.type !== 'admin-pan' && userInfo.username !== tl_id) {
      return res.status(403).json({ msg: 'Access denied' });
    }

    const artisans = await Artisan.find({ tl_id }).populate('teamLeaderRef');
    res.json(artisans);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get available artisans (not currently assigned to work) for a team leader
router.get('/available/:tl_id', authenticateToken, async (req, res) => {
  try {
    const { tl_id } = req.params;
    
    // Check if the requesting user is the team leader or has admin privileges
    const userInfo = req.user;
    if (userInfo.type !== 'admin' && userInfo.type !== 'admin-pan' && userInfo.username !== tl_id) {
      return res.status(403).json({ msg: 'Access denied' });
    }

    // Get artisans under this team leader
    const artisans = await Artisan.find({ tl_id }).populate('teamLeaderRef');
    
    // For now, we'll return all artisans under the team leader
    // In a more complex system, you might check their current work assignments
    res.json(artisans);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Create new artisan
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { customer_id, name, time, production_number, tl_id } = req.body;

    // Validate required fields
    if (!customer_id || !name || !time || !production_number || !tl_id) {
      return res.status(400).json({ msg: 'Missing required fields' });
    }

    // Check if artisan already exists
    const existingArtisan = await Artisan.findOne({ customer_id });
    if (existingArtisan) {
      return res.status(400).json({ msg: 'Artisan with this customer ID already exists' });
    }

    // Verify team leader exists
    const teamLeader = await TeamLeader.findOne({ tl_id });
    if (!teamLeader) {
      return res.status(400).json({ msg: 'Team leader not found' });
    }

    const newArtisan = new Artisan({
      customer_id,
      name,
      time: new Date(time),
      production_number,
      tl_id,
      teamLeaderRef: teamLeader._id
    });

    await newArtisan.save();
    res.status(201).json({ msg: 'Artisan created successfully', artisan: newArtisan });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Update artisan
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { name, time, production_number, tl_id } = req.body;
    const artisan = await Artisan.findById(req.params.id);
    
    if (!artisan) {
      return res.status(404).json({ msg: 'Artisan not found' });
    }

    if (name) artisan.name = name;
    if (time) artisan.time = new Date(time);
    if (production_number) artisan.production_number = production_number;
    if (tl_id) {
      const teamLeader = await TeamLeader.findOne({ tl_id });
      if (!teamLeader) {
        return res.status(400).json({ msg: 'Team leader not found' });
      }
      artisan.tl_id = tl_id;
      artisan.teamLeaderRef = teamLeader._id;
    }

    await artisan.save();
    res.json({ msg: 'Artisan updated successfully', artisan });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Delete artisan
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const artisan = await Artisan.findByIdAndDelete(req.params.id);
    if (!artisan) {
      return res.status(404).json({ msg: 'Artisan not found' });
    }
    res.json({ msg: 'Artisan deleted successfully' });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get all orders assigned to an artisan, with revenue calculation
router.get('/:id/orders', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params; // artisan_id
    const orders = await require('../models/Order').find({
      'artisan_assignments.artisans.artisan_id': id
    });

    const result = orders.map(order => {
      // Find the team leader assignment that includes this artisan
      let artisanAssignment = null;
      let teamLeaderId = null;
      let teamLeadQuantity = null;
      for (const assignment of order.artisan_assignments) {
        const found = assignment.artisans.find(a => a.artisan_id === id);
        if (found) {
          artisanAssignment = found;
          teamLeaderId = assignment.team_leader_id;
          // Find the team leader's assigned quantity
          const tlIndex = order.team_leads.indexOf(teamLeaderId);
          teamLeadQuantity = order.quantities[tlIndex];
          break;
        }
      }
      if (!artisanAssignment) return null;
      // Revenue calculation
      const totalAmount = order.total_amount || 0;
      const commissionPercent = order.commission_percent || 10;
      const commission = (totalAmount * commissionPercent) / 100;
      const distributable = totalAmount - commission;
      const assignedQty = artisanAssignment.assigned_quantity;
      const artisanRevenue = teamLeadQuantity && distributable > 0
        ? (assignedQty / teamLeadQuantity) * distributable
        : 0;
      return {
        order_id: order.order_id,
        product_id: order.product_id,
        product_name: order.product_name,
        status: order.status,
        total_amount: totalAmount,
        commission_percent: commissionPercent,
        commission,
        distributable,
        team_leader_id: teamLeaderId,
        team_leader_quantity: teamLeadQuantity,
        assigned_quantity: assignedQty,
        completed_quantity: artisanAssignment.completed_quantity,
        artisan_revenue: Math.round(artisanRevenue * 100) / 100,
        deadline: order.deadline,
        date_ordered: order.date_ordered
      };
    }).filter(Boolean);
    res.json(result);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

module.exports = router; 