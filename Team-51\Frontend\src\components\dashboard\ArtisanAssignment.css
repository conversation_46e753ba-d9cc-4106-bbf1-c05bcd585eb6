/* Artisan Assignment Styles */
.artisan-assignment {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.assignment-header {
  text-align: center;
  margin-bottom: 30px;
}

.assignment-header h1 {
  font-size: 2.5rem;
  color: var(--primary-green);
  margin-bottom: 10px;
}

.assignment-header p {
  font-size: 1.1rem;
  color: var(--gray-600);
  margin-bottom: 15px;
}

.skill-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: white;
  padding: 25px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  text-align: center;
  border-top: 4px solid;
  transition: transform 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.summary-card.primary {
  border-top-color: var(--primary-green);
}

.summary-card.success {
  border-top-color: #10b981;
}

.summary-card.warning {
  border-top-color: #f59e0b;
}

.summary-card.info {
  border-top-color: #3b82f6;
}

.summary-card h3 {
  font-size: 1rem;
  color: var(--gray-600);
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: 5px;
}

.summary-label {
  font-size: 0.9rem;
  color: var(--gray-500);
}

/* Navigation Tabs */
.assignment-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: var(--shadow-sm);
}

.tab-btn {
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: var(--gray-600);
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: var(--primary-green);
  color: white;
  box-shadow: var(--shadow-sm);
}

.tab-btn:hover:not(.active) {
  background: var(--gray-100);
  color: var(--primary-green);
}

/* Tab Content */
.tab-content {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: var(--shadow-md);
}

/* Orders Overview */
.orders-overview h2 {
  color: var(--gray-800);
  margin-bottom: 25px;
  font-size: 1.8rem;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.order-card {
  background: var(--gray-50);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid var(--primary-green);
  transition: transform 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.order-header h3 {
  color: var(--gray-800);
  font-size: 1.2rem;
}

.order-status {
  background: var(--primary-green);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.order-card h4 {
  color: var(--gray-700);
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.order-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.progress-item span {
  font-size: 0.9rem;
  color: var(--gray-600);
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.order-deadline {
  font-size: 0.9rem;
  color: var(--gray-500);
  margin: 0;
}

/* Assignments Table */
.assignments-section h2 {
  color: var(--gray-800);
  margin-bottom: 25px;
  font-size: 1.8rem;
}

.assignments-table {
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
}

.assignments-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.assignments-table th {
  background: var(--primary-green);
  color: white;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.8rem;
}

.assignments-table td {
  padding: 15px 12px;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-700);
  font-size: 0.9rem;
}

.assignments-table tr:hover {
  background: var(--gray-50);
}

.quantity-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  font-size: 0.9rem;
  text-align: center;
}

.quantity-input:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 2px rgba(44, 85, 48, 0.1);
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.complete-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.complete-btn:hover {
  background: #059669;
}

/* Assign Form */
.assign-form {
  background: var(--gray-50);
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  border-left: 4px solid var(--primary-green);
}

.assign-form h3 {
  color: var(--gray-800);
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.form-group select,
.form-group input {
  padding: 12px;
  border: 2px solid var(--gray-200);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(44, 85, 48, 0.1);
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.assign-btn,
.cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.assign-btn {
  background: var(--primary-green);
  color: white;
}

.assign-btn:hover {
  background: var(--primary-green-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.cancel-btn {
  background: var(--gray-200);
  color: var(--gray-700);
}

.cancel-btn:hover {
  background: var(--gray-300);
}

/* Available Artisans */
.available-artisans {
  margin-top: 30px;
}

.available-artisans h3 {
  color: var(--gray-800);
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.artisans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.artisan-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  text-align: center;
  transition: transform 0.3s ease;
  border: 2px solid transparent;
}

.artisan-card.available {
  border-color: #10b981;
}

.artisan-card.unavailable {
  border-color: var(--gray-300);
  opacity: 0.7;
}

.artisan-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.artisan-card h4 {
  color: var(--gray-800);
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.artisan-card p {
  color: var(--gray-600);
  margin-bottom: 12px;
  font-size: 0.9rem;
}

.availability-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.availability-badge.available {
  background: #ecfdf5;
  color: #10b981;
}

.availability-badge.unavailable {
  background: #fef2f2;
  color: #ef4444;
}

/* Responsive Design */
@media (max-width: 768px) {
  .artisan-assignment {
    padding: 15px;
  }
  
  .assignment-header h1 {
    font-size: 2rem;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .assignment-tabs {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .tab-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .orders-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-content {
    padding: 20px;
  }
  
  .assignments-table {
    font-size: 0.8rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .assign-btn,
  .cancel-btn {
    width: 100%;
  }
}
