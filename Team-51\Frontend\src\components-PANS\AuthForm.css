.auth-modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(30, 41, 59, 0.18);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-modal-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 32px 0 rgba(59,130,246,0.12), 0 1.5px 6px 0 rgba(0,0,0,0.06);
  padding: 38px 32px 32px 32px;
  min-width: 340px;
  max-width: 380px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: authModalIn 0.18s cubic-bezier(.4,2,.6,1) 1;
}

@keyframes authModalIn {
  0% { transform: scale(0.95) translateY(30px); opacity: 0; }
  100% { transform: scale(1) translateY(0); opacity: 1; }
}

.auth-modal-close {
  position: absolute;
  top: 14px;
  right: 18px;
  background: none;
  border: none;
  font-size: 1.7rem;
  color: #b0b8c9;
  cursor: pointer;
  transition: color 0.15s;
  z-index: 2;
}
.auth-modal-close:hover {
  color: #ef4444;
}

.auth-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 18px;
  color: #222;
  letter-spacing: 0.5px;
}

.auth-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.auth-input-group {
  display: flex;
  align-items: center;
  background: #f3f6fa;
  border-radius: 8px;
  padding: 0 12px;
  margin-bottom: 0;
  border: 1.5px solid #e0e7ef;
  transition: border 0.2s;
}
.auth-input-group:focus-within {
  border: 1.5px solid #3b82f6;
}
.auth-input-icon {
  font-size: 1.2rem;
  color: #3b82f6;
  margin-right: 8px;
}
.auth-input {
  border: none;
  outline: none;
  background: transparent;
  font-size: 1rem;
  padding: 12px 0;
  width: 100%;
  color: #222;
}

.auth-btn {
  width: 100%;
  padding: 12px 0;
  border-radius: 8px;
  border: none;
  font-size: 1.08rem;
  font-weight: 600;
  background: #3b82f6;
  color: #fff;
  margin-top: 6px;
  margin-bottom: 2px;
  box-shadow: 0 2px 8px 0 rgba(59,130,246,0.08);
  cursor: pointer;
  transition: background 0.18s;
}
.auth-btn:hover {
  background: #2563eb;
}

.auth-switch {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 0.98rem;
  margin: 10px 0 0 0;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.15s;
}
.auth-switch:hover {
  color: #1d4ed8;
}

.auth-divider {
  width: 100%;
  text-align: center;
  margin: 18px 0 10px 0;
  position: relative;
}
.auth-divider span {
  background: #fff;
  color: #b0b8c9;
  font-size: 0.95rem;
  padding: 0 12px;
  position: relative;
  z-index: 1;
}
.auth-divider:before {
  content: '';
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background: #e0e7ef;
  z-index: 0;
}

.auth-google {
  display: flex;
  justify-content: center;
  margin-top: 2px;
}

.auth-alert {
  width: 100%;
  padding: 10px 0;
  border-radius: 7px;
  font-size: 0.98rem;
  margin-bottom: 10px;
  text-align: center;
}
.auth-alert.error {
  background: #fee2e2;
  color: #b91c1c;
}
.auth-alert.success {
  background: #d1fae5;
  color: #047857;
}

@media (max-width: 500px) {
  .auth-modal-card {
    min-width: 90vw;
    padding: 24px 6vw 18px 6vw;
  }
} 