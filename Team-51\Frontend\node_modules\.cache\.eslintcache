[{"C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js": "3", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js": "4", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js": "5", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js": "7", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js": "9", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\AgreementPDF.js": "11"}, {"size": 394, "mtime": 1752339457265, "results": "12", "hashOfConfig": "13"}, {"size": 3172, "mtime": 1752360348170, "results": "14", "hashOfConfig": "13"}, {"size": 3376, "mtime": 1752339457260, "results": "15", "hashOfConfig": "13"}, {"size": 2467, "mtime": 1752339457264, "results": "16", "hashOfConfig": "13"}, {"size": 3568, "mtime": 1752339457263, "results": "17", "hashOfConfig": "13"}, {"size": 2870, "mtime": 1752349920510, "results": "18", "hashOfConfig": "13"}, {"size": 5668, "mtime": 1752349920507, "results": "19", "hashOfConfig": "13"}, {"size": 20841, "mtime": 1752360090597, "results": "20", "hashOfConfig": "13"}, {"size": 17456, "mtime": 1752347599041, "results": "21", "hashOfConfig": "13"}, {"size": 2770, "mtime": 1752349920512, "results": "22", "hashOfConfig": "13"}, {"size": 15094, "mtime": 1752360024426, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yx484d", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js", ["57", "58", "59", "60"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js", ["61", "62"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\AgreementPDF.js", [], [], {"ruleId": "63", "severity": 1, "message": "64", "line": 15, "column": 15, "nodeType": "65", "endLine": 15, "endColumn": 58}, {"ruleId": "63", "severity": 1, "message": "64", "line": 18, "column": 15, "nodeType": "65", "endLine": 18, "endColumn": 58}, {"ruleId": "63", "severity": 1, "message": "64", "line": 21, "column": 15, "nodeType": "65", "endLine": 21, "endColumn": 58}, {"ruleId": "63", "severity": 1, "message": "64", "line": 24, "column": 15, "nodeType": "65", "endLine": 24, "endColumn": 58}, {"ruleId": "66", "severity": 1, "message": "67", "line": 1, "column": 27, "nodeType": "68", "messageId": "69", "endLine": 1, "endColumn": 36}, {"ruleId": "66", "severity": 1, "message": "70", "line": 150, "column": 10, "nodeType": "68", "messageId": "69", "endLine": 150, "endColumn": 24}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'showAssignForm' is assigned a value but never used."]