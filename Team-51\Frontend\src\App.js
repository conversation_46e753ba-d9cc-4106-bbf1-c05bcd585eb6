import React, { useState } from 'react';
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import Hero from './components/pages/Hero';
import About from './components/pages/About';
import AuthForm from './components/common/AuthForm';
import TLDashboard from './components/dashboard/TLDashboard';
import { GoogleOAuthProvider } from '@react-oauth/google';
import './styles/globals.css';

function App() {
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState('signin'); // 'signin' or 'signup'
  const [showDashboard, setShowDashboard] = useState(false);

  // Pass these handlers to Header
  const handleSignIn = () => {
    setAuthMode('signin');
    setAuthModalOpen(true);
  };
  
  const handleSignUp = () => {
    setAuthMode('signup');
    setAuthModalOpen(true);
  };

  const handleCloseModal = () => setAuthModalOpen(false);

  return (
    <GoogleOAuthProvider clientId="###">
      <div className="App">
        <Header onSignIn={handleSignIn} onSignUp={handleSignUp} />

        {/* Dashboard Toggle Button */}
        <div style={{ textAlign: 'center', padding: '20px', background: '#f8fafc' }}>
          <button
            onClick={() => setShowDashboard(!showDashboard)}
            style={{
              padding: '12px 24px',
              backgroundColor: '#2c5530',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}
          >
            {showDashboard ? 'Show Main Site' : 'Show Team Leader Dashboard'}
          </button>
        </div>

        {showDashboard ? (
          <TLDashboard />
        ) : (
          <main>
            <Hero />
            <About />
          </main>
        )}

        <Footer />
        <AuthForm isOpen={authModalOpen} onClose={handleCloseModal} initialMode={authMode} />
      </div>
    </GoogleOAuthProvider>
  );
}

export default App;
