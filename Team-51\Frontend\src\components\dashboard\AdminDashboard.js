import React, { useState, useEffect } from 'react';
import './AdminDashboard.css';
import api from '../../services/api';

function AdminDashboard() {
  const [orders, setOrders] = useState([]);
  const [availableTeamLeaders, setAvailableTeamLeaders] = useState([]);
  const [allTeamLeaders, setAllTeamLeaders] = useState([]);
  const [showAddOrderForm, setShowAddOrderForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [expandedOrders, setExpandedOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Form state
  const [orderForm, setOrderForm] = useState({
    order_id: '',
    product_id: '',
    product_name: '',
    buyer_name: '',
    team_leads: [''],
    quantities: [0],
    date_ordered: '',
    deadline: '',
    status: 'in progress',
    remark: ''
  });

  // Fetch orders and team leaders on component mount
  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('token');
    const username = localStorage.getItem('username');
    console.log('Auth check - Token:', !!token, 'Username:', username);
    
    if (!token || !username) {
      setError('Please log in to access the admin dashboard');
      return;
    }
    
    fetchOrders();
    fetchTeamLeaders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await api.get('/orders');
      setOrders(response.data);
    } catch (error) {
      setError('Failed to fetch orders');
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTeamLeaders = async () => {
    try {
      console.log('Fetching team leaders...');
      
      // First try the authenticated endpoints
      try {
        const [availableResponse, allResponse] = await Promise.all([
          api.get('/orders/available-team-leaders'),
          api.get('/orders/team-leaders')
        ]);
        console.log('Available team leaders:', availableResponse.data);
        console.log('All team leaders:', allResponse.data);
        setAvailableTeamLeaders(availableResponse.data);
        setAllTeamLeaders(allResponse.data);
      } catch (authError) {
        console.error('Authenticated endpoints failed:', authError.response?.data);
        
        // Fallback to test endpoint
        console.log('Trying test endpoint...');
        const testResponse = await api.get('/orders/team-leaders-test');
        console.log('Test team leaders:', testResponse.data);
        
        // Transform the data to match expected format
        const transformedData = testResponse.data.map(tl => ({
          ...tl,
          current_order_count: 0,
          total_order_count: 0
        }));
        
        setAvailableTeamLeaders(transformedData);
        setAllTeamLeaders(transformedData);
      }
    } catch (error) {
      console.error('Error fetching team leaders:', error);
      console.error('Error details:', error.response?.data);
      setError('Failed to fetch team leaders. Please check your connection.');
    }
  };

  const handleAddTeamLead = () => {
    setOrderForm(prev => ({
      ...prev,
      team_leads: [...prev.team_leads, ''],
      quantities: [...prev.quantities, 0]
    }));
  };

  const handleRemoveTeamLead = (index) => {
    if (orderForm.team_leads.length > 1) {
      setOrderForm(prev => ({
        ...prev,
        team_leads: prev.team_leads.filter((_, i) => i !== index),
        quantities: prev.quantities.filter((_, i) => i !== index)
      }));
    }
  };

  const handleTeamLeadChange = (index, value) => {
    setOrderForm(prev => ({
      ...prev,
      team_leads: prev.team_leads.map((tl, i) => i === index ? value : tl)
    }));
  };

  const handleQuantityChange = (index, value) => {
    setOrderForm(prev => ({
      ...prev,
      quantities: prev.quantities.map((qty, i) => i === index ? parseInt(value) || 0 : qty)
    }));
  };

  const handleFormChange = (field, value) => {
    setOrderForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmitOrder = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!orderForm.order_id || !orderForm.product_id || !orderForm.date_ordered || !orderForm.deadline) {
      setError('Please fill in all required fields');
      return;
    }

    if (orderForm.team_leads.some(tl => !tl) || orderForm.quantities.some(qty => qty <= 0)) {
      setError('Please fill in all team leads and quantities');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const response = await api.post('/orders', orderForm);
      setSuccess('Order created successfully!');
      setShowAddOrderForm(false);
      setOrderForm({
        order_id: '',
        product_id: '',
        product_name: '',
        buyer_name: '',
        team_leads: [''],
        quantities: [0],
        date_ordered: '',
        deadline: '',
        status: 'in progress',
        remark: ''
      });
      
      // Refresh orders list and team leaders
      fetchOrders();
      fetchTeamLeaders();
    } catch (error) {
      setError(error.response?.data?.msg || 'Failed to create order');
      console.error('Error creating order:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'to be done': return '#6b7280';
      case 'in progress': return '#3b82f6';
      case 'completed': return '#10b981';
      case 'delayed': return '#ef4444';
      default: return '#6b7280';
    }
  };

  // Test API connection
  const testAPI = async () => {
    try {
      console.log('Testing API connection...');
      const response = await api.get('/orders/test');
      console.log('API test response:', response.data);
      setSuccess('API connection successful!');
    } catch (error) {
      console.error('API test failed:', error);
      setError('API connection failed: ' + error.message);
    }
  };

  // Toggle order details expansion
  const toggleOrderDetails = (orderId) => {
    setExpandedOrders(prev => 
      prev.includes(orderId) 
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  // Show detailed order modal
  const showOrderDetailsModal = (order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <h1>Admin Dashboard</h1>
        <p>Manage orders and team leaders</p>
      </div>

      <div className="dashboard-actions">
        <button 
          className="add-order-btn"
          onClick={() => setShowAddOrderForm(true)}
        >
          + Add New Order
        </button>
        <button 
          className="test-btn"
          onClick={testAPI}
          style={{ marginLeft: '10px', background: '#6b7280' }}
        >
          Test API
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError('')}>×</button>
        </div>
      )}

      {success && (
        <div className="success-message">
          {success}
          <button onClick={() => setSuccess('')}>×</button>
        </div>
      )}

      {/* Add Order Form Modal */}
      {showAddOrderForm && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Add New Order</h2>
              <button 
                className="close-btn"
                onClick={() => setShowAddOrderForm(false)}
              >
                ×
              </button>
            </div>

            <form onSubmit={handleSubmitOrder} className="order-form">
              <div className="form-row">
                <div className="form-group">
                  <label>Order ID *</label>
                  <input
                    type="text"
                    value={orderForm.order_id}
                    onChange={(e) => handleFormChange('order_id', e.target.value)}
                    placeholder="e.g., ORD001"
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Product ID *</label>
                  <input
                    type="text"
                    value={orderForm.product_id}
                    onChange={(e) => handleFormChange('product_id', e.target.value)}
                    placeholder="e.g., PROD001"
                    required
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Product Name *</label>
                  <input
                    type="text"
                    value={orderForm.product_name}
                    onChange={(e) => handleFormChange('product_name', e.target.value)}
                    placeholder="e.g., Cotton T-Shirt"
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Buyer Name *</label>
                  <input
                    type="text"
                    value={orderForm.buyer_name}
                    onChange={(e) => handleFormChange('buyer_name', e.target.value)}
                    placeholder="e.g., John Doe"
                    required
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Date Ordered *</label>
                  <input
                    type="datetime-local"
                    value={orderForm.date_ordered}
                    onChange={(e) => handleFormChange('date_ordered', e.target.value)}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Deadline *</label>
                  <input
                    type="datetime-local"
                    value={orderForm.deadline}
                    onChange={(e) => handleFormChange('deadline', e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="form-group">
                <label>Status</label>
                <select
                  value={orderForm.status}
                  onChange={(e) => handleFormChange('status', e.target.value)}
                >
                  <option value="to be done">To Be Done</option>
                  <option value="in progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="delayed">Delayed</option>
                </select>
              </div>

              <div className="form-group">
                <label>Remark</label>
                <textarea
                  value={orderForm.remark}
                  onChange={(e) => handleFormChange('remark', e.target.value)}
                  placeholder="Additional notes..."
                  rows="3"
                />
              </div>

              <div className="team-leads-section">
                <div className="section-header">
                  <h3>Team Leaders & Quantities</h3>
                  <button 
                    type="button"
                    className="add-team-btn"
                    onClick={handleAddTeamLead}
                  >
                    + Add Team Leader
                  </button>
                </div>

                                 {orderForm.team_leads.map((teamLead, index) => (
                   <div key={index} className="team-lead-row">
                     <div className="form-group">
                       <label>Team Leader</label>
                       <select
                         value={teamLead}
                         onChange={(e) => handleTeamLeadChange(index, e.target.value)}
                         required
                       >
                         <option value="">Select Team Leader</option>
                         {allTeamLeaders.length > 0 ? (
                           allTeamLeaders.map((tl) => (
                             <option key={tl.tl_id} value={tl.tl_id}>
                               {tl.tl_id} - {tl.name || 'Unknown'} ({tl.village_name || 'Unknown Village'}) - Current: {tl.current_order_count || 0}, Total: {tl.total_order_count || 0}
                             </option>
                           ))
                         ) : (
                           <option value="" disabled>No team leaders available - Check console for errors</option>
                         )}
                       </select>
                     </div>
                     <div className="form-group">
                       <label>Quantity</label>
                       <input
                         type="number"
                         value={orderForm.quantities[index]}
                         onChange={(e) => handleQuantityChange(index, e.target.value)}
                         placeholder="0"
                         min="1"
                         required
                       />
                     </div>
                     {orderForm.team_leads.length > 1 && (
                       <button
                         type="button"
                         className="remove-btn"
                         onClick={() => handleRemoveTeamLead(index)}
                       >
                         Remove
                       </button>
                     )}
                   </div>
                 ))}
              </div>

              <div className="form-actions">
                <button 
                  type="button" 
                  className="cancel-btn"
                  onClick={() => setShowAddOrderForm(false)}
                >
                  Cancel
                </button>
                <button 
                  type="submit" 
                  className="submit-btn"
                  disabled={loading}
                >
                  {loading ? 'Creating...' : 'Create Order'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Team Leaders Section */}
      <div className="team-leaders-section">
        <h2>Team Leaders Status ({allTeamLeaders.length} found)</h2>
        <div className="team-leaders-grid">
          {allTeamLeaders.length > 0 ? (
            allTeamLeaders.map((tl) => (
            <div key={tl.tl_id} className="team-leader-card">
              <div className="tl-header">
                <h3>{tl.name}</h3>
                <span className="tl-id">{tl.tl_id}</span>
              </div>
              <div className="tl-details">
                <p><strong>Village:</strong> {tl.village_name}</p>
                <p><strong>Cluster:</strong> {tl.cluster_id}</p>
                <p><strong>Current Orders:</strong> 
                  <span className="order-count">{tl.current_order_count || 0}</span>
                </p>
                <p><strong>Total Orders:</strong> 
                  <span className="order-count">{tl.total_order_count || 0}</span>
                </p>
                <p><strong>Current Order:</strong> 
                  {tl.current_orderid ? (
                    <span className="assigned-order">{tl.current_orderid}</span>
                  ) : (
                    <span className="available-status">Available</span>
                  )}
                </p>
              </div>
            </div>
          ))
          ) : (
            <div className="no-team-leaders">
              <p>No team leaders found. Please check the database connection.</p>
            </div>
          )}
        </div>
        

      </div>

      {/* Orders List */}
      <div className="orders-section">
        <h2>Orders ({orders.length})</h2>
        
        {loading ? (
          <div className="loading">Loading orders...</div>
        ) : (
          <div className="orders-table-container">
            <table className="orders-table">
              <thead>
                <tr>
                  <th>Buyer Name</th>
                  <th>Product Name</th>
                  <th>Status</th>
                  <th>Quantity</th>
                  <th>Team Leaders</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {orders.map((order) => (
                  <React.Fragment key={order._id}>
                    <tr className="order-row">
                      <td className="buyer-name">{order.buyer_name || 'N/A'}</td>
                      <td className="product-name">{order.product_name || order.product_id}</td>
                      <td>
                        <span 
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(order.status) }}
                        >
                          {order.status}
                        </span>
                      </td>
                      <td>{order.total_qty}</td>
                      <td className="team-leads-cell">
                        {order.team_leaders_details ? (
                          order.team_leaders_details.map(tl => tl.name || tl.tl_id).join(', ')
                        ) : (
                          order.team_leads.join(', ')
                        )}
                      </td>
                                          <td>
                      <button 
                        className="view-details-btn"
                        onClick={() => showOrderDetailsModal(order)}
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className="modal-overlay">
          <div className="modal-content order-details-modal">
            <div className="modal-header">
              <h2>Order Details - {selectedOrder.order_id}</h2>
              <button 
                className="close-btn"
                onClick={() => setShowOrderDetails(false)}
              >
                ×
              </button>
            </div>

            <div className="order-details-content">
              {/* Three Main Cards */}
              <div className="main-cards">
                {/* Order Information Card */}
                <div className="detail-card">
                  <h3>Order Information</h3>
                  <div className="card-content">
                    <div className="info-row">
                      <span className="label">Order ID:</span>
                      <span className="value">{selectedOrder.order_id}</span>
                    </div>
                    <div className="info-row">
                      <span className="label">Product ID:</span>
                      <span className="value">{selectedOrder.product_id}</span>
                    </div>
                    <div className="info-row">
                      <span className="label">Product Name:</span>
                      <span className="value">{selectedOrder.product_name}</span>
                    </div>
                    <div className="info-row">
                      <span className="label">Status:</span>
                      <span 
                        className="value status-badge"
                        style={{ backgroundColor: getStatusColor(selectedOrder.status) }}
                      >
                        {selectedOrder.status}
                      </span>
                    </div>
                    <div className="info-row">
                      <span className="label">Total Quantity:</span>
                      <span className="value">{selectedOrder.total_qty}</span>
                    </div>
                    <div className="info-row">
                      <span className="label">Date Ordered:</span>
                      <span className="value">{new Date(selectedOrder.date_ordered).toLocaleDateString()}</span>
                    </div>
                    <div className="info-row">
                      <span className="label">Deadline:</span>
                      <span className="value">{new Date(selectedOrder.deadline).toLocaleDateString()}</span>
                    </div>
                    <div className="info-row">
                      <span className="label">Remark:</span>
                      <span className="value">{selectedOrder.remark || 'No remark'}</span>
                    </div>
                  </div>
                </div>

                {/* Buyer Information Card */}
                <div className="detail-card">
                  <h3>Buyer Information</h3>
                  <div className="card-content">
                    {selectedOrder.buyer ? (
                      <>
                        <div className="info-row">
                          <span className="label">Name:</span>
                          <span className="value">{selectedOrder.buyer.name}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Email:</span>
                          <span className="value">{selectedOrder.buyer.email}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Phone:</span>
                          <span className="value">{selectedOrder.buyer.phone}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Delivery Method:</span>
                          <span className="value">{selectedOrder.buyer.delivery_method}</span>
                        </div>
                      </>
                    ) : (
                      <div className="info-row">
                        <span className="value">Buyer information not available</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Team Leaders Card */}
                <div className="detail-card">
                  <h3>Team Leaders & Quantities</h3>
                  <div className="card-content">
                    {selectedOrder.team_leaders_details ? (
                      selectedOrder.team_leaders_details.map((teamLead, index) => (
                        <div key={index} className="team-lead-info">
                          <div className="info-row">
                            <span className="label">Team Leader:</span>
                            <span className="value">
                              {teamLead.name || 'Unknown'}
                              {teamLead.village_name && ` - ${teamLead.village_name}`}
                            </span>
                          </div>
                          <div className="info-row">
                            <span className="label">Quantity:</span>
                            <span className="value">{selectedOrder.quantities[index]} units</span>
                          </div>
                          {index < selectedOrder.team_leaders_details.length - 1 && <hr />}
                        </div>
                      ))
                    ) : (
                      selectedOrder.team_leads.map((teamLead, index) => (
                        <div key={index} className="team-lead-info">
                          <div className="info-row">
                            <span className="label">Team Leader:</span>
                            <span className="value">{teamLead}</span>
                          </div>
                          <div className="info-row">
                            <span className="label">Quantity:</span>
                            <span className="value">{selectedOrder.quantities[index]} units</span>
                          </div>
                          {index < selectedOrder.team_leads.length - 1 && <hr />}
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>

              {/* Address Information */}
              {selectedOrder.buyer && (
                <div className="address-section">
                  <div className="address-card">
                    <h3>Shipping Address</h3>
                    <div className="address-content">
                      <p>{selectedOrder.buyer.shipping_address.street}</p>
                      <p>{selectedOrder.buyer.shipping_address.city}, {selectedOrder.buyer.shipping_address.state} {selectedOrder.buyer.shipping_address.zip_code}</p>
                      <p>{selectedOrder.buyer.shipping_address.country}</p>
                    </div>
                  </div>

                  <div className="address-card">
                    <h3>Billing Address</h3>
                    <div className="address-content">
                      <p>{selectedOrder.buyer.billing_address.street}</p>
                      <p>{selectedOrder.buyer.billing_address.city}, {selectedOrder.buyer.billing_address.state} {selectedOrder.buyer.billing_address.zip_code}</p>
                      <p>{selectedOrder.buyer.billing_address.country}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Progress Updates */}
              {selectedOrder.progress_updates && selectedOrder.progress_updates.length > 0 && (
                <div className="progress-section">
                  <h3>Progress Updates</h3>
                  <div className="progress-updates">
                    {selectedOrder.progress_updates.map((update, index) => (
                      <div key={index} className="progress-update">
                        <div className="update-header">
                          <span className="team-leader">
                            {selectedOrder.team_leaders_details ? 
                              (selectedOrder.team_leaders_details.find(tl => tl.tl_id === update.team_leader)?.name || update.team_leader) 
                              : update.team_leader}
                          </span>
                          <span className="update-date">{new Date(update.updated_at).toLocaleDateString()}</span>
                        </div>
                        <div className="update-details">
                          <span className="status">{update.status}</span>
                          {update.fault_products > 0 && (
                            <span className="fault-products">Fault Products: {update.fault_products}</span>
                          )}
                          {update.remarks && <p className="remarks">{update.remarks}</p>}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default AdminDashboard; 