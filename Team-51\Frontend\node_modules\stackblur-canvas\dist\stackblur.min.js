!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).StackBlur={})}(this,(function(t){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],n=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function a(t,r,n,a,o){if("string"==typeof t&&(t=document.getElementById(t)),!t||"object"!==e(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var i=t.getContext("2d");try{return i.getImageData(r,n,a,o)}catch(t){throw new Error("unable to access image data: "+t)}}function o(t,e,r,n,o,f){if(!(isNaN(f)||f<1)){f|=0;var g=a(t,e,r,n,o);g=i(g,e,r,n,o,f),t.getContext("2d").putImageData(g,e,r)}}function i(t,e,a,o,i,f){for(var g,c=t.data,u=2*f+1,s=o-1,v=i-1,b=f+1,x=b*(b+1)/2,d=new l,p=d,y=1;y<u;y++)p=p.next=new l,y===b&&(g=p);p.next=d;for(var h=null,m=null,w=0,B=0,C=r[f],I=n[f],S=0;S<i;S++){p=d;for(var E=c[B],N=c[B+1],R=c[B+2],j=c[B+3],D=0;D<b;D++)p.r=E,p.g=N,p.b=R,p.a=j,p=p.next;for(var G=0,T=0,A=0,H=0,O=b*E,W=b*N,k=b*R,M=b*j,_=x*E,L=x*N,P=x*R,q=x*j,z=1;z<b;z++){var F=B+((s<z?s:z)<<2),J=c[F],K=c[F+1],Q=c[F+2],U=c[F+3],V=b-z;_+=(p.r=J)*V,L+=(p.g=K)*V,P+=(p.b=Q)*V,q+=(p.a=U)*V,G+=J,T+=K,A+=Q,H+=U,p=p.next}h=d,m=g;for(var X=0;X<o;X++){var Y=q*C>>>I;if(c[B+3]=Y,0!==Y){var Z=255/Y;c[B]=(_*C>>>I)*Z,c[B+1]=(L*C>>>I)*Z,c[B+2]=(P*C>>>I)*Z}else c[B]=c[B+1]=c[B+2]=0;_-=O,L-=W,P-=k,q-=M,O-=h.r,W-=h.g,k-=h.b,M-=h.a;var $=X+f+1;$=w+($<s?$:s)<<2,_+=G+=h.r=c[$],L+=T+=h.g=c[$+1],P+=A+=h.b=c[$+2],q+=H+=h.a=c[$+3],h=h.next;var tt=m,et=tt.r,rt=tt.g,nt=tt.b,at=tt.a;O+=et,W+=rt,k+=nt,M+=at,G-=et,T-=rt,A-=nt,H-=at,m=m.next,B+=4}w+=o}for(var ot=0;ot<o;ot++){var it=c[B=ot<<2],ft=c[B+1],gt=c[B+2],lt=c[B+3],ct=b*it,ut=b*ft,st=b*gt,vt=b*lt,bt=x*it,xt=x*ft,dt=x*gt,pt=x*lt;p=d;for(var yt=0;yt<b;yt++)p.r=it,p.g=ft,p.b=gt,p.a=lt,p=p.next;for(var ht=o,mt=0,wt=0,Bt=0,Ct=0,It=1;It<=f;It++){B=ht+ot<<2;var St=b-It;bt+=(p.r=it=c[B])*St,xt+=(p.g=ft=c[B+1])*St,dt+=(p.b=gt=c[B+2])*St,pt+=(p.a=lt=c[B+3])*St,Ct+=it,mt+=ft,wt+=gt,Bt+=lt,p=p.next,It<v&&(ht+=o)}B=ot,h=d,m=g;for(var Et=0;Et<i;Et++){var Nt=B<<2;c[Nt+3]=lt=pt*C>>>I,lt>0?(lt=255/lt,c[Nt]=(bt*C>>>I)*lt,c[Nt+1]=(xt*C>>>I)*lt,c[Nt+2]=(dt*C>>>I)*lt):c[Nt]=c[Nt+1]=c[Nt+2]=0,bt-=ct,xt-=ut,dt-=st,pt-=vt,ct-=h.r,ut-=h.g,st-=h.b,vt-=h.a,Nt=ot+((Nt=Et+b)<v?Nt:v)*o<<2,bt+=Ct+=h.r=c[Nt],xt+=mt+=h.g=c[Nt+1],dt+=wt+=h.b=c[Nt+2],pt+=Bt+=h.a=c[Nt+3],h=h.next,ct+=it=m.r,ut+=ft=m.g,st+=gt=m.b,vt+=lt=m.a,Ct-=it,mt-=ft,wt-=gt,Bt-=lt,m=m.next,B+=o}}return t}function f(t,e,r,n,o,i){if(!(isNaN(i)||i<1)){i|=0;var f=a(t,e,r,n,o);f=g(f,e,r,n,o,i),t.getContext("2d").putImageData(f,e,r)}}function g(t,e,a,o,i,f){for(var g,c=t.data,u=2*f+1,s=o-1,v=i-1,b=f+1,x=b*(b+1)/2,d=new l,p=d,y=1;y<u;y++)p=p.next=new l,y===b&&(g=p);p.next=d;for(var h,m,w=null,B=null,C=r[f],I=n[f],S=0,E=0,N=0;N<i;N++){var R=c[E],j=c[E+1],D=c[E+2],G=b*R,T=b*j,A=b*D,H=x*R,O=x*j,W=x*D;p=d;for(var k=0;k<b;k++)p.r=R,p.g=j,p.b=D,p=p.next;for(var M=0,_=0,L=0,P=1;P<b;P++)h=E+((s<P?s:P)<<2),H+=(p.r=R=c[h])*(m=b-P),O+=(p.g=j=c[h+1])*m,W+=(p.b=D=c[h+2])*m,M+=R,_+=j,L+=D,p=p.next;w=d,B=g;for(var q=0;q<o;q++)c[E]=H*C>>>I,c[E+1]=O*C>>>I,c[E+2]=W*C>>>I,H-=G,O-=T,W-=A,G-=w.r,T-=w.g,A-=w.b,h=S+((h=q+f+1)<s?h:s)<<2,H+=M+=w.r=c[h],O+=_+=w.g=c[h+1],W+=L+=w.b=c[h+2],w=w.next,G+=R=B.r,T+=j=B.g,A+=D=B.b,M-=R,_-=j,L-=D,B=B.next,E+=4;S+=o}for(var z=0;z<o;z++){var F=c[E=z<<2],J=c[E+1],K=c[E+2],Q=b*F,U=b*J,V=b*K,X=x*F,Y=x*J,Z=x*K;p=d;for(var $=0;$<b;$++)p.r=F,p.g=J,p.b=K,p=p.next;for(var tt=0,et=0,rt=0,nt=1,at=o;nt<=f;nt++)E=at+z<<2,X+=(p.r=F=c[E])*(m=b-nt),Y+=(p.g=J=c[E+1])*m,Z+=(p.b=K=c[E+2])*m,tt+=F,et+=J,rt+=K,p=p.next,nt<v&&(at+=o);E=z,w=d,B=g;for(var ot=0;ot<i;ot++)c[h=E<<2]=X*C>>>I,c[h+1]=Y*C>>>I,c[h+2]=Z*C>>>I,X-=Q,Y-=U,Z-=V,Q-=w.r,U-=w.g,V-=w.b,h=z+((h=ot+b)<v?h:v)*o<<2,X+=tt+=w.r=c[h],Y+=et+=w.g=c[h+1],Z+=rt+=w.b=c[h+2],w=w.next,Q+=F=B.r,U+=J=B.g,V+=K=B.b,tt-=F,et-=J,rt-=K,B=B.next,E+=o}return t}var l=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};t.BlurStack=l,t.canvasRGB=f,t.canvasRGBA=o,t.image=function(t,e,r,n,a,i){if("string"==typeof t&&(t=document.getElementById(t)),t&&("HTMLImageElement"!==Object.prototype.toString.call(t).slice(8,-1)||"naturalWidth"in t)){var g=a?"offset":"natural",l=t[g+"Width"],c=t[g+"Height"];if("ImageBitmap"===Object.prototype.toString.call(t).slice(8,-1)&&(l=t.width,c=t.height),"string"==typeof e&&(e=document.getElementById(e)),e&&"getContext"in e){i||(e.style.width=l+"px",e.style.height=c+"px"),e.width=l,e.height=c;var u=e.getContext("2d");u.clearRect(0,0,l,c),u.drawImage(t,0,0,t.naturalWidth,t.naturalHeight,0,0,l,c),isNaN(r)||r<1||(n?o(e,0,0,l,c,r):f(e,0,0,l,c,r))}}},t.imageDataRGB=g,t.imageDataRGBA=i,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=stackblur.min.js.map
