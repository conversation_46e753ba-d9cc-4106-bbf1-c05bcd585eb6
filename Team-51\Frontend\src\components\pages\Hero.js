import React, { useState, useEffect } from 'react';
import './Hero.css';

function Hero() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const principleImages = [
    { src: '/landing4.jpeg', alt: 'PANS NGO Principle 1' },
    { src: '/landing5.jpg', alt: 'PANS NGO Principle 2' },
    { src: '/landing6.jpg', alt: 'PANS NGO Principle 3' }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % principleImages.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [principleImages.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % principleImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + principleImages.length) % principleImages.length);
  };

  return (
    <section className="hero" id="home">
      {/* Background Image with Overlay */}
      <div 
        className="hero-background"
        style={{
          backgroundImage: `url(${process.env.PUBLIC_URL}/landing3.jpeg)`
        }}
      ></div>
      <div className="hero-overlay"></div>
      
      <div className="hero-container">
        {/* Hero Header Section */}
        <div className="hero-header">
          <div className="hero-badge">
            <span className="hero-badge-icon">🌟</span>
            <span>Transforming Lives Since 1996</span>
          </div>
          
          <h1 className="hero-title">
            Empowering Communities
            <span className="hero-subtitle">Across Rajasthan</span>
          </h1>
          
          <p className="hero-description">
            People's Awareness Network Society (PANS) is dedicated to the social, economic, and educational upliftment of marginalized families. We cultivate an ecosystem empowering youth, children, and women to access quality life and education.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="hero-main-grid">
          {/* Left Column - Mission & Actions */}
          <div className="hero-left-column">
            <div className="mission-section">
              <h3 className="mission-title">Our Core Focus Areas</h3>
              <div className="mission-cards">
                <div className="mission-card">
                  <div className="mission-icon">🎯</div>
                  <div className="mission-content">
                    <h4>Youth Leadership Development</h4>
                    <p>Empowering the next generation of leaders</p>
                  </div>
                </div>
                <div className="mission-card">
                  <div className="mission-icon">👩‍💼</div>
                  <div className="mission-content">
                    <h4>Women Empowerment</h4>
                    <p>Creating equal opportunities for women</p>
                  </div>
                </div>
                <div className="mission-card">
                  <div className="mission-icon">🌱</div>
                  <div className="mission-content">
                    <h4>Sustainable Rural Development</h4>
                    <p>Building resilient communities</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="hero-actions">
              <button className="hero-btn hero-btn-primary">
                <i className="fas fa-heart"></i>
                Support Our Cause
              </button>
              <button className="hero-btn hero-btn-secondary">
                <i className="fas fa-info-circle"></i>
                Learn More
              </button>
            </div>
          </div>

          {/* Right Column - Carousel */}
          <div className="hero-right-column">
            <div className="principles-section">
              <div className="principles-header">
                <h3>Our Guiding Principles</h3>
                <p>Values that drive our mission to transform communities</p>
              </div>
              
              <div className="principles-carousel">
                <div className="carousel-wrapper">
                  <button className="carousel-btn carousel-btn-prev" onClick={prevSlide}>
                    <i className="fas fa-chevron-left"></i>
                  </button>
                  
                  <div className="carousel-slides">
                    {principleImages.map((image, index) => (
                      <div
                        key={index}
                        className={`carousel-slide ${index === currentSlide ? 'active' : ''}`}
                      >
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="principle-image"
                          onError={(e) => {
                            console.error(`Failed to load image: ${image.src}`);
                            e.target.style.display = 'none';
                          }}
                          onLoad={() => {
                            console.log(`Successfully loaded: ${image.src}`);
                          }}
                        />
                      </div>
                    ))}
                  </div>
                  
                  <button className="carousel-btn carousel-btn-next" onClick={nextSlide}>
                    <i className="fas fa-chevron-right"></i>
                  </button>
                </div>
                
                <div className="carousel-indicators">
                  {principleImages.map((_, index) => (
                    <button
                      key={index}
                      className={`indicator ${index === currentSlide ? 'active' : ''}`}
                      onClick={() => setCurrentSlide(index)}
                    ></button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Impact Statistics */}
        <div className="impact-stats">
          <div className="stats-container">
            <div className="stat-item">
              <div className="stat-number">400+</div>
              <div className="stat-label">Youth Empowered</div>
              <div className="stat-icon">👥</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">100</div>
              <div className="stat-label">Target Villages</div>
              <div className="stat-icon">🏘️</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">28+</div>
              <div className="stat-label">Years of Service</div>
              <div className="stat-icon">⏰</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">1000+</div>
              <div className="stat-label">Lives Transformed</div>
              <div className="stat-icon">🎓</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Hero;
