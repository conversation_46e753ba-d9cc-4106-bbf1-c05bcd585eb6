import React, { useState, useEffect } from 'react';
import api, { artisansAPI, ordersAPI } from '../services/api';
import './ArtisanAssignmentModal.css';

function ArtisanAssignmentModal({ 
  isOpen, 
  onClose, 
  order, 
  teamLeaderId, 
  onAssignmentComplete 
}) {
  const [artisans, setArtisans] = useState([]);
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Get team leader's assigned quantity for this order
  const getTeamLeaderQuantity = () => {
    if (!order || !teamLeaderId) return 0;
    const teamLeadIndex = order.team_leads.indexOf(teamLeaderId);
    return teamLeadIndex >= 0 ? order.quantities[teamLeadIndex] : 0;
  };

  const teamLeaderQuantity = getTeamLeaderQuantity();

  useEffect(() => {
    if (isOpen && teamLeaderId) {
      fetchArtisans();
      fetchExistingAssignments();
    }
  }, [isOpen, teamLeaderId]);

  const fetchArtisans = async () => {
    try {
      setLoading(true);
      const response = await artisansAPI.getByTeamLeader(teamLeaderId);
      setArtisans(response.data);
    } catch (error) {
      console.error('Error fetching artisans:', error);
      setError('Failed to fetch artisans');
    } finally {
      setLoading(false);
    }
  };

  const fetchExistingAssignments = async () => {
    try {
      const response = await ordersAPI.getArtisanAssignments(order._id, teamLeaderId);
      setAssignments(response.data.assignments || []);
    } catch (error) {
      console.error('Error fetching existing assignments:', error);
      // Don't show error for this as it might be the first assignment
    }
  };

  const handleAddArtisan = () => {
    setAssignments(prev => [...prev, {
      artisan_id: '',
      artisan_name: '',
      assigned_quantity: 0
    }]);
  };

  const handleRemoveArtisan = (index) => {
    setAssignments(prev => prev.filter((_, i) => i !== index));
  };

  const handleArtisanChange = (index, field, value) => {
    setAssignments(prev => prev.map((assignment, i) => {
      if (i === index) {
        return { ...assignment, [field]: value };
      }
      return assignment;
    }));
  };

  const handleArtisanSelect = (index, artisanId) => {
    const selectedArtisan = artisans.find(a => a.customer_id === artisanId);
    setAssignments(prev => prev.map((assignment, i) => {
      if (i === index) {
        return {
          ...assignment,
          artisan_id: artisanId,
          artisan_name: selectedArtisan ? selectedArtisan.name : ''
        };
      }
      return assignment;
    }));
  };

  const calculateTotalAssigned = () => {
    return assignments.reduce((sum, assignment) => sum + (assignment.assigned_quantity || 0), 0);
  };

  const getRemainingQuantity = () => {
    return teamLeaderQuantity - calculateTotalAssigned();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (assignments.length === 0) {
      setError('Please add at least one artisan assignment');
      return;
    }

    // Validate assignments
    const invalidAssignments = assignments.filter(a => 
      !a.artisan_id || !a.artisan_name || a.assigned_quantity <= 0
    );

    if (invalidAssignments.length > 0) {
      setError('Please fill in all fields for all artisans');
      return;
    }

    if (calculateTotalAssigned() > teamLeaderQuantity) {
      setError(`Total assigned quantity (${calculateTotalAssigned()}) exceeds your quantity (${teamLeaderQuantity})`);
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const assignmentData = {
        team_leader_id: teamLeaderId,
        artisan_assignments: assignments
      };

      await ordersAPI.assignArtisans(order._id, assignmentData);
      
      setSuccess('Artisan assignments updated successfully!');
      setTimeout(() => {
        onAssignmentComplete();
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Error assigning artisans:', error);
      setError(error.response?.data?.msg || 'Failed to assign artisans');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="artisan-assignment-modal-overlay">
      <div className="artisan-assignment-modal">
        <div className="modal-header">
          <h2>Assign Work to Artisans</h2>
          <button className="close-btn" onClick={onClose}>&times;</button>
        </div>

        <div className="modal-content">
          <div className="order-info">
            <h3>Order: {order?.order_id}</h3>
            <p>Product: {order?.product_name}</p>
            <p>Your Assigned Quantity: <strong>{teamLeaderQuantity} units</strong></p>
            <p>Remaining to Assign: <strong>{getRemainingQuantity()} units</strong></p>
          </div>

          {error && <div className="error-message">{error}</div>}
          {success && <div className="success-message">{success}</div>}

          <form onSubmit={handleSubmit}>
            <div className="assignments-section">
              <div className="section-header">
                <h3>Artisan Assignments</h3>
                <button 
                  type="button" 
                  className="add-artisan-btn"
                  onClick={handleAddArtisan}
                >
                  + Add Artisan
                </button>
              </div>

              {assignments.map((assignment, index) => (
                <div key={index} className="artisan-assignment-row">
                  <div className="form-group">
                    <label>Artisan</label>
                    <select
                      value={assignment.artisan_id}
                      onChange={(e) => handleArtisanSelect(index, e.target.value)}
                      required
                    >
                      <option value="">Select Artisan</option>
                      {artisans.map((artisan) => (
                        <option key={artisan.customer_id} value={artisan.customer_id}>
                          {artisan.customer_id} - {artisan.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Quantity to Assign</label>
                    <input
                      type="number"
                      min="1"
                      max={getRemainingQuantity() + (assignment.assigned_quantity || 0)}
                      value={assignment.assigned_quantity}
                      onChange={(e) => handleArtisanChange(index, 'assigned_quantity', parseInt(e.target.value) || 0)}
                      required
                    />
                  </div>

                  <button
                    type="button"
                    className="remove-artisan-btn"
                    onClick={() => handleRemoveArtisan(index)}
                  >
                    Remove
                  </button>
                </div>
              ))}

              {assignments.length === 0 && (
                <div className="no-assignments">
                  <p>No artisans assigned yet. Click "Add Artisan" to start.</p>
                </div>
              )}
            </div>

            <div className="modal-actions">
              <button 
                type="button" 
                className="cancel-btn" 
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </button>
              <button 
                type="submit" 
                className="submit-btn"
                disabled={loading || assignments.length === 0}
              >
                {loading ? 'Saving...' : 'Save Assignments'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default ArtisanAssignmentModal; 