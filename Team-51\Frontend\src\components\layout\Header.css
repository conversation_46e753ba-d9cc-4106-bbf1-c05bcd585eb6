/* Header Component Styles */
.header {
  background: linear-gradient(135deg, #2c5530 0%, #3d7b47 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

/* Logo Section */
.header-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-logo-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.header-logo-text h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: white;
  letter-spacing: 1px;
}

.header-tagline {
  font-size: 0.75rem;
  color: #b8e6c1;
  font-weight: 400;
  margin-top: -4px;
  display: block;
}

/* Navigation */
.header-nav {
  display: flex;
  align-items: center;
}

.header-nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 30px;
}

.header-nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 6px;
  position: relative;
}

.header-nav-link:hover {
  color: #b8e6c1;
  background: rgba(255, 255, 255, 0.1);
}

.header-nav-link:active {
  color: #90d4a3;
}

/* Action Buttons */
.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-user-menu {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-welcome {
  font-size: 0.95rem;
  color: #b8e6c1;
}

.header-welcome strong {
  color: white;
}

.header-auth-buttons {
  display: flex;
  gap: 10px;
}

.header-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.header-btn-primary {
  background: #ffffff;
  color: #2c5530;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-btn-primary:hover {
  background: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.header-btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.header-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.6);
}

.header-btn-logout {
  background: rgba(239, 68, 68, 0.1);
  color: #fca5a5;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.header-btn-logout:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

/* Mobile Menu Toggle */
.header-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
}

.header-menu-toggle span {
  width: 25px;
  height: 3px;
  background: white;
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: 0 15px;
    min-height: 70px;
  }

  .header-logo-text h1 {
    font-size: 1.5rem;
  }

  .header-tagline {
    font-size: 0.65rem;
  }

  .header-nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #2c5530;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .header-nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .header-nav-list {
    flex-direction: column;
    gap: 0;
    padding: 20px 0;
  }

  .header-nav-link {
    padding: 15px 20px;
    border-radius: 0;
    display: block;
    width: 100%;
  }

  .header-menu-toggle {
    display: flex;
  }

  .header-actions {
    gap: 10px;
  }

  .header-auth-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .header-btn {
    padding: 8px 16px;
    font-size: 0.85rem;
  }

  .header-user-menu {
    flex-direction: column;
    gap: 8px;
    align-items: flex-end;
  }

  .header-welcome {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 10px;
  }

  .header-logo-img {
    width: 40px;
    height: 40px;
  }

  .header-logo-text h1 {
    font-size: 1.3rem;
  }

  .header-tagline {
    font-size: 0.6rem;
  }
}
