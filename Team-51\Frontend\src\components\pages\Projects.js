import React from 'react';
import './Projects.css';

function Projects() {
  return (
    <section className="projects" id="projects">
      <div className="projects-container">
        <div className="projects-header">
          <h2 className="projects-title">Our Projects & Impact</h2>
          <p className="projects-subtitle">
            Transformative, community-driven initiatives empowering villages across Rajasthan
          </p>
        </div>
        
        <div className="projects-grid">
          <div className="project-card">
            <div className="project-icon">🌟</div>
            <h3>"She Dares" Initiative</h3>
            <p>
              In collaboration with Lamberti and other partners, this program empowers guar-farming community women, 
              offering livelihood support and global visibility to rural women leaders.
            </p>
            <div className="project-impact">
              <span className="impact-badge">Women Empowerment</span>
            </div>
          </div>

          <div className="project-card">
            <div className="project-icon">🎯</div>
            <h3>Youth Adda (Janshruti)</h3>
            <p>
              Informal forums across Bassi where rural youth—especially girls—share ambitions, build confidence, 
              and develop leadership skills. Over 400 youths engaged through this initiative.
            </p>
            <div className="project-impact">
              <span className="impact-badge">400+ Youth Engaged</span>
            </div>
          </div>

          <div className="project-card">
            <div className="project-icon">🎨</div>
            <h3>Skill-Training Centres</h3>
            <p>
              Initiated in villages like Isharwala through PANS-led and women-led efforts, featuring art and 
              vocational training centres managed by local women leaders.
            </p>
            <div className="project-impact">
              <span className="impact-badge">Women-Led Training</span>
            </div>
          </div>

          <div className="project-card">
            <div className="project-icon">📚</div>
            <h3>Teach for India Fellowship</h3>
            <p>
              Partnered with Teach For India to place fellows like Neha Chopra in villages, focusing on 
              early-childhood education and women leadership growth.
            </p>
            <div className="project-impact">
              <span className="impact-badge">Education Partnership</span>
            </div>
          </div>
        </div>

        <div className="partnerships-section">
          <h3>Our Partners</h3>
          <div className="partners-grid">
            <div className="partner-card">
              <h4>Lamberti</h4>
              <p>Collaboration under "She Dares" providing global visibility to rural women leaders</p>
            </div>
            <div className="partner-card">
              <h4>Indibni Foundation</h4>
              <p>Support for sustainable women's livelihood efforts, including eco-friendly "seed rakhi" products</p>
            </div>
            <div className="partner-card">
              <h4>Teach For India</h4>
              <p>Educational partnership focusing on early-childhood education and leadership development</p>
            </div>
          </div>
        </div>

        <div className="impact-stats">
          <div className="stat-card">
            <h3>100</h3>
            <p>Target Villages for Transformation</p>
          </div>
          <div className="stat-card">
            <h3>400+</h3>
            <p>Youth Empowered Through Janshruti</p>
          </div>
          <div className="stat-card">
            <h3>28+</h3>
            <p>Years of Community Service</p>
          </div>
          <div className="stat-card">
            <h3>11-50</h3>
            <p>Dedicated Team Members</p>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Projects;
