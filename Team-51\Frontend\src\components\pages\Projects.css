/* Projects Section Styles */
.projects {
  padding: 80px 0;
  background: var(--white);
}

.projects-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.projects-header {
  text-align: center;
  margin-bottom: 60px;
}

.projects-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-blue);
  margin-bottom: 16px;
  position: relative;
}

.projects-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-orange), var(--accent-orange-light));
  border-radius: 2px;
}

.projects-subtitle {
  font-size: 1.2rem;
  color: var(--gray-700);
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.project-card {
  background: var(--white);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid var(--gray-200);
  overflow: hidden;
  word-wrap: break-word;
  hyphens: auto;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.project-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  text-align: center;
}

.project-card h3 {
  font-size: 1.6rem;
  font-weight: 600;
  color: var(--light-green);
  margin-bottom: 16px;
  text-align: center;
  word-wrap: break-word;
  hyphens: auto;
}

.project-card p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--gray-600);
  margin-bottom: 20px;
  word-wrap: break-word;
  hyphens: auto;
}

.project-impact {
  text-align: center;
}

.impact-badge {
  display: inline-block;
  background: var(--light-green-pale);
  color: var(--light-green);
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.partnerships-section {
  margin-bottom: 60px;
  padding: 40px;
  background: var(--light-blue-bg);
  border-radius: 15px;
}

.partnerships-section h3 {
  text-align: center;
  font-size: 2rem;
  color: var(--primary-blue);
  margin-bottom: 30px;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.partner-card {
  background: var(--white);
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  overflow: hidden;
  word-wrap: break-word;
  hyphens: auto;
}

.partner-card h4 {
  font-size: 1.3rem;
  color: var(--accent-orange);
  margin-bottom: 12px;
  font-weight: 600;
  word-wrap: break-word;
}

.partner-card p {
  font-size: 1rem;
  color: var(--gray-600);
  line-height: 1.5;
  margin: 0;
  word-wrap: break-word;
  hyphens: auto;
}

.impact-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 25px;
  padding: 40px;
  background: linear-gradient(135deg, var(--primary-blue-lighter) 0%, var(--white) 100%);
  border-radius: 15px;
  border: 2px solid var(--primary-blue-lighter);
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  overflow: hidden;
  word-wrap: break-word;
  hyphens: auto;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-card h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: var(--primary-blue);
}

.stat-card p {
  font-size: 0.9rem;
  margin: 0;
  color: var(--gray-700);
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  hyphens: auto;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .projects {
    padding: 60px 0;
  }
  
  .projects-title {
    font-size: 2.5rem;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .project-card {
    padding: 25px;
  }
  
  .partnerships-section {
    padding: 30px 20px;
  }
  
  .partners-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .impact-stats {
    grid-template-columns: repeat(2, 1fr);
    padding: 30px 20px;
    gap: 20px;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-card h3 {
    font-size: 1.8rem;
  }
  
  .stat-card p {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .projects-title {
    font-size: 2rem;
  }
  
  .impact-stats {
    grid-template-columns: 1fr;
    padding: 25px 15px;
  }
  
  .stat-card {
    padding: 20px 15px;
  }
  
  .stat-card h3 {
    font-size: 1.6rem;
  }
  
  .stat-card p {
    font-size: 0.8rem;
  }
  
  .project-icon {
    font-size: 2.5rem;
  }
}
