const express = require('express');
const jwt = require('jsonwebtoken');
const Buyer = require('../models/Buyer');
const User = require('../models/User');
const router = express.Router();

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ msg: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, 's0meSup3r$tr0ng!Key2025');
    const user = await User.findById(decoded.id);
    if (!user) {
      return res.status(403).json({ msg: 'User not found' });
    }
    req.user = user;
    next();
  } catch (err) {
    return res.status(403).json({ msg: 'Invalid token' });
  }
};

// Get all buyers
router.get('/', authenticateToken, async (req, res) => {
  try {
    const buyers = await Buyer.find().sort({ createdAt: -1 });
    res.json(buyers);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get buyer by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const buyer = await Buyer.findById(req.params.id);
    if (!buyer) {
      return res.status(404).json({ msg: 'Buyer not found' });
    }
    res.json(buyer);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Create new buyer
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      delivery_method,
      shipping_address,
      billing_address
    } = req.body;

    // Validate required fields
    if (!name || !email || !phone || !shipping_address || !billing_address) {
      return res.status(400).json({ msg: 'Missing required fields' });
    }

    // Check if email already exists
    const existingBuyer = await Buyer.findOne({ email });
    if (existingBuyer) {
      return res.status(400).json({ msg: 'Email already exists' });
    }

    const newBuyer = new Buyer({
      name,
      email,
      phone,
      delivery_method: delivery_method || 'standard',
      shipping_address,
      billing_address
    });

    await newBuyer.save();
    res.status(201).json({ 
      msg: 'Buyer created successfully', 
      buyer: newBuyer 
    });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Update buyer
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      delivery_method,
      shipping_address,
      billing_address
    } = req.body;

    const buyer = await Buyer.findById(req.params.id);
    if (!buyer) {
      return res.status(404).json({ msg: 'Buyer not found' });
    }

    // Update fields if provided
    if (name) buyer.name = name;
    if (email) buyer.email = email;
    if (phone) buyer.phone = phone;
    if (delivery_method) buyer.delivery_method = delivery_method;
    if (shipping_address) buyer.shipping_address = shipping_address;
    if (billing_address) buyer.billing_address = billing_address;

    await buyer.save();
    res.json({ msg: 'Buyer updated successfully', buyer });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Delete buyer
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const buyer = await Buyer.findByIdAndDelete(req.params.id);
    if (!buyer) {
      return res.status(404).json({ msg: 'Buyer not found' });
    }
    res.json({ msg: 'Buyer deleted successfully' });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

module.exports = router; 