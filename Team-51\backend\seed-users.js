const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');
const TeamLeader = require('./models/TeamLeader');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/ngo_users')
  .then(() => console.log('MongoDB connected for seeding users'))
  .catch(err => console.log(err));

// Helper to generate user_id
function generateUserId(type) {
  const unique = Math.random().toString(36).substring(2, 10);
  if (type === 'admin') return `admin-${unique}`;
  if (type === 'admin-pan') return `admin-pan-${unique}`;
  if (type === 'team leader') return `teamlead-${unique}`;
  if (type === 'artisan') return `artisan-${unique}`;
  return `user-${unique}`;
}

// Function to seed team leaders as users
async function seedTeamLeaderUsers() {
  try {
    // Get all team leaders from the database
    const teamLeaders = await TeamLeader.find();
    
    if (teamLeaders.length === 0) {
      console.log('No team leaders found in database. Please run the main seed.js first.');
      process.exit(1);
    }

    console.log(`Found ${teamLeaders.length} team leaders to create as users`);

    // Create users for each team leader
    for (const tl of teamLeaders) {
      // Check if user already exists
      const existingUser = await User.findOne({ username: tl.tl_id });
      
      if (existingUser) {
        console.log(`User ${tl.tl_id} already exists, skipping...`);
        continue;
      }

      // Hash the password (tl_id as password)
      const hashedPassword = await bcrypt.hash(tl.tl_id, 10);

      // Create user
      const user_id = generateUserId('team leader');
      const newUser = new User({
        username: tl.tl_id,
        password: hashedPassword,
        email: `${tl.tl_id.toLowerCase()}@example.com`,
        phone_number: '1234567890', // Default phone number
        type: 'team leader',
        user_id: user_id,
        verified: true // Auto-verify team leaders
      });

      await newUser.save();
      console.log(`Created user: ${tl.tl_id} (${tl.name})`);
    }

    console.log('Team leader users seeded successfully!');
    console.log('\nTeam Leader Login Credentials:');
    console.log('==============================');
    
    for (const tl of teamLeaders) {
      console.log(`Username: ${tl.tl_id}`);
      console.log(`Password: ${tl.tl_id}`);
      console.log(`Name: ${tl.name}`);
      console.log('---');
    }

    process.exit(0);
  } catch (error) {
    console.error('Error seeding team leader users:', error);
    process.exit(1);
  }
}

// Run the seed function
seedTeamLeaderUsers(); 