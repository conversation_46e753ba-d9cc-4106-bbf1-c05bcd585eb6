const mongoose = require('mongoose');
const Order = require('./models/Order');
const Artisan = require('./models/Artisan');
const TeamLeader = require('./models/TeamLeader');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/ngo_users')
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.log(err));

// Function to update existing orders with artisan assignments
async function updateOrdersWithArtisans() {
  try {
    console.log('Starting to update existing orders with artisan assignments...');

    // Get all existing orders
    const orders = await Order.find({});
    console.log(`Found ${orders.length} orders to update`);

    // Get all artisans
    const artisans = await Artisan.find({});
    console.log(`Found ${artisans.length} artisans`);

    // Get all team leaders
    const teamLeaders = await TeamLeader.find({});
    console.log(`Found ${teamLeaders.length} team leaders`);

    // Group artisans by village (tl_id represents village/cluster)
    const artisansByVillage = {};
    artisans.forEach(artisan => {
      if (!artisansByVillage[artisan.tl_id]) {
        artisansByVillage[artisan.tl_id] = [];
      }
      artisansByVillage[artisan.tl_id].push(artisan);
    });

    console.log('Artisans grouped by village:');
    Object.keys(artisansByVillage).forEach(village => {
      console.log(`  Village ${village}: ${artisansByVillage[village].length} artisans`);
    });

    // Create artisan assignments for each order
    for (const order of orders) {
      console.log(`Processing order: ${order.order_id}`);

      // Clear existing artisan assignments
      order.artisan_assignments = [];

      // For each team leader assigned to this order
      for (let i = 0; i < order.team_leads.length; i++) {
        const teamLeaderId = order.team_leads[i];
        const teamLeaderQuantity = order.quantities[i];

        // Get artisans from this team leader's village
        const villageArtisans = artisansByVillage[teamLeaderId] || [];
        
        if (villageArtisans.length > 0) {
          console.log(`  Team Leader ${teamLeaderId} has ${villageArtisans.length} artisans in village`);

          // Create realistic artisan assignments based on order characteristics
          const artisanAssignments = createVillageBasedAssignments(
            villageArtisans, 
            teamLeaderQuantity, 
            order, 
            i
          );

          // Add the team leader assignment to the order
          order.artisan_assignments.push({
            team_leader_id: teamLeaderId,
            artisans: artisanAssignments
          });

          console.log(`  Created ${artisanAssignments.length} artisan assignments for team leader ${teamLeaderId}`);
        } else {
          console.log(`  No artisans found for team leader ${teamLeaderId}`);
        }
      }

      // Save the updated order
      await order.save();
      console.log(`  Updated order ${order.order_id} with artisan assignments`);
    }

    console.log('Successfully updated all orders with artisan assignments!');
    
    // Display summary
    const updatedOrders = await Order.find({});
    let totalAssignments = 0;
    let totalArtisans = 0;
    let ordersWithMultipleArtisans = 0;

    updatedOrders.forEach(order => {
      if (order.artisan_assignments) {
        order.artisan_assignments.forEach(assignment => {
          totalAssignments++;
          totalArtisans += assignment.artisans.length;
          if (assignment.artisans.length >= 2) {
            ordersWithMultipleArtisans++;
          }
        });
      }
    });

    console.log(`\nSummary:`);
    console.log(`- Total orders updated: ${updatedOrders.length}`);
    console.log(`- Total team leader assignments: ${totalAssignments}`);
    console.log(`- Total artisan assignments: ${totalArtisans}`);
    console.log(`- Orders with 2+ artisans: ${ordersWithMultipleArtisans}`);

  } catch (error) {
    console.error('Error updating orders:', error);
  } finally {
    mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Function to create village-based artisan assignments
function createVillageBasedAssignments(artisans, totalQuantity, order, orderIndex) {
  const assignments = [];
  let remainingQuantity = totalQuantity;

  // Determine how many artisans should work on this order (2-4 artisans)
  const numArtisans = Math.min(
    Math.max(2, Math.floor(Math.random() * 3) + 2), // 2-4 artisans
    artisans.length
  );

  console.log(`    Assigning ${numArtisans} artisans to work on this order`);

  // Select artisans for this order
  const selectedArtisans = [];
  const availableArtisans = [...artisans];

  // ART001 (Lakshmi Devi) should be included in most orders if available
  const art001 = availableArtisans.find(a => a.customer_id === 'ART001');
  if (art001 && Math.random() > 0.2) { // 80% chance to include ART001
    selectedArtisans.push(art001);
    availableArtisans.splice(availableArtisans.indexOf(art001), 1);
  }

  // Add other artisans randomly
  while (selectedArtisans.length < numArtisans && availableArtisans.length > 0) {
    const randomIndex = Math.floor(Math.random() * availableArtisans.length);
    selectedArtisans.push(availableArtisans[randomIndex]);
    availableArtisans.splice(randomIndex, 1);
  }

  // Distribute work among selected artisans
  const baseQuantityPerArtisan = Math.floor(totalQuantity / selectedArtisans.length);
  const extraQuantity = totalQuantity % selectedArtisans.length;

  selectedArtisans.forEach((artisan, index) => {
    let assignedQuantity = baseQuantityPerArtisan;
    
    // Distribute extra quantity to first few artisans
    if (index < extraQuantity) {
      assignedQuantity += 1;
    }

    if (assignedQuantity > 0) {
      // Calculate completion based on order status
      let completedQuantity = 0;
      if (order.status === 'completed') {
        completedQuantity = assignedQuantity;
      } else if (order.status === 'in progress') {
        // ART001 has higher completion rate
        const completionRate = artisan.customer_id === 'ART001' ? 0.8 : 0.6;
        completedQuantity = Math.floor(assignedQuantity * completionRate);
      }

      assignments.push({
        artisan_id: artisan.customer_id,
        artisan_name: artisan.name,
        assigned_quantity: assignedQuantity,
        completed_quantity: completedQuantity,
        status: order.status === 'completed' ? 'completed' : 
               order.status === 'in progress' ? 'in progress' : 'assigned',
        assigned_at: new Date(order.date_ordered)
      });

      remainingQuantity -= assignedQuantity;
    }
  });

  // If there's still remaining quantity, distribute it
  if (remainingQuantity > 0 && selectedArtisans.length > 0) {
    const extraPerArtisan = Math.floor(remainingQuantity / selectedArtisans.length);
    selectedArtisans.forEach((artisan, index) => {
      if (remainingQuantity > 0) {
        const extraQty = index === selectedArtisans.length - 1 ? remainingQuantity : extraPerArtisan;
        
        // Find and update the existing assignment
        const existingAssignment = assignments.find(a => a.artisan_id === artisan.customer_id);
        if (existingAssignment) {
          existingAssignment.assigned_quantity += extraQty;
          if (order.status === 'completed') {
            existingAssignment.completed_quantity += extraQty;
          } else if (order.status === 'in progress') {
            const completionRate = artisan.customer_id === 'ART001' ? 0.8 : 0.6;
            existingAssignment.completed_quantity += Math.floor(extraQty * completionRate);
          }
        }
        
        remainingQuantity -= extraQty;
      }
    });
  }

  return assignments;
}

// Run the update function
updateOrdersWithArtisans(); 