{"ast": null, "code": "import axios from 'axios';\n\n// Base API URL\nconst API_BASE_URL = 'http://localhost:5000/api';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor to include auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add response interceptor to handle errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid, clear auth data\n    localStorage.removeItem('token');\n    localStorage.removeItem('username');\n    localStorage.removeItem('user_id');\n    localStorage.removeItem('user_type');\n    localStorage.removeItem('verified');\n    window.location.reload();\n  }\n  return Promise.reject(error);\n});\n\n// Auth API functions\nexport const authAPI = {\n  signup: userData => api.post('/auth/signup', userData),\n  signin: credentials => api.post('/auth/signin', credentials),\n  googleAuth: googleData => api.post('/auth/google-auth', googleData),\n  enroll: enrollmentData => api.post('/auth/enroll', enrollmentData)\n};\n\n// User utility functions\nexport const userUtils = {\n  isAuthenticated: () => {\n    const token = localStorage.getItem('token');\n    const username = localStorage.getItem('username');\n    return !!(token && username);\n  },\n  getUserInfo: () => {\n    return {\n      username: localStorage.getItem('username'),\n      userId: localStorage.getItem('user_id'),\n      userType: localStorage.getItem('user_type'),\n      verified: localStorage.getItem('verified') === 'true',\n      token: localStorage.getItem('token')\n    };\n  },\n  setUserInfo: userData => {\n    localStorage.setItem('token', userData.token);\n    localStorage.setItem('username', userData.username);\n    localStorage.setItem('user_id', userData.user_id);\n    localStorage.setItem('user_type', userData.type);\n    localStorage.setItem('verified', userData.verified);\n  },\n  clearUserInfo: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('username');\n    localStorage.removeItem('user_id');\n    localStorage.removeItem('user_type');\n    localStorage.removeItem('verified');\n  },\n  canAccessDashboard: () => {\n    const userInfo = userUtils.getUserInfo();\n    return userInfo.verified && ['team leader', 'admin-pan', 'admin'].includes(userInfo.userType);\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "reload", "authAPI", "signup", "userData", "post", "signin", "credentials", "googleAuth", "googleData", "enroll", "enrollmentData", "userUtils", "isAuthenticated", "username", "getUserInfo", "userId", "userType", "verified", "setUserInfo", "setItem", "user_id", "type", "clearUserInfo", "canAccessDashboard", "userInfo", "includes"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Base API URL\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\n// Create axios instance with default config\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add request interceptor to include auth token\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor to handle errors\r\napi.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response?.status === 401) {\r\n      // Token expired or invalid, clear auth data\r\n      localStorage.removeItem('token');\r\n      localStorage.removeItem('username');\r\n      localStorage.removeItem('user_id');\r\n      localStorage.removeItem('user_type');\r\n      localStorage.removeItem('verified');\r\n      window.location.reload();\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Auth API functions\r\nexport const authAPI = {\r\n  signup: (userData) => api.post('/auth/signup', userData),\r\n  signin: (credentials) => api.post('/auth/signin', credentials),\r\n  googleAuth: (googleData) => api.post('/auth/google-auth', googleData),\r\n  enroll: (enrollmentData) => api.post('/auth/enroll', enrollmentData),\r\n};\r\n\r\n// User utility functions\r\nexport const userUtils = {\r\n  isAuthenticated: () => {\r\n    const token = localStorage.getItem('token');\r\n    const username = localStorage.getItem('username');\r\n    return !!(token && username);\r\n  },\r\n  \r\n  getUserInfo: () => {\r\n    return {\r\n      username: localStorage.getItem('username'),\r\n      userId: localStorage.getItem('user_id'),\r\n      userType: localStorage.getItem('user_type'),\r\n      verified: localStorage.getItem('verified') === 'true',\r\n      token: localStorage.getItem('token'),\r\n    };\r\n  },\r\n  \r\n  setUserInfo: (userData) => {\r\n    localStorage.setItem('token', userData.token);\r\n    localStorage.setItem('username', userData.username);\r\n    localStorage.setItem('user_id', userData.user_id);\r\n    localStorage.setItem('user_type', userData.type);\r\n    localStorage.setItem('verified', userData.verified);\r\n  },\r\n  \r\n  clearUserInfo: () => {\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('username');\r\n    localStorage.removeItem('user_id');\r\n    localStorage.removeItem('user_type');\r\n    localStorage.removeItem('verified');\r\n  },\r\n  \r\n  canAccessDashboard: () => {\r\n    const userInfo = userUtils.getUserInfo();\r\n    return userInfo.verified && \r\n           ['team leader', 'admin-pan', 'admin'].includes(userInfo.userType);\r\n  }\r\n};\r\n\r\nexport default api;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,UAAU,CAAC;IACnCT,YAAY,CAACS,UAAU,CAAC,SAAS,CAAC;IAClCT,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCT,YAAY,CAACS,UAAU,CAAC,UAAU,CAAC;IACnCC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,MAAM,EAAGC,QAAQ,IAAKxB,GAAG,CAACyB,IAAI,CAAC,cAAc,EAAED,QAAQ,CAAC;EACxDE,MAAM,EAAGC,WAAW,IAAK3B,GAAG,CAACyB,IAAI,CAAC,cAAc,EAAEE,WAAW,CAAC;EAC9DC,UAAU,EAAGC,UAAU,IAAK7B,GAAG,CAACyB,IAAI,CAAC,mBAAmB,EAAEI,UAAU,CAAC;EACrEC,MAAM,EAAGC,cAAc,IAAK/B,GAAG,CAACyB,IAAI,CAAC,cAAc,EAAEM,cAAc;AACrE,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,eAAe,EAAEA,CAAA,KAAM;IACrB,MAAMzB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMwB,QAAQ,GAAGzB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD,OAAO,CAAC,EAAEF,KAAK,IAAI0B,QAAQ,CAAC;EAC9B,CAAC;EAEDC,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAO;MACLD,QAAQ,EAAEzB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MAC1C0B,MAAM,EAAE3B,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;MACvC2B,QAAQ,EAAE5B,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC3C4B,QAAQ,EAAE7B,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,MAAM;MACrDF,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO;IACrC,CAAC;EACH,CAAC;EAED6B,WAAW,EAAGf,QAAQ,IAAK;IACzBf,YAAY,CAAC+B,OAAO,CAAC,OAAO,EAAEhB,QAAQ,CAAChB,KAAK,CAAC;IAC7CC,YAAY,CAAC+B,OAAO,CAAC,UAAU,EAAEhB,QAAQ,CAACU,QAAQ,CAAC;IACnDzB,YAAY,CAAC+B,OAAO,CAAC,SAAS,EAAEhB,QAAQ,CAACiB,OAAO,CAAC;IACjDhC,YAAY,CAAC+B,OAAO,CAAC,WAAW,EAAEhB,QAAQ,CAACkB,IAAI,CAAC;IAChDjC,YAAY,CAAC+B,OAAO,CAAC,UAAU,EAAEhB,QAAQ,CAACc,QAAQ,CAAC;EACrD,CAAC;EAEDK,aAAa,EAAEA,CAAA,KAAM;IACnBlC,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,UAAU,CAAC;IACnCT,YAAY,CAACS,UAAU,CAAC,SAAS,CAAC;IAClCT,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCT,YAAY,CAACS,UAAU,CAAC,UAAU,CAAC;EACrC,CAAC;EAED0B,kBAAkB,EAAEA,CAAA,KAAM;IACxB,MAAMC,QAAQ,GAAGb,SAAS,CAACG,WAAW,CAAC,CAAC;IACxC,OAAOU,QAAQ,CAACP,QAAQ,IACjB,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,CAACQ,QAAQ,CAACD,QAAQ,CAACR,QAAQ,CAAC;EAC1E;AACF,CAAC;AAED,eAAerC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}