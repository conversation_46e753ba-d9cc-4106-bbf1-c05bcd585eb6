const mongoose = require('mongoose');
const TeamLeader = require('./models/TeamLeader');
const Artisan = require('./models/Artisan');
const Order = require('./models/Order');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/ngo_users')
  .then(() => console.log('MongoDB connected for seeding'))
  .catch(err => console.log(err));

// Dummy data for TeamLeader
const teamLeaders = [
  {
    tl_id: 'TL001',
    name: '<PERSON><PERSON>',
    cluster_id: 'CLUSTER001',
    village_name: 'Village A',
    current_orderid: null
  },
  {
    tl_id: 'TL002',
    name: '<PERSON><PERSON>',
    cluster_id: 'CLUSTER002',
    village_name: 'Village B',
    current_orderid: null
  },
  {
    tl_id: 'TL003',
    name: '<PERSON><PERSON> <PERSON>',
    cluster_id: 'CLUSTER003',
    village_name: 'Village C',
    current_orderid: null
  }
];

// Dummy data for Artisan
const artisans = [
  {
    customer_id: 'ART001',
    name: '<PERSON>',
    time: new Date('2024-01-15T10:00:00Z'),
    production_number: 150,
    tl_id: 'TL001'
  },
  {
    customer_id: 'ART002',
    name: 'Sita Ram',
    time: new Date('2024-01-16T11:30:00Z'),
    production_number: 200,
    tl_id: 'TL002'
  },
  {
    customer_id: 'ART003',
    name: 'Gita Singh',
    time: new Date('2024-01-17T09:15:00Z'),
    production_number: 175,
    tl_id: 'TL003'
  }
];

// Dummy data for Order
const orders = [
  {
    order_id: 'ORD001',
    product_id: 'PROD001',
    team_leads: ['TL001', 'TL002'],
    quantities: [300, 200],
    total_qty: 500,
    date_ordered: new Date('2024-01-10T08:00:00Z'),
    deadline: new Date('2024-02-10T17:00:00Z'),
    status: 'in progress',
    remark: 'High priority order - split between two teams'
  },
  {
    order_id: 'ORD002',
    product_id: 'PROD002',
    team_leads: ['TL002'],
    quantities: [300],
    total_qty: 300,
    date_ordered: new Date('2024-01-12T10:30:00Z'),
    deadline: new Date('2024-02-15T17:00:00Z'),
    status: 'completed',
    remark: 'Delivered on time'
  },
  {
    order_id: 'ORD003',
    product_id: 'PROD003',
    team_leads: ['TL001', 'TL002', 'TL003'],
    quantities: [250, 250, 250],
    total_qty: 750,
    date_ordered: new Date('2024-01-14T14:00:00Z'),
    deadline: new Date('2024-02-20T17:00:00Z'),
    status: 'to be done',
    remark: 'Bulk order for export - distributed across three teams'
  }
];

// Function to seed the database
async function seedDatabase() {
  try {
    // Clear existing data
    await TeamLeader.deleteMany({});
    await Artisan.deleteMany({});
    await Order.deleteMany({});
    
    console.log('Cleared existing data');

    // Insert TeamLeaders
    const createdTeamLeaders = await TeamLeader.insertMany(teamLeaders);
    console.log('TeamLeaders seeded:', createdTeamLeaders.length);

    // Update artisans with teamLeaderRef
    const updatedArtisans = artisans.map((artisan, index) => ({
      ...artisan,
      teamLeaderRef: createdTeamLeaders[index]._id
    }));

    // Insert Artisans
    const createdArtisans = await Artisan.insertMany(updatedArtisans);
    console.log('Artisans seeded:', createdArtisans.length);

    // Insert Orders
    const createdOrders = await Order.insertMany(orders);
    console.log('Orders seeded:', createdOrders.length);

    console.log('Database seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seed function
seedDatabase(); 