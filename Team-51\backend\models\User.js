const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema({
  username: { type: String, unique: true, required: true },
  password: { type: String, required: true },
  email: { type: String, required: false },
  phone_number: { type: String, required: true },
  type: {
    type: String,
    enum: ['admin', 'admin-pan', 'team leader', 'artisan'],
    required: true
  },
  user_id: { type: String, unique: true, required: true },
  verified: { type: Boolean, default: false },
});

module.exports = mongoose.model('User', UserSchema);
