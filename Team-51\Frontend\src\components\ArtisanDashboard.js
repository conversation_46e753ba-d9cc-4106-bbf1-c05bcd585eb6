import React, { useEffect, useState } from 'react';
import axios from 'axios';

// TODO: Replace with actual artisan ID from auth context or props
const ARTISAN_ID = 'ART001';

const ArtisanDashboard = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError('');
        // Adjust API base path as needed
        const res = await axios.get(`/api/artisans/${ARTISAN_ID}/orders`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });
        setOrders(res.data);
      } catch (err) {
        setError('Failed to fetch orders');
      } finally {
        setLoading(false);
      }
    };
    fetchOrders();
  }, []);

  return (
    <div style={{ padding: 24 }}>
      <h2>My Orders & Revenue</h2>
      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p style={{ color: 'red' }}>{error}</p>
      ) : orders.length === 0 ? (
        <p>No orders assigned yet.</p>
      ) : (
        <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: 16 }}>
          <thead>
            <tr style={{ background: '#f0f0f0' }}>
              <th>Order ID</th>
              <th>Product</th>
              <th>Status</th>
              <th>Total Amount</th>
              <th>Commission (%)</th>
              <th>Units Assigned</th>
              <th>Team Lead Total</th>
              <th>Your Revenue</th>
              <th>Deadline</th>
            </tr>
          </thead>
          <tbody>
            {orders.map(order => (
              <tr key={order.order_id}>
                <td>{order.order_id}</td>
                <td>{order.product_name}</td>
                <td>{order.status}</td>
                <td>₹{order.total_amount}</td>
                <td>{order.commission_percent}%</td>
                <td>{order.assigned_quantity}</td>
                <td>{order.team_leader_quantity}</td>
                <td style={{ color: '#10b981', fontWeight: 600 }}>₹{order.artisan_revenue}</td>
                <td>{order.deadline ? new Date(order.deadline).toLocaleDateString() : ''}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default ArtisanDashboard; 