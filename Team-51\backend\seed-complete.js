const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const TeamLeader = require('./models/TeamLeader');
const Artisan = require('./models/Artisan');
const Order = require('./models/Order');
const User = require('./models/User');
const Buyer = require('./models/Buyer');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/ngo_users')
  .then(() => console.log('MongoDB connected for complete seeding'))
  .catch(err => console.log(err));

// Helper to generate user_id
function generateUserId(type) {
  const unique = Math.random().toString(36).substring(2, 10);
  if (type === 'admin') return `admin-${unique}`;
  if (type === 'admin-pan') return `admin-pan-${unique}`;
  if (type === 'team leader') return `teamlead-${unique}`;
  if (type === 'artisan') return `artisan-${unique}`;
  return `user-${unique}`;
}

// Dummy data for TeamLeader
const teamLeaders = [
  {
    tl_id: 'TL001',
    name: '<PERSON>esh <PERSON>',
    cluster_id: 'CLUSTER001',
    village_name: 'Village A',
    current_orderid: null
  },
  {
    tl_id: 'TL002',
    name: 'Priya Sharma',
    cluster_id: 'CLUSTER002',
    village_name: 'Village B',
    current_orderid: null
  },
  {
    tl_id: 'TL003',
    name: 'Amit Patel',
    cluster_id: 'CLUSTER003',
    village_name: 'Village C',
    current_orderid: null
  }
];

// Dummy data for Artisan
const artisans = [
  {
    customer_id: 'ART001',
    name: 'Lakshmi Devi',
    time: new Date('2024-01-15T10:00:00Z'),
    production_number: 150,
    tl_id: 'TL001'
  },
  {
    customer_id: 'ART002',
    name: 'Sita Ram',
    time: new Date('2024-01-16T11:30:00Z'),
    production_number: 200,
    tl_id: 'TL002'
  },
  {
    customer_id: 'ART003',
    name: 'Gita Singh',
    time: new Date('2024-01-17T09:15:00Z'),
    production_number: 175,
    tl_id: 'TL003'
  }
];

// Dummy data for Buyer
const buyers = [
  {
    name: 'Fashion Retail Co.',
    email: '<EMAIL>',
    phone: '******-0101',
    delivery_method: 'express',
    shipping_address: {
      street: '123 Fashion Street',
      city: 'New York',
      state: 'NY',
      zip_code: '10001',
      country: 'USA'
    },
    billing_address: {
      street: '123 Fashion Street',
      city: 'New York',
      state: 'NY',
      zip_code: '10001',
      country: 'USA'
    }
  },
  {
    name: 'Urban Clothing Store',
    email: '<EMAIL>',
    phone: '******-0202',
    delivery_method: 'standard',
    shipping_address: {
      street: '456 Urban Avenue',
      city: 'Los Angeles',
      state: 'CA',
      zip_code: '90210',
      country: 'USA'
    },
    billing_address: {
      street: '456 Urban Avenue',
      city: 'Los Angeles',
      state: 'CA',
      zip_code: '90210',
      country: 'USA'
    }
  },
  {
    name: 'Winter Fashion Ltd.',
    email: '<EMAIL>',
    phone: '******-0303',
    delivery_method: 'overnight',
    shipping_address: {
      street: '789 Winter Road',
      city: 'Chicago',
      state: 'IL',
      zip_code: '60601',
      country: 'USA'
    },
    billing_address: {
      street: '789 Winter Road',
      city: 'Chicago',
      state: 'IL',
      zip_code: '60601',
      country: 'USA'
    }
  }
];

// Dummy data for Order
const orders = [
  {
    order_id: 'ORD001',
    product_id: 'PROD001',
    product_name: 'Cotton T-Shirts',
    buyer_name: 'Fashion Retail Co.',
    team_leads: ['TL001', 'TL002'],
    quantities: [300, 200],
    total_qty: 500,
    date_ordered: new Date('2024-01-10T08:00:00Z'),
    deadline: new Date('2024-02-10T17:00:00Z'),
    status: 'in progress',
    remark: 'High priority order - split between two teams'
  },
  {
    order_id: 'ORD002',
    product_id: 'PROD002',
    product_name: 'Denim Jeans',
    buyer_name: 'Urban Clothing Store',
    team_leads: ['TL002'],
    quantities: [300],
    total_qty: 300,
    date_ordered: new Date('2024-01-12T10:30:00Z'),
    deadline: new Date('2024-02-15T17:00:00Z'),
    status: 'completed',
    remark: 'Delivered on time'
  },
  {
    order_id: 'ORD003',
    product_id: 'PROD003',
    product_name: 'Wool Sweaters',
    buyer_name: 'Winter Fashion Ltd.',
    team_leads: ['TL001', 'TL002', 'TL003'],
    quantities: [250, 250, 250],
    total_qty: 750,
    date_ordered: new Date('2024-01-14T14:00:00Z'),
    deadline: new Date('2024-02-20T17:00:00Z'),
    status: 'to be done',
    remark: 'Bulk order for export - distributed across three teams'
  }
];

// Admin users to create
const adminUsers = [
  {
    username: 'admin',
    password: 'admin123',
    email: '<EMAIL>',
    phone_number: '1234567890',
    type: 'admin',
    verified: true
  },
  {
    username: 'admin-pan',
    password: 'adminpan123',
    email: '<EMAIL>',
    phone_number: '1234567891',
    type: 'admin-pan',
    verified: true
  }
];

// Function to seed the complete database
async function seedCompleteDatabase() {
  try {
    // Clear existing data
    await TeamLeader.deleteMany({});
    await Artisan.deleteMany({});
    await Order.deleteMany({});
    await Buyer.deleteMany({});
    await User.deleteMany({});
    
    console.log('Cleared existing data');

    // Insert Buyers
    const createdBuyers = await Buyer.insertMany(buyers);
    console.log('Buyers seeded:', createdBuyers.length);

    // Insert TeamLeaders
    const createdTeamLeaders = await TeamLeader.insertMany(teamLeaders);
    console.log('TeamLeaders seeded:', createdTeamLeaders.length);

    // Update artisans with teamLeaderRef
    const updatedArtisans = artisans.map((artisan, index) => ({
      ...artisan,
      teamLeaderRef: createdTeamLeaders[index]._id
    }));

    // Insert Artisans
    const createdArtisans = await Artisan.insertMany(updatedArtisans);
    console.log('Artisans seeded:', createdArtisans.length);

    // Create orders with buyer references
    const ordersWithBuyers = orders.map((order, index) => ({
      ...order,
      buyer: createdBuyers[index]._id
    }));

    // Insert Orders
    const createdOrders = await Order.insertMany(ordersWithBuyers);
    console.log('Orders seeded:', createdOrders.length);

    // Create admin users
    for (const adminUser of adminUsers) {
      const hashedPassword = await bcrypt.hash(adminUser.password, 10);
      const user_id = generateUserId(adminUser.type);
      
      const newUser = new User({
        ...adminUser,
        password: hashedPassword,
        user_id: user_id
      });
      
      await newUser.save();
      console.log(`Created ${adminUser.type} user: ${adminUser.username}`);
    }

    // Create team leader users
    for (const tl of createdTeamLeaders) {
      const hashedPassword = await bcrypt.hash(tl.tl_id, 10);
      const user_id = generateUserId('team leader');
      
      const newUser = new User({
        username: tl.tl_id,
        password: hashedPassword,
        email: `${tl.tl_id.toLowerCase()}@example.com`,
        phone_number: '1234567890',
        type: 'team leader',
        user_id: user_id,
        verified: true
      });
      
      await newUser.save();
      console.log(`Created team leader user: ${tl.tl_id} (${tl.name})`);
    }

    console.log('\n=== DATABASE SEEDED SUCCESSFULLY ===');
    console.log('\nLogin Credentials:');
    console.log('==================');
    console.log('\nAdmin Users:');
    console.log('Username: admin, Password: admin123');
    console.log('Username: admin-pan, Password: adminpan123');
    console.log('\nTeam Leader Users:');
    for (const tl of createdTeamLeaders) {
      console.log(`Username: ${tl.tl_id}, Password: ${tl.tl_id} (${tl.name})`);
    }
    console.log('\nOrders Created:');
    for (const order of createdOrders) {
      console.log(`${order.order_id}: ${order.product_id} - ${order.status}`);
    }

    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seed function
seedCompleteDatabase(); 