const mongoose = require('mongoose');
const Artisan = require('./models/Artisan');
const TeamLeader = require('./models/TeamLeader');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/ngo_users')
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.log(err));

// Function to add more artisans
async function addMoreArtisans() {
  try {
    console.log('Adding more artisans to create realistic assignments...');

    // Get existing artisans
    const existingArtisans = await Artisan.find({});
    console.log(`Found ${existingArtisans.length} existing artisans`);

    // Get team leaders
    const teamLeaders = await TeamLeader.find({});
    console.log(`Found ${teamLeaders.length} team leaders`);

    // Create additional artisans for each village
    const newArtisans = [
      // Village TL001 - Add 3 more artisans
      {
        customer_id: 'ART004',
        name: '<PERSON><PERSON>',
        time: new Date('2024-01-20T10:00:00Z'),
        production_number: 180,
        tl_id: 'TL001'
      },
      {
        customer_id: 'ART005',
        name: '<PERSON><PERSON>',
        time: new Date('2024-01-25T11:30:00Z'),
        production_number: 160,
        tl_id: 'TL001'
      },
      {
        customer_id: 'ART006',
        name: 'Sunita Devi',
        time: new Date('2024-02-01T09:15:00Z'),
        production_number: 140,
        tl_id: 'TL001'
      },
      // Village TL002 - Add 2 more artisans
      {
        customer_id: 'ART007',
        name: 'Amit Singh',
        time: new Date('2024-01-22T12:00:00Z'),
        production_number: 170,
        tl_id: 'TL002'
      },
      {
        customer_id: 'ART008',
        name: 'Priya Sharma',
        time: new Date('2024-01-28T14:30:00Z'),
        production_number: 190,
        tl_id: 'TL002'
      },
      // Village TL003 - Add 2 more artisans
      {
        customer_id: 'ART009',
        name: 'Vikram Malhotra',
        time: new Date('2024-01-30T08:45:00Z'),
        production_number: 165,
        tl_id: 'TL003'
      },
      {
        customer_id: 'ART010',
        name: 'Anjali Gupta',
        time: new Date('2024-02-05T13:20:00Z'),
        production_number: 175,
        tl_id: 'TL003'
      }
    ];

    // Check which artisans already exist
    const existingIds = existingArtisans.map(a => a.customer_id);
    const artisansToAdd = newArtisans.filter(artisan => !existingIds.includes(artisan.customer_id));

    if (artisansToAdd.length > 0) {
      console.log(`Adding ${artisansToAdd.length} new artisans...`);

      // Add teamLeaderRef to each artisan
      const artisansWithRefs = artisansToAdd.map(artisan => {
        const teamLeader = teamLeaders.find(tl => tl.tl_id === artisan.tl_id);
        return {
          ...artisan,
          teamLeaderRef: teamLeader ? teamLeader._id : null
        };
      });

      // Insert new artisans
      const createdArtisans = await Artisan.insertMany(artisansWithRefs);
      console.log(`Successfully added ${createdArtisans.length} new artisans`);

      // Display summary
      const allArtisans = await Artisan.find({});
      const artisansByVillage = {};
      
      allArtisans.forEach(artisan => {
        if (!artisansByVillage[artisan.tl_id]) {
          artisansByVillage[artisan.tl_id] = [];
        }
        artisansByVillage[artisan.tl_id].push(artisan);
      });

      console.log('\nUpdated artisan distribution:');
      Object.keys(artisansByVillage).forEach(village => {
        console.log(`  Village ${village}: ${artisansByVillage[village].length} artisans`);
        artisansByVillage[village].forEach(artisan => {
          console.log(`    - ${artisan.customer_id}: ${artisan.name} (Production: ${artisan.production_number})`);
        });
      });
    } else {
      console.log('All artisans already exist, no new artisans to add');
    }

  } catch (error) {
    console.error('Error adding artisans:', error);
  } finally {
    mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the function
addMoreArtisans(); 