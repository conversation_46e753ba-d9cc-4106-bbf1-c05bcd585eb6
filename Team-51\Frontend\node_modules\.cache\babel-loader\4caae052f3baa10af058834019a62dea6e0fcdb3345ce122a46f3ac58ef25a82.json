{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\components\\\\common\\\\TranslateButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport GoogleTranslate from './GoogleTranslate';\nimport './TranslateButton.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TranslateButton() {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [currentLanguage, setCurrentLanguage] = useState('English');\n  useEffect(() => {\n    // Monitor language changes\n    const checkLanguage = () => {\n      const frame = document.querySelector('.goog-te-menu-frame');\n      if (frame && frame.style.display !== 'none') {\n        // Language selector is open\n      }\n\n      // Get current language from Google Translate\n      const selectElement = document.querySelector('.goog-te-combo');\n      if (selectElement && selectElement.value) {\n        const languageMap = {\n          'en': 'English',\n          'hi': 'हिंदी',\n          'ta': 'தமிழ்',\n          'te': 'తెలుగు',\n          'bn': 'বাংলা',\n          'gu': 'ગુજરાતી',\n          'kn': 'ಕನ್ನಡ',\n          'ml': 'മലയാളം',\n          'mr': 'मराठी',\n          'pa': 'ਪੰਜਾਬੀ',\n          'ur': 'اردو',\n          'as': 'অসমীয়া',\n          'or': 'ଓଡ଼ିଆ'\n        };\n        setCurrentLanguage(languageMap[selectElement.value] || 'English');\n      }\n    };\n    const interval = setInterval(checkLanguage, 1000);\n    return () => clearInterval(interval);\n  }, []);\n  const toggleTranslate = () => {\n    setIsOpen(!isOpen);\n  };\n  const handleClickOutside = event => {\n    if (!event.target.closest('.translate-widget-container')) {\n      setIsOpen(false);\n    }\n  };\n  useEffect(() => {\n    if (isOpen) {\n      document.addEventListener('click', handleClickOutside);\n    } else {\n      document.removeEventListener('click', handleClickOutside);\n    }\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, [isOpen]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"translate-widget-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"floating-translate-btn\",\n      onClick: toggleTranslate,\n      title: \"Translate Page\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"translate-icon\",\n        children: \"\\uD83C\\uDF10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"translate-text\",\n        children: \"Translate\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"current-language-indicator\",\n      children: currentLanguage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"translate-popup\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"translate-popup-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select Language\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-btn\",\n          onClick: () => setIsOpen(false),\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"translate-popup-content\",\n        children: /*#__PURE__*/_jsxDEV(GoogleTranslate, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"translate-popup-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Powered by Google Translate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"translate-overlay\",\n      onClick: () => setIsOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 18\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(TranslateButton, \"f4SWGJ9ZetNHE3mqKD1uy9moWJQ=\");\n_c = TranslateButton;\nexport default TranslateButton;\nvar _c;\n$RefreshReg$(_c, \"TranslateButton\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "GoogleTranslate", "jsxDEV", "_jsxDEV", "TranslateButton", "_s", "isOpen", "setIsOpen", "currentLanguage", "setCurrentLanguage", "checkLanguage", "frame", "document", "querySelector", "style", "display", "selectElement", "value", "languageMap", "interval", "setInterval", "clearInterval", "toggleTranslate", "handleClickOutside", "event", "target", "closest", "addEventListener", "removeEventListener", "className", "children", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/components/common/TranslateButton.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport GoogleTranslate from './GoogleTranslate';\nimport './TranslateButton.css';\n\nfunction TranslateButton() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [currentLanguage, setCurrentLanguage] = useState('English');\n\n  useEffect(() => {\n    // Monitor language changes\n    const checkLanguage = () => {\n      const frame = document.querySelector('.goog-te-menu-frame');\n      if (frame && frame.style.display !== 'none') {\n        // Language selector is open\n      }\n      \n      // Get current language from Google Translate\n      const selectElement = document.querySelector('.goog-te-combo');\n      if (selectElement && selectElement.value) {\n        const languageMap = {\n          'en': 'English',\n          'hi': 'हिंदी',\n          'ta': 'தமிழ்',\n          'te': 'తెలుగు',\n          'bn': 'বাংলা',\n          'gu': 'ગુજરાતી',\n          'kn': 'ಕನ್ನಡ',\n          'ml': 'മലയാളം',\n          'mr': 'मराठी',\n          'pa': 'ਪੰਜਾਬੀ',\n          'ur': 'اردو',\n          'as': 'অসমীয়া',\n          'or': 'ଓଡ଼ିଆ'\n        };\n        setCurrentLanguage(languageMap[selectElement.value] || 'English');\n      }\n    };\n\n    const interval = setInterval(checkLanguage, 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const toggleTranslate = () => {\n    setIsOpen(!isOpen);\n  };\n\n  const handleClickOutside = (event) => {\n    if (!event.target.closest('.translate-widget-container')) {\n      setIsOpen(false);\n    }\n  };\n\n  useEffect(() => {\n    if (isOpen) {\n      document.addEventListener('click', handleClickOutside);\n    } else {\n      document.removeEventListener('click', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, [isOpen]);\n\n  return (\n    <div className=\"translate-widget-container\">\n      {/* Floating Translate Button */}\n      <button \n        className=\"floating-translate-btn\"\n        onClick={toggleTranslate}\n        title=\"Translate Page\"\n      >\n        <span className=\"translate-icon\">🌐</span>\n        <span className=\"translate-text\">Translate</span>\n      </button>\n\n      {/* Current Language Indicator */}\n      <div className=\"current-language-indicator\">\n        {currentLanguage}\n      </div>\n\n      {/* Translate Widget Popup */}\n      {isOpen && (\n        <div className=\"translate-popup\">\n          <div className=\"translate-popup-header\">\n            <h3>Select Language</h3>\n            <button \n              className=\"close-btn\"\n              onClick={() => setIsOpen(false)}\n            >\n              ×\n            </button>\n          </div>\n          <div className=\"translate-popup-content\">\n            <GoogleTranslate />\n          </div>\n          <div className=\"translate-popup-footer\">\n            <p>Powered by Google Translate</p>\n          </div>\n        </div>\n      )}\n\n      {/* Overlay */}\n      {isOpen && <div className=\"translate-overlay\" onClick={() => setIsOpen(false)}></div>}\n    </div>\n  );\n}\n\nexport default TranslateButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACS,eAAe,EAAEC,kBAAkB,CAAC,GAAGV,QAAQ,CAAC,SAAS,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACd;IACA,MAAMU,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;MAC3D,IAAIF,KAAK,IAAIA,KAAK,CAACG,KAAK,CAACC,OAAO,KAAK,MAAM,EAAE;QAC3C;MAAA;;MAGF;MACA,MAAMC,aAAa,GAAGJ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIG,aAAa,IAAIA,aAAa,CAACC,KAAK,EAAE;QACxC,MAAMC,WAAW,GAAG;UAClB,IAAI,EAAE,SAAS;UACf,IAAI,EAAE,OAAO;UACb,IAAI,EAAE,OAAO;UACb,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,OAAO;UACb,IAAI,EAAE,SAAS;UACf,IAAI,EAAE,OAAO;UACb,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,OAAO;UACb,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,MAAM;UACZ,IAAI,EAAE,SAAS;UACf,IAAI,EAAE;QACR,CAAC;QACDT,kBAAkB,CAACS,WAAW,CAACF,aAAa,CAACC,KAAK,CAAC,IAAI,SAAS,CAAC;MACnE;IACF,CAAC;IAED,MAAME,QAAQ,GAAGC,WAAW,CAACV,aAAa,EAAE,IAAI,CAAC;IACjD,OAAO,MAAMW,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5Bf,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;EAED,MAAMiB,kBAAkB,GAAIC,KAAK,IAAK;IACpC,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,6BAA6B,CAAC,EAAE;MACxDnB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAEDP,SAAS,CAAC,MAAM;IACd,IAAIM,MAAM,EAAE;MACVM,QAAQ,CAACe,gBAAgB,CAAC,OAAO,EAAEJ,kBAAkB,CAAC;IACxD,CAAC,MAAM;MACLX,QAAQ,CAACgB,mBAAmB,CAAC,OAAO,EAAEL,kBAAkB,CAAC;IAC3D;IAEA,OAAO,MAAM;MACXX,QAAQ,CAACgB,mBAAmB,CAAC,OAAO,EAAEL,kBAAkB,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,CAACjB,MAAM,CAAC,CAAC;EAEZ,oBACEH,OAAA;IAAK0B,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAEzC3B,OAAA;MACE0B,SAAS,EAAC,wBAAwB;MAClCE,OAAO,EAAET,eAAgB;MACzBU,KAAK,EAAC,gBAAgB;MAAAF,QAAA,gBAEtB3B,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CjC,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eAGTjC,OAAA;MAAK0B,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EACxCtB;IAAe;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,EAGL9B,MAAM,iBACLH,OAAA;MAAK0B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B3B,OAAA;QAAK0B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC3B,OAAA;UAAA2B,QAAA,EAAI;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBjC,OAAA;UACE0B,SAAS,EAAC,WAAW;UACrBE,OAAO,EAAEA,CAAA,KAAMxB,SAAS,CAAC,KAAK,CAAE;UAAAuB,QAAA,EACjC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNjC,OAAA;QAAK0B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtC3B,OAAA,CAACF,eAAe;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACNjC,OAAA;QAAK0B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrC3B,OAAA;UAAA2B,QAAA,EAAG;QAA2B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA9B,MAAM,iBAAIH,OAAA;MAAK0B,SAAS,EAAC,mBAAmB;MAACE,OAAO,EAAEA,CAAA,KAAMxB,SAAS,CAAC,KAAK;IAAE;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClF,CAAC;AAEV;AAAC/B,EAAA,CAtGQD,eAAe;AAAAiC,EAAA,GAAfjC,eAAe;AAwGxB,eAAeA,eAAe;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}