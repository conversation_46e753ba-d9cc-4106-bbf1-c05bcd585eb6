{"name": "ngo-website", "version": "0.1.0", "private": true, "dependencies": {"@react-oauth/google": "^0.12.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "bootstrap": "^5.3.7", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}