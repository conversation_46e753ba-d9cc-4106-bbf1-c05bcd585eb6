/* Hero Section Styles */
.hero {
  background: linear-gradient(135deg, #2c5530 0%, #3d7b47 50%, #4a8c5a 100%);
  color: white;
  padding: 60px 0 0 0;
  position: relative;
  overflow: hidden;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  padding: 40px 0 80px 0;
}

.hero-text {
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 24px;
  animation: fadeInUp 1s ease-out;
}

.hero-highlight {
  color: #b8e6c1;
  display: block;
}

.hero-description {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 40px;
  color: #e8f5e8;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-bottom: 40px;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-stat {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-stat h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #b8e6c1;
}

.hero-stat p {
  font-size: 0.9rem;
  margin: 0;
  color: #e8f5e8;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-btn {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.hero-btn-primary {
  background: #ffffff;
  color: #2c5530;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hero-btn-primary:hover {
  background: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.hero-btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.6);
}

.hero-image {
  position: relative;
  animation: fadeInRight 1s ease-out 0.3s both;
}

.hero-img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.hero-image-overlay {
  position: absolute;
  top: -20px;
  right: -20px;
}

.hero-badge {
  background: linear-gradient(45deg, #fbbf24, #f59e0b);
  color: white;
  padding: 12px 24px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
  animation: pulse 2s infinite;
}

.hero-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60px;
  overflow: hidden;
  z-index: 1;
}

.hero-wave svg {
  width: 100%;
  height: 100%;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 40px 0 0 0;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
    padding: 20px 0 60px 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-stat {
    padding: 15px;
  }

  .hero-stat h3 {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .hero-btn {
    padding: 12px 24px;
    font-size: 1rem;
  }

  .hero-img {
    height: 300px;
  }

  .hero-image-overlay {
    top: -10px;
    right: -10px;
  }

  .hero-badge {
    padding: 8px 16px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .hero-container {
    padding: 0 15px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-stat h3 {
    font-size: 1.8rem;
  }

  .hero-img {
    height: 250px;
  }
}
