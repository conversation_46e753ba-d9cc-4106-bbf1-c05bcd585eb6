{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\components\\\\common\\\\GoogleTranslate.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport './GoogleTranslate.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction GoogleTranslate() {\n  _s();\n  useEffect(() => {\n    // Add Google Translate script\n    const addScript = () => {\n      if (!window.google || !window.google.translate) {\n        const script = document.createElement('script');\n        script.type = 'text/javascript';\n        script.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';\n        script.async = true;\n        document.head.appendChild(script);\n      }\n    };\n\n    // Initialize Google Translate\n    window.googleTranslateElementInit = () => {\n      if (window.google && window.google.translate) {\n        new window.google.translate.TranslateElement({\n          pageLanguage: 'en',\n          includedLanguages: 'en,hi,ta,te,bn,gu,kn,ml,mr,pa,ur,as,or',\n          layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,\n          autoDisplay: false,\n          multilanguagePage: true\n        }, 'google_translate_element');\n      }\n    };\n\n    // Load the script\n    addScript();\n\n    // Cleanup function\n    return () => {\n      // Remove the script when component unmounts\n      const scripts = document.querySelectorAll('script[src*=\"translate.google.com\"]');\n      scripts.forEach(script => script.remove());\n\n      // Remove the translate element\n      const translateElement = document.getElementById('google_translate_element');\n      if (translateElement) {\n        translateElement.innerHTML = '';\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"google-translate-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"translate-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"translate-icon\",\n        children: \"\\uD83C\\uDF10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"translate-label\",\n        children: \"Translate\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"google_translate_element\",\n      className: \"google-translate-widget\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n}\n_s(GoogleTranslate, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = GoogleTranslate;\nexport default GoogleTranslate;\nvar _c;\n$RefreshReg$(_c, \"GoogleTranslate\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "GoogleTranslate", "_s", "addScript", "window", "google", "translate", "script", "document", "createElement", "type", "src", "async", "head", "append<PERSON><PERSON><PERSON>", "googleTranslateElementInit", "TranslateElement", "pageLanguage", "includedLanguages", "layout", "InlineLayout", "SIMPLE", "autoDisplay", "multilanguagePage", "scripts", "querySelectorAll", "for<PERSON>ach", "remove", "translateElement", "getElementById", "innerHTML", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/components/common/GoogleTranslate.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport './GoogleTranslate.css';\n\nfunction GoogleTranslate() {\n  useEffect(() => {\n    // Add Google Translate script\n    const addScript = () => {\n      if (!window.google || !window.google.translate) {\n        const script = document.createElement('script');\n        script.type = 'text/javascript';\n        script.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';\n        script.async = true;\n        document.head.appendChild(script);\n      }\n    };\n\n    // Initialize Google Translate\n    window.googleTranslateElementInit = () => {\n      if (window.google && window.google.translate) {\n        new window.google.translate.TranslateElement(\n          {\n            pageLanguage: 'en',\n            includedLanguages: 'en,hi,ta,te,bn,gu,kn,ml,mr,pa,ur,as,or',\n            layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,\n            autoDisplay: false,\n            multilanguagePage: true\n          },\n          'google_translate_element'\n        );\n      }\n    };\n\n    // Load the script\n    addScript();\n\n    // Cleanup function\n    return () => {\n      // Remove the script when component unmounts\n      const scripts = document.querySelectorAll('script[src*=\"translate.google.com\"]');\n      scripts.forEach(script => script.remove());\n      \n      // Remove the translate element\n      const translateElement = document.getElementById('google_translate_element');\n      if (translateElement) {\n        translateElement.innerHTML = '';\n      }\n    };\n  }, []);\n\n  return (\n    <div className=\"google-translate-container\">\n      <div className=\"translate-header\">\n        <span className=\"translate-icon\">🌐</span>\n        <span className=\"translate-label\">Translate</span>\n      </div>\n      <div id=\"google_translate_element\" className=\"google-translate-widget\"></div>\n    </div>\n  );\n}\n\nexport default GoogleTranslate;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzBJ,SAAS,CAAC,MAAM;IACd;IACA,MAAMK,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI,CAACC,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACC,MAAM,CAACC,SAAS,EAAE;QAC9C,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/CF,MAAM,CAACG,IAAI,GAAG,iBAAiB;QAC/BH,MAAM,CAACI,GAAG,GAAG,6EAA6E;QAC1FJ,MAAM,CAACK,KAAK,GAAG,IAAI;QACnBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;MACnC;IACF,CAAC;;IAED;IACAH,MAAM,CAACW,0BAA0B,GAAG,MAAM;MACxC,IAAIX,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,SAAS,EAAE;QAC5C,IAAIF,MAAM,CAACC,MAAM,CAACC,SAAS,CAACU,gBAAgB,CAC1C;UACEC,YAAY,EAAE,IAAI;UAClBC,iBAAiB,EAAE,wCAAwC;UAC3DC,MAAM,EAAEf,MAAM,CAACC,MAAM,CAACC,SAAS,CAACU,gBAAgB,CAACI,YAAY,CAACC,MAAM;UACpEC,WAAW,EAAE,KAAK;UAClBC,iBAAiB,EAAE;QACrB,CAAC,EACD,0BACF,CAAC;MACH;IACF,CAAC;;IAED;IACApB,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX;MACA,MAAMqB,OAAO,GAAGhB,QAAQ,CAACiB,gBAAgB,CAAC,qCAAqC,CAAC;MAChFD,OAAO,CAACE,OAAO,CAACnB,MAAM,IAAIA,MAAM,CAACoB,MAAM,CAAC,CAAC,CAAC;;MAE1C;MACA,MAAMC,gBAAgB,GAAGpB,QAAQ,CAACqB,cAAc,CAAC,0BAA0B,CAAC;MAC5E,IAAID,gBAAgB,EAAE;QACpBA,gBAAgB,CAACE,SAAS,GAAG,EAAE;MACjC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE9B,OAAA;IAAK+B,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzChC,OAAA;MAAK+B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BhC,OAAA;QAAM+B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CpC,OAAA;QAAM+B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eACNpC,OAAA;MAAKqC,EAAE,EAAC,0BAA0B;MAACN,SAAS,EAAC;IAAyB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1E,CAAC;AAEV;AAAClC,EAAA,CAvDQD,eAAe;AAAAqC,EAAA,GAAfrC,eAAe;AAyDxB,eAAeA,eAAe;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}