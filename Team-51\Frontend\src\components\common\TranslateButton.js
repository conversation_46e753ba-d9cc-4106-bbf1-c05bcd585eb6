import React, { useState, useEffect } from 'react';
import GoogleTranslate from './GoogleTranslate';
import './TranslateButton.css';

function TranslateButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('English');

  useEffect(() => {
    // Monitor language changes
    const checkLanguage = () => {
      const frame = document.querySelector('.goog-te-menu-frame');
      if (frame && frame.style.display !== 'none') {
        // Language selector is open
      }
      
      // Get current language from Google Translate
      const selectElement = document.querySelector('.goog-te-combo');
      if (selectElement && selectElement.value) {
        const languageMap = {
          'en': 'English',
          'hi': 'हिंदी',
          'ta': 'தமிழ்',
          'te': 'తెలుగు',
          'bn': 'বাংলা',
          'gu': 'ગુજરાતી',
          'kn': 'ಕನ್ನಡ',
          'ml': 'മലയാളം',
          'mr': 'मराठी',
          'pa': 'ਪੰਜਾਬੀ',
          'ur': 'اردو',
          'as': 'অসমীয়া',
          'or': 'ଓଡ଼ିଆ'
        };
        setCurrentLanguage(languageMap[selectElement.value] || 'English');
      }
    };

    const interval = setInterval(checkLanguage, 1000);
    return () => clearInterval(interval);
  }, []);

  const toggleTranslate = () => {
    setIsOpen(!isOpen);
  };

  const handleClickOutside = (event) => {
    if (!event.target.closest('.translate-widget-container')) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
    } else {
      document.removeEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="translate-widget-container">
      {/* Floating Translate Button */}
      <button 
        className="floating-translate-btn"
        onClick={toggleTranslate}
        title="Translate Page"
      >
        <span className="translate-icon">🌐</span>
        <span className="translate-text">Translate</span>
      </button>

      {/* Current Language Indicator */}
      <div className="current-language-indicator">
        {currentLanguage}
      </div>

      {/* Translate Widget Popup */}
      {isOpen && (
        <div className="translate-popup">
          <div className="translate-popup-header">
            <h3>Select Language</h3>
            <button 
              className="close-btn"
              onClick={() => setIsOpen(false)}
            >
              ×
            </button>
          </div>
          <div className="translate-popup-content">
            <GoogleTranslate />
          </div>
          <div className="translate-popup-footer">
            <p>Powered by Google Translate</p>
          </div>
        </div>
      )}

      {/* Overlay */}
      {isOpen && <div className="translate-overlay" onClick={() => setIsOpen(false)}></div>}
    </div>
  );
}

export default TranslateButton;
