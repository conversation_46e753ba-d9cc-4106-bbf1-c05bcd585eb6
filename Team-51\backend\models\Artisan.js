// models/Artisan.js
const mongoose = require('mongoose');

const artisanSchema = new mongoose.Schema({
  customer_id: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  time: {
    type: Date,
    required: true
  },
  production_number: {
    type: Number,
    required: true
  },
  tl_id: {
    type: String,
    required: true
  },
  teamLeaderRef: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TeamLeader'
  }
}, { timestamps: true });

module.exports = mongoose.model('Artisan', artisanSchema);