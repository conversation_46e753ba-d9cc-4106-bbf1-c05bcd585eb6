/* Auth Form Styles */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease-out;
}

.auth-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 450px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: slideUp 0.3s ease-out;
}

.auth-modal-header {
  padding: 40px 40px 20px;
  text-align: center;
  position: relative;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
  color: white;
  border-radius: 20px 20px 0 0;
}

.auth-modal-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.auth-modal-subtitle {
  font-size: 1rem;
  color: var(--accent-orange-lighter);
  margin: 0;
  opacity: 0.9;
}

.auth-modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.auth-modal-body {
  padding: 40px;
}

.auth-alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.auth-alert-error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.auth-alert-success {
  background: #f0f9ff;
  color: #059669;
  border: 1px solid #bfdbfe;
}

.auth-form {
  margin-bottom: 30px;
}

.auth-form-group {
  margin-bottom: 20px;
}

.auth-form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.auth-input-wrapper {
  position: relative;
}

.auth-input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1rem;
  z-index: 1;
}

.auth-input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fafafa;
  box-sizing: border-box;
}

.auth-input:focus {
  outline: none;
  border-color: #3d7b47;
  background: white;
  box-shadow: 0 0 0 3px rgba(61, 123, 71, 0.1);
}

.auth-input:focus + .auth-input-icon {
  color: #3d7b47;
}

.auth-btn {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.auth-btn-primary {
  background: linear-gradient(135deg, #2c5530 0%, #3d7b47 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
}

.auth-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(44, 85, 48, 0.4);
}

.auth-btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.auth-btn-loading {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auth-divider {
  text-align: center;
  margin: 30px 0;
  position: relative;
  color: #6b7280;
  font-size: 0.9rem;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e7eb;
}

.auth-divider span {
  background: white;
  padding: 0 15px;
  position: relative;
  z-index: 1;
}

.auth-google-wrapper {
  margin-bottom: 30px;
}

.auth-switch {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.auth-switch p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.auth-switch-btn {
  background: none;
  border: none;
  color: #3d7b47;
  font-weight: 600;
  cursor: pointer;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.auth-switch-btn:hover {
  color: #2c5530;
  text-decoration: underline;
}

/* Select dropdown styling */
.auth-input[name="type"] {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 20px;
  cursor: pointer;
}

.auth-input[name="type"]:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232c5530' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-modal {
    max-width: 90vw;
    margin: 20px;
  }

  .auth-modal-header {
    padding: 30px 30px 20px;
  }

  .auth-modal-title {
    font-size: 1.5rem;
  }

  .auth-modal-subtitle {
    font-size: 0.9rem;
  }

  .auth-modal-body {
    padding: 30px;
  }

  .auth-input {
    padding: 12px 12px 12px 40px;
  }

  .auth-btn {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .auth-modal {
    max-width: 95vw;
    margin: 10px;
  }

  .auth-modal-header {
    padding: 25px 25px 15px;
  }

  .auth-modal-body {
    padding: 25px;
  }
}

/* Font Awesome Icons */
.fas {
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}
