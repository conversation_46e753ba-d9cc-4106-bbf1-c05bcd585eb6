import React, { useState } from 'react';
import jsPDF from 'jspdf';
import { useTranslation } from 'react-i18next';
import './AgreementPDF.css';

function AgreementPDF() {
  const { t } = useTranslation();

  const [agreementData, setAgreementData] = useState({
    teamLeader: {
      name: '<PERSON><PERSON>',
      village: 'Kumargram Village',
      clusterId: 'TL-001',
      phone: '+91-9876543212',
      email: '<EMAIL>'
    },
    artisan: {
      name: '',
      phone: '',
      address: '',
      experience: ''
    },
    order: {
      orderId: '',
      product: 'Handwoven Sarees',
      quantity: '',
      pricePerUnit: '',
      totalAmount: '',
      deadline: '',
      qualityStandards: t('qualityStandards')
    },
    terms: {
      advancePayment: '',
      finalPayment: '',
      deliveryTerms: t('deliveryTerms'),
      qualityCheck: t('qualityCheck'),
      penaltyClause: t('penaltyClause')
    }
  });

  const [showForm, setShowForm] = useState(false);

  const handleInputChange = (section, field, value) => {
    setAgreementData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const calculateTotalAmount = () => {
    const quantity = parseFloat(agreementData.order.quantity) || 0;
    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;
    const total = quantity * pricePerUnit;
    setAgreementData(prev => ({
      ...prev,
      order: {
        ...prev.order,
        totalAmount: total.toFixed(2)
      }
    }));
  };

  const generatePDF = () => {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    let yPosition = 30;

    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text(t('pdfTitle'), pageWidth / 2, yPosition, { align: 'center' });

    yPosition += 15;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(t('pdfSubtitle'), pageWidth / 2, yPosition, { align: 'center' });

    yPosition += 20;
    doc.setFontSize(10);
    doc.text(`${t('agreementDate')}: ${new Date().toLocaleDateString()}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(t('teamLeaderDetails'), margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`${t('name')}: ${agreementData.teamLeader.name}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('village')}: ${agreementData.teamLeader.village}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('clusterId')}: ${agreementData.teamLeader.clusterId}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('phone')}: ${agreementData.teamLeader.phone}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('email')}: ${agreementData.teamLeader.email}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(t('artisanDetails'), margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`${t('name')}: ${agreementData.artisan.name}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('phone')}: ${agreementData.artisan.phone}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('address')}: ${agreementData.artisan.address}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('experience')}: ${agreementData.artisan.experience}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(t('orderDetails'), margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`${t('orderId')}: ${agreementData.order.orderId}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('product')}: ${agreementData.order.product}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('quantity')}: ${agreementData.order.quantity}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('pricePerUnit')}: ₹${agreementData.order.pricePerUnit}`, margin, yPosition);
    yPosition += 6;
    doc.setFont('helvetica', 'bold');
    doc.text(`${t('totalAmount')}: ₹${agreementData.order.totalAmount}`, margin, yPosition);
    doc.setFont('helvetica', 'normal');
    yPosition += 6;
    doc.text(`${t('deadline')}: ${agreementData.order.deadline}`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('qualityStandards')}: ${agreementData.order.qualityStandards}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(t('paymentTerms'), margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const advance = parseFloat(agreementData.terms.advancePayment);
    const total = parseFloat(agreementData.order.totalAmount);
    const percent = total ? ((advance / total) * 100).toFixed(1) : '0';
    doc.text(`${t('advancePayment')}: ₹${agreementData.terms.advancePayment} (${percent}%)`, margin, yPosition);
    yPosition += 6;
    doc.text(`${t('finalPayment')}: ₹${agreementData.terms.finalPayment}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(t('termsAndConditions'), margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const terms = [
      `1. ${t('deliveryTerms')}: ${agreementData.terms.deliveryTerms}`,
      `2. ${t('qualityCheck')}: ${agreementData.terms.qualityCheck}`,
      `3. ${t('penaltyClause')}: ${agreementData.terms.penaltyClause}`,
      `4. ${t('term4')}`,
      `5. ${t('term5')}`,
      `6. ${t('term6')}`,
      `7. ${t('term7')}`
    ];
    terms.forEach(term => {
      doc.text(term, margin, yPosition, { maxWidth: pageWidth - 2 * margin });
      yPosition += 8;
    });
    yPosition += 20;

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text(t('signatures'), margin, yPosition);
    yPosition += 20;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`${t('teamLeader')}:`, margin, yPosition);
    doc.text(`${t('artisan')}:`, pageWidth - margin - 60, yPosition);
    yPosition += 15;

    doc.text('_____________________', margin, yPosition);
    doc.text('_____________________', pageWidth - margin - 60, yPosition);
    yPosition += 8;
    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);
    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);
    yPosition += 8;
    doc.text(`${t('date')}: _______________`, margin, yPosition);
    doc.text(`${t('date')}: _______________`, pageWidth - margin - 60, yPosition);

    yPosition += 20;
    doc.setFontSize(8);
    doc.setFont('helvetica', 'italic');
    doc.text(t('footerNote'), pageWidth / 2, yPosition, { align: 'center' });

    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);
  };

  return (
    <div>{/* UI rendering omitted for brevity */}</div>
  );
}

export default AgreementPDF;
