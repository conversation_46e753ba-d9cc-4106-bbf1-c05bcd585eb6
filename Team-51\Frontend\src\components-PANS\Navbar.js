import React from 'react';
import './Navbar.css';

function Navbar({ onSignIn, onSignUp }) {
  const username = localStorage.getItem('username');

  return (
    <nav className="custom-navbar">
      <div className="navbar-logo">NGO<span className="logo-dot">.</span></div>
      <button className="navbar-btn home" onClick={() => window.location.href = '/'}>Home</button>
      <div className="navbar-actions">
        {username ? (
          <>
            <span className="navbar-welcome">Welcome, <span className="navbar-username">{username}</span></span>
            <button className="navbar-btn logout" onClick={() => {
                localStorage.clear();
                window.location.reload();
              }}>Logout</button>
          </>
        ) : (
          <>
            <button className="navbar-btn" onClick={onSignIn}>Sign In</button>
            <button className="navbar-btn primary" onClick={onSignUp}>Sign Up</button>
          </>
        )}
      </div>
    </nav>
  );
}

export default Navbar;
