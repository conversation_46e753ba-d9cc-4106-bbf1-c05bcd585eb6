{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Header from './components/layout/Header';\nimport Footer from './components/layout/Footer';\nimport Hero from './components/pages/Hero';\nimport About from './components/pages/About';\nimport AuthForm from './components/common/AuthForm';\nimport TLDashboard from './components/dashboard/TLDashboard';\nimport { GoogleOAuthProvider } from '@react-oauth/google';\nimport './styles/globals.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [authModalOpen, setAuthModalOpen] = useState(false);\n  const [authMode, setAuthMode] = useState('signin'); // 'signin' or 'signup'\n  const [showDashboard, setShowDashboard] = useState(false);\n\n  // Pass these handlers to Header\n  const handleSignIn = () => {\n    setAuthMode('signin');\n    setAuthModalOpen(true);\n  };\n  const handleSignUp = () => {\n    setAuthMode('signup');\n    setAuthModalOpen(true);\n  };\n  const handleCloseModal = () => setAuthModalOpen(false);\n  return /*#__PURE__*/_jsxDEV(GoogleOAuthProvider, {\n    clientId: \"###\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        onSignIn: handleSignIn,\n        onSignUp: handleSignUp\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AuthForm, {\n        isOpen: authModalOpen,\n        onClose: handleCloseModal,\n        initialMode: authMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"LGAEIpZwr/9hD4ouhcg2Io+LZ1w=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Header", "Footer", "Hero", "About", "AuthForm", "TLDashboard", "GoogleOAuthProvider", "jsxDEV", "_jsxDEV", "App", "_s", "authModalOpen", "setAuthModalOpen", "authMode", "setAuthMode", "showDashboard", "setShowDashboard", "handleSignIn", "handleSignUp", "handleCloseModal", "clientId", "children", "className", "onSignIn", "onSignUp", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOpen", "onClose", "initialMode", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Header from './components/layout/Header';\r\nimport Footer from './components/layout/Footer';\r\nimport Hero from './components/pages/Hero';\r\nimport About from './components/pages/About';\r\nimport AuthForm from './components/common/AuthForm';\r\nimport TLDashboard from './components/dashboard/TLDashboard';\r\nimport { GoogleOAuthProvider } from '@react-oauth/google';\r\nimport './styles/globals.css';\r\n\r\nfunction App() {\r\n  const [authModalOpen, setAuthModalOpen] = useState(false);\r\n  const [authMode, setAuthMode] = useState('signin'); // 'signin' or 'signup'\r\n  const [showDashboard, setShowDashboard] = useState(false);\r\n\r\n  // Pass these handlers to Header\r\n  const handleSignIn = () => {\r\n    setAuthMode('signin');\r\n    setAuthModalOpen(true);\r\n  };\r\n  \r\n  const handleSignUp = () => {\r\n    setAuthMode('signup');\r\n    setAuthModalOpen(true);\r\n  };\r\n\r\n  const handleCloseModal = () => setAuthModalOpen(false);\r\n\r\n  return (\r\n    <GoogleOAuthProvider clientId=\"###\">\r\n      <div className=\"App\">\r\n        <Header onSignIn={handleSignIn} onSignUp={handleSignUp} />\r\n        <main>\r\n          <Hero />\r\n          <About />\r\n        </main>\r\n        <Footer />\r\n        <AuthForm isOpen={authModalOpen} onClose={handleCloseModal} initialMode={authMode} />\r\n      </div>\r\n    </GoogleOAuthProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzBH,WAAW,CAAC,QAAQ,CAAC;IACrBF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzBJ,WAAW,CAAC,QAAQ,CAAC;IACrBF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMO,gBAAgB,GAAGA,CAAA,KAAMP,gBAAgB,CAAC,KAAK,CAAC;EAEtD,oBACEJ,OAAA,CAACF,mBAAmB;IAACc,QAAQ,EAAC,KAAK;IAAAC,QAAA,eACjCb,OAAA;MAAKc,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBb,OAAA,CAACR,MAAM;QAACuB,QAAQ,EAAEN,YAAa;QAACO,QAAQ,EAAEN;MAAa;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1DpB,OAAA;QAAAa,QAAA,gBACEb,OAAA,CAACN,IAAI;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACRpB,OAAA,CAACL,KAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACPpB,OAAA,CAACP,MAAM;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVpB,OAAA,CAACJ,QAAQ;QAACyB,MAAM,EAAElB,aAAc;QAACmB,OAAO,EAAEX,gBAAiB;QAACY,WAAW,EAAElB;MAAS;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE1B;AAAClB,EAAA,CA/BQD,GAAG;AAAAuB,EAAA,GAAHvB,GAAG;AAiCZ,eAAeA,GAAG;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}