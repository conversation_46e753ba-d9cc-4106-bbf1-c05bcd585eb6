[{"C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js": "3", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js": "4", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js": "5", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js": "7", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js": "9", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\AgreementPDF.js": "11", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\GoogleTranslate.js": "12", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\TranslateButton.js": "13"}, {"size": 394, "mtime": 1752339457265, "results": "14", "hashOfConfig": "15"}, {"size": 3822, "mtime": 1752373455834, "results": "16", "hashOfConfig": "15"}, {"size": 3376, "mtime": 1752339457260, "results": "17", "hashOfConfig": "15"}, {"size": 2467, "mtime": 1752339457264, "results": "18", "hashOfConfig": "15"}, {"size": 3568, "mtime": 1752339457263, "results": "19", "hashOfConfig": "15"}, {"size": 3004, "mtime": 1752363069408, "results": "20", "hashOfConfig": "15"}, {"size": 5668, "mtime": 1752349920507, "results": "21", "hashOfConfig": "15"}, {"size": 20835, "mtime": 1752371839996, "results": "22", "hashOfConfig": "15"}, {"size": 17456, "mtime": 1752347599041, "results": "23", "hashOfConfig": "15"}, {"size": 2770, "mtime": 1752349920512, "results": "24", "hashOfConfig": "15"}, {"size": 14921, "mtime": 1752365376662, "results": "25", "hashOfConfig": "15"}, {"size": 1926, "mtime": 1752362959029, "results": "26", "hashOfConfig": "15"}, {"size": 3167, "mtime": 1752363003576, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "h97h8f", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js", ["67"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js", ["68", "69", "70", "71"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js", ["72", "73"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\AgreementPDF.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\GoogleTranslate.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\TranslateButton.js", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "74", "line": 82, "column": 1, "nodeType": null}, {"ruleId": "75", "severity": 1, "message": "76", "line": 15, "column": 15, "nodeType": "77", "endLine": 15, "endColumn": 58}, {"ruleId": "75", "severity": 1, "message": "76", "line": 18, "column": 15, "nodeType": "77", "endLine": 18, "endColumn": 58}, {"ruleId": "75", "severity": 1, "message": "76", "line": 21, "column": 15, "nodeType": "77", "endLine": 21, "endColumn": 58}, {"ruleId": "75", "severity": 1, "message": "76", "line": 24, "column": 15, "nodeType": "77", "endLine": 24, "endColumn": 58}, {"ruleId": "78", "severity": 1, "message": "79", "line": 1, "column": 27, "nodeType": "80", "messageId": "81", "endLine": 1, "endColumn": 36}, {"ruleId": "78", "severity": 1, "message": "82", "line": 150, "column": 10, "nodeType": "80", "messageId": "81", "endLine": 150, "endColumn": 24}, "Parsing error: Unexpected token (82:1)", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'showAssignForm' is assigned a value but never used."]