/* Team Leader Dashboard Styles */
.tl-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.dashboard-header {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-md);
  border-left: 6px solid var(--primary-green);
}

.leader-info {
  margin-bottom: 20px;
}

.dashboard-header h1 {
  font-size: 2.2rem;
  color: var(--primary-green);
  margin-bottom: 20px;
  text-align: center;
}

.leader-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.leader-name {
  flex: 1;
  min-width: 250px;
}

.leader-name h2 {
  font-size: 1.8rem;
  color: var(--gray-800);
  margin-bottom: 5px;
  font-weight: 700;
}

.leader-id {
  background: var(--primary-green);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.leader-meta {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 120px;
}

.meta-label {
  font-size: 0.9rem;
  color: var(--gray-500);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 5px;
}

.meta-value {
  font-size: 1.1rem;
  color: var(--gray-800);
  font-weight: 600;
}

.welcome-message {
  font-size: 1.1rem;
  color: var(--gray-600);
  text-align: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--gray-200);
}

/* Tabs */
.dashboard-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: var(--shadow-sm);
}

.tab-btn {
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: var(--gray-600);
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: var(--primary-green);
  color: white;
  box-shadow: var(--shadow-sm);
}

.tab-btn:hover:not(.active) {
  background: var(--gray-100);
  color: var(--primary-green);
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Income Section */
.income-section h2 {
  color: var(--gray-800);
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.income-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.income-card {
  background: white;
  padding: 25px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  border-left: 4px solid var(--gray-300);
  transition: transform 0.3s ease;
}

.income-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.income-card.primary {
  border-left-color: var(--primary-green);
  background: linear-gradient(135deg, #f0f9f0 0%, #ffffff 100%);
}

.income-card h3 {
  font-size: 1rem;
  color: var(--gray-600);
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.income-card .amount {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--primary-green);
  margin-bottom: 5px;
}

.income-card .subtitle {
  font-size: 0.9rem;
  color: var(--gray-500);
}

/* Monthly Breakdown */
.breakdown-section h2 {
  color: var(--gray-800);
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.breakdown-chart {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 300px;
  gap: 15px;
}

.month-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
  position: relative;
}

.bar {
  width: 40px;
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
  border-radius: 8px 8px 0 0;
  margin-bottom: 10px;
  min-height: 20px;
  transition: all 0.3s ease;
}

.bar:hover {
  background: linear-gradient(135deg, var(--primary-green-light) 0%, var(--primary-green-lighter) 100%);
  transform: scaleY(1.05);
}

.month-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 5px;
}

.income-label {
  font-size: 0.9rem;
  color: var(--primary-green);
  font-weight: 600;
}

.orders-label {
  font-size: 0.8rem;
  color: var(--gray-500);
}

/* Delivery Stats */
.delivery-section h2,
.artisan-summary-section h2 {
  color: var(--gray-800);
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.delivery-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  text-align: center;
  border-top: 4px solid;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-card.success {
  border-top-color: #10b981;
}

.stat-card.warning {
  border-top-color: #f59e0b;
}

.stat-card.info {
  border-top-color: #3b82f6;
}

.stat-card.primary {
  border-top-color: var(--primary-green);
}

.stat-card h3 {
  font-size: 1rem;
  color: var(--gray-600);
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card p {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-800);
}

/* Artisans Section */
.artisans-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.artisans-section h2 {
  color: var(--gray-800);
  margin: 0;
  font-size: 1.8rem;
}

.add-artisan-btn {
  background: var(--primary-green);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.add-artisan-btn:hover {
  background: var(--primary-green-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Add Artisan Form */
.add-artisan-form {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: var(--shadow-md);
  border-left: 4px solid var(--primary-green);
}

.add-artisan-form h3 {
  color: var(--gray-800);
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 2px solid var(--gray-200);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(44, 85, 48, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.cancel-btn,
.submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.cancel-btn {
  background: var(--gray-200);
  color: var(--gray-700);
}

.cancel-btn:hover {
  background: var(--gray-300);
}

.submit-btn {
  background: var(--primary-green);
  color: white;
}

.submit-btn:hover {
  background: var(--primary-green-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.artisans-table {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.artisans-table table {
  width: 100%;
  border-collapse: collapse;
}

.artisans-table th {
  background: var(--primary-green);
  color: white;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

.artisans-table td {
  padding: 15px;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-700);
}

.artisans-table tr:hover {
  background: var(--gray-50);
}

.performance-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.performance-score {
  font-weight: 600;
  min-width: 40px;
}

.performance-bar {
  flex: 1;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
}

.performance-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Orders Section */
.orders-section h2 {
  color: var(--gray-800);
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.order-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  border-left: 4px solid var(--primary-green);
  transition: transform 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.order-card.completed {
  border-left-color: #10b981;
  opacity: 0.8;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.order-header h3 {
  color: var(--gray-800);
  font-size: 1.1rem;
}

.order-status {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.order-product {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 8px;
}

.order-quantity,
.order-deadline,
.order-completed {
  font-size: 0.9rem;
  color: var(--gray-600);
  margin-bottom: 5px;
}

/* Analytics Section */
.analytics-section h2 {
  color: var(--gray-800);
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.analytics-card {
  background: white;
  padding: 25px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: transform 0.3s ease;
}

.analytics-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.analytics-card h3 {
  font-size: 1rem;
  color: var(--gray-600);
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.analytics-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-green);
  margin-bottom: 10px;
}

.analytics-trend {
  font-size: 0.9rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
}

.analytics-trend.positive {
  color: #10b981;
  background: #ecfdf5;
}

.analytics-trend.neutral {
  color: var(--gray-600);
  background: var(--gray-100);
}

/* Toggle Switch Styles */
.switch {
  position: relative;
  display: inline-block;
  width: 46px;
  height: 26px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ef4444; /* danger by default */
  transition: .4s;
  border-radius: 26px;
}
.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}
input:checked + .slider {
  background-color: #10b981; /* success */
}
input:checked + .slider:before {
  transform: translateX(20px);
}
.slider.danger {
  background-color: #ef4444 !important;
}
.slider.success {
  background-color: #10b981 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tl-dashboard {
    padding: 15px;
  }

  .dashboard-header {
    padding: 20px;
  }

  .leader-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .leader-name {
    min-width: auto;
    width: 100%;
    text-align: center;
  }

  .leader-name h2 {
    font-size: 1.5rem;
  }

  .leader-meta {
    justify-content: space-around;
    width: 100%;
    gap: 15px;
  }

  .meta-item {
    min-width: 100px;
  }

  .artisans-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .add-artisan-form {
    padding: 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .form-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }

  .dashboard-tabs {
    flex-wrap: wrap;
    gap: 5px;
  }

  .tab-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .income-cards,
  .delivery-stats,
  .orders-grid,
  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .breakdown-chart {
    padding: 20px;
    height: 250px;
  }

  .artisans-table {
    overflow-x: auto;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }
}

/* Error and Loading Messages */
.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #fecaca;
}

.error-message button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: inherit;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #64748b;
  font-size: 1.1rem;
}

.no-orders {
  text-align: center;
  padding: 2rem;
  color: #64748b;
  font-style: italic;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px dashed #cbd5e1;
}

/* Order Card Enhancements */
.order-card .order-date {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0.5rem 0;
}

.order-card .order-remark {
  background: #f1f5f9;
  padding: 0.5rem;
  border-radius: 4px;
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #475569;
}

.quantity-breakdown {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #e2e8f0;
}

.quantity-breakdown h4 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 0.9rem;
}

.assignment-item {
  display: flex;
  justify-content: space-between;
  padding: 0.25rem 0;
  font-size: 0.85rem;
  color: #374151;
}

.assignment-item:not(:last-child) {
  border-bottom: 1px solid #e2e8f0;
}

.artisan-assignments {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.artisan-assignments h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.artisan-assignment-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.artisan-assignment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 4px;
  font-size: 0.85rem;
}

.artisan-assignment-item span:first-child {
  font-weight: 500;
  color: #374151;
}

.artisan-assignment-item span:nth-child(2) {
  color: #6b7280;
}

.artisan-assignment-item .status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.artisan-assignment-item .status-badge.assigned {
  background: #dbeafe;
  color: #1e40af;
}

.artisan-assignment-item .status-badge.in-progress {
  background: #fef3c7;
  color: #d97706;
}

.artisan-assignment-item .status-badge.completed {
  background: #d1fae5;
  color: #059669;
}

.artisan-assignment-item .completed-info {
  color: #059669;
  font-size: 0.8rem;
  font-weight: 500;
  background: #f0fdf4;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  border: 1px solid #bbf7d0;
}

.completion-summary {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-md);
  border-left: 4px solid #10b981;
}

.completion-summary h3 {
  color: #1f2937;
  margin-bottom: 16px;
  font-size: 1.2rem;
  font-weight: 600;
}

.completion-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.completion-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.completion-stat .stat-label {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

.completion-stat .stat-value {
  font-size: 1rem;
  color: #1f2937;
  font-weight: 600;
}

.artisan-performance-history {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-md);
  border-left: 4px solid #3b82f6;
}

.artisan-performance-history h3 {
  color: #1f2937;
  margin-bottom: 16px;
  font-size: 1.2rem;
  font-weight: 600;
}

.artisan-history-table {
  overflow-x: auto;
}

.artisan-history-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.artisan-history-table th {
  background: #f8fafc;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.artisan-history-table td {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
}

.artisan-history-table tr:hover {
  background: #f9fafb;
}

.completion-rate {
  font-weight: 600;
  font-size: 0.9rem;
}

/* Order Actions */
.order-actions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.update-progress-btn {
  background: #2c5530;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.update-progress-btn:hover {
  background: #1e3a1f;
  transform: translateY(-1px);
}

.assign-artisans-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
  width: 100%;
}

.assign-artisans-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Success Message */
.success-message {
  background: #f0fdf4;
  color: #16a34a;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #bbf7d0;
}

.success-message button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: inherit;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  color: #1e293b;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f1f5f9;
  color: #1e293b;
}

/* Update Form Styles */
.update-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.update-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.update-form .form-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.update-form .form-group input,
.update-form .form-group select,
.update-form .form-group textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.update-form .form-group input:focus,
.update-form .form-group select:focus,
.update-form .form-group textarea:focus {
  outline: none;
  border-color: #2c5530;
  box-shadow: 0 0 0 3px rgba(44, 85, 48, 0.1);
}

.disabled-input {
  background: #f1f5f9;
  color: #64748b;
  cursor: not-allowed;
}

.update-form .form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.update-form .cancel-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.update-form .cancel-btn:hover {
  background: #4b5563;
}

.update-form .submit-btn {
  background: #2c5530;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.update-form .submit-btn:hover:not(:disabled) {
  background: #1e3a1f;
}

.update-form .submit-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}
