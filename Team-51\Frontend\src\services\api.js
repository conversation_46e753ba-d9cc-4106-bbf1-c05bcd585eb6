import axios from 'axios';

// Base API URL
const API_BASE_URL = 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, clear auth data
      localStorage.removeItem('token');
      localStorage.removeItem('username');
      localStorage.removeItem('user_id');
      localStorage.removeItem('user_type');
      localStorage.removeItem('verified');
      window.location.reload();
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  signup: (userData) => api.post('/auth/signup', userData),
  signin: (credentials) => api.post('/auth/signin', credentials),
  googleAuth: (googleData) => api.post('/auth/google-auth', googleData),
  enroll: (enrollmentData) => api.post('/auth/enroll', enrollmentData),
};

// User utility functions
export const userUtils = {
  isAuthenticated: () => {
    const token = localStorage.getItem('token');
    const username = localStorage.getItem('username');
    return !!(token && username);
  },
  
  getUserInfo: () => {
    return {
      username: localStorage.getItem('username'),
      userId: localStorage.getItem('user_id'),
      userType: localStorage.getItem('user_type'),
      verified: localStorage.getItem('verified') === 'true',
      token: localStorage.getItem('token'),
    };
  },
  
  setUserInfo: (userData) => {
    localStorage.setItem('token', userData.token);
    localStorage.setItem('username', userData.username);
    localStorage.setItem('user_id', userData.user_id);
    localStorage.setItem('user_type', userData.type);
    localStorage.setItem('verified', userData.verified);
  },
  
  clearUserInfo: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('username');
    localStorage.removeItem('user_id');
    localStorage.removeItem('user_type');
    localStorage.removeItem('verified');
  },
  
  canAccessDashboard: () => {
    const userInfo = userUtils.getUserInfo();
    return userInfo.verified && 
           ['team leader', 'admin-pan', 'admin'].includes(userInfo.userType);
  }
};

export default api;
