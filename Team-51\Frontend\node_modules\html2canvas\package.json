{"title": "html2canvas", "name": "html2canvas", "description": "Screenshots with JavaScript", "main": "dist/html2canvas.js", "module": "dist/html2canvas.esm.js", "typings": "dist/types/index.d.ts", "browser": "dist/html2canvas.js", "version": "1.4.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://hertzen.com"}, "engines": {"node": ">=8.0.0"}, "repository": {"type": "git", "url": "**************:niklasvh/html2canvas.git"}, "bugs": {"url": "https://github.com/niklasvh/html2canvas/issues"}, "devDependencies": {"@babel/cli": "^7.4.3", "@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "@babel/preset-flow": "^7.0.0", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/chai": "^4.1.7", "@types/express": "^4.17.13", "@types/glob": "^7.1.1", "@types/jest": "^26.0.24", "@types/jest-image-snapshot": "^4.3.1", "@types/karma": "^6.3.0", "@types/mocha": "^8.2.3", "@types/node": "^16.3.1", "@types/platform": "^1.3.4", "@types/promise-polyfill": "^6.0.3", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "appium-ios-simulator": "^3.10.0", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-dev-expression": "^0.2.1", "base64-arraybuffer": "1.0.1", "body-parser": "^1.19.0", "chai": "4.1.1", "chromeless": "^1.5.2", "cors": "^2.8.5", "es6-promise": "^4.2.8", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "3.4.0", "express": "^4.17.1", "filenamify-url": "1.0.0", "glob": "7.1.3", "html2canvas-proxy": "1.0.1", "jest": "^27.0.6", "jest-image-snapshot": "^4.5.1", "jquery": "^3.5.1", "js-polyfills": "^0.1.42", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.0", "karma-ie-launcher": "^1.0.0", "karma-junit-reporter": "^2.0.1", "karma-mocha": "^2.0.1", "karma-safarinative-launcher": "^1.1.0", "karma-sauce-launcher": "^2.0.2", "mocha": "^9.0.2", "node-simctl": "^5.3.0", "platform": "^1.3.6", "prettier": "^2.3.2", "replace-in-file": "^3.0.0", "rimraf": "^3.0.2", "rollup": "^2.53.1", "rollup-plugin-sourcemaps": "^0.6.3", "serve-index": "^1.9.1", "slash": "1.0.0", "standard-version": "^8.0.2", "ts-jest": "^27.0.3", "ts-loader": "^8.3.0", "ts-node": "^10.1.0", "tslib": "^2.3.0", "typescript": "^4.3.5", "uglify-js": "^3.13.10", "uglifyjs-webpack-plugin": "^2.2.0", "webpack": "^4.46.0", "webpack-cli": "^3.3.12", "yargs": "^17.0.1"}, "scripts": {"prebuild": "rimraf dist/ && rimraf build/ && mkdirp dist && mkdirp build", "build": "tsc --module commonjs && rollup -c rollup.config.ts && npm run build:create-reftest-list && npm run build:testrunner && npm run build:minify", "build:testrunner": "rollup -c tests/rollup.config.ts", "build:minify": "uglifyjs --compress --comments /^!/ -o dist/html2canvas.min.js --mangle -- dist/html2canvas.js", "build:reftest-result-list": "ts-node scripts/create-reftest-result-list.ts", "build:create-reftest-list": "ts-node scripts/create-reftest-list.ts tests/reftests/ignore.txt build/reftests.ts", "build:reftest-preview": "webpack --config www/webpack.config.js", "release": "standard-version", "format": "prettier --write \"{src,www/src,tests,scripts}/**/*.ts\"", "lint": "eslint src/**/*.ts --max-warnings 0", "test": "npm run lint && npm run unittest && npm run karma", "unittest": "jest", "reftests-diff": "mkdirp tmp/snapshots && jest --roots=tests --testMatch=**/reftest-diff.ts", "karma": "ts-node tests/karma", "watch": "rollup -c rollup.config.ts -w", "watch:unittest": "mocha --require ts-node/register --watch-extensions ts -w src/**/__tests__/*.ts", "start": "ts-node tests/server --port=8080 --cors=8081"}, "homepage": "https://html2canvas.hertzen.com", "license": "MIT", "dependencies": {"css-line-break": "^2.1.0", "text-segmentation": "^1.0.3"}}