// models/TeamLeader.js
const mongoose = require('mongoose');

const teamLeaderSchema = new mongoose.Schema({
  tl_id: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  cluster_id: {
    type: String,
    required: true
  },
  village_name: {
    type: String,
    required: true
  },
  current_orderid: {
    type: String,
    default: null
  },
  artisans: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Artisan'
  }]
}, { timestamps: true });

module.exports = mongoose.model('TeamLeader', teamLeaderSchema);