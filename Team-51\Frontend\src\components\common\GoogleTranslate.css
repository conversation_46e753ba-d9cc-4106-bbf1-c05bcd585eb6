/* Google Translate Widget Styles */
.google-translate-container {
  position: relative;
  display: inline-block;
  margin-left: 15px;
}

.translate-header {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: var(--gray-700);
  margin-bottom: 5px;
  cursor: pointer;
}

.translate-icon {
  font-size: 1.1rem;
}

.translate-label {
  font-weight: 500;
}

.google-translate-widget {
  position: relative;
  z-index: 1000;
}

/* Override Google Translate default styles */
.goog-te-gadget {
  font-family: inherit !important;
  font-size: 0.9rem !important;
  color: var(--gray-700) !important;
  background: white !important;
  border: 1px solid var(--gray-300) !important;
  border-radius: 6px !important;
  padding: 5px 8px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.goog-te-gadget-simple {
  background-color: white !important;
  border: 1px solid var(--gray-300) !important;
  border-radius: 6px !important;
  font-size: 0.9rem !important;
  display: inline-block !important;
  cursor: pointer !important;
  zoom: 1 !important;
}

.goog-te-gadget-simple .goog-te-menu-value {
  color: var(--gray-700) !important;
  font-family: inherit !important;
  font-size: 0.9rem !important;
  padding: 5px 8px !important;
}

.goog-te-gadget-simple .goog-te-menu-value:hover {
  text-decoration: none !important;
  background-color: var(--gray-50) !important;
}

.goog-te-gadget-simple .goog-te-menu-value span {
  color: var(--gray-700) !important;
  font-family: inherit !important;
}

.goog-te-gadget-simple .goog-te-menu-value span:first-child {
  display: none !important; /* Hide "Select Language" text */
}

.goog-te-gadget-icon {
  background-image: none !important;
  margin-right: 5px !important;
}

/* Hide Google Translate banner */
.goog-te-banner-frame {
  display: none !important;
}

.goog-te-menu-frame {
  max-height: 400px !important;
  overflow-y: auto !important;
  border: 1px solid var(--gray-300) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Style the dropdown menu */
.goog-te-menu2 {
  background: white !important;
  border: none !important;
  box-shadow: none !important;
  max-height: 300px !important;
  overflow-y: auto !important;
}

.goog-te-menu2-item {
  font-family: inherit !important;
  font-size: 0.9rem !important;
  padding: 8px 12px !important;
  border-bottom: 1px solid var(--gray-100) !important;
  color: var(--gray-700) !important;
}

.goog-te-menu2-item:hover {
  background-color: var(--gray-50) !important;
  color: var(--primary-green) !important;
}

.goog-te-menu2-item-selected {
  background-color: var(--primary-green) !important;
  color: white !important;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .google-translate-container {
    margin-left: 10px;
  }
  
  .translate-header {
    font-size: 0.8rem;
  }
  
  .goog-te-gadget-simple {
    font-size: 0.8rem !important;
  }
  
  .goog-te-gadget-simple .goog-te-menu-value {
    font-size: 0.8rem !important;
    padding: 4px 6px !important;
  }
}

/* Hide Google branding */
.goog-logo-link {
  display: none !important;
}

.goog-te-gadget .goog-te-combo {
  margin: 0 !important;
}

/* Custom translate button styles */
.translate-button {
  background: linear-gradient(135deg, var(--primary-green) 0%, #2d5a31 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.translate-button:hover {
  background: linear-gradient(135deg, #2d5a31 0%, var(--primary-green) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.translate-button:active {
  transform: translateY(0);
}

.translate-button .icon {
  font-size: 1rem;
}

/* Language indicator */
.current-language {
  font-size: 0.8rem;
  color: var(--gray-500);
  margin-top: 2px;
  text-align: center;
}

/* Floating translate widget */
.floating-translate {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 10px;
  border: 1px solid var(--gray-200);
}

.floating-translate .translate-header {
  margin-bottom: 8px;
  justify-content: center;
}

/* Animation for translate widget */
.translate-widget-enter {
  opacity: 0;
  transform: scale(0.9);
}

.translate-widget-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.translate-widget-exit {
  opacity: 1;
  transform: scale(1);
}

.translate-widget-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
}
