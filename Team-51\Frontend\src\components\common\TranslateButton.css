/* Floating Translate Button Styles */
.translate-widget-container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  font-family: inherit;
}

.floating-translate-btn {
  background: linear-gradient(135deg, var(--primary-green) 0%, #2d5a31 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 20px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
  min-width: 120px;
  justify-content: center;
}

.floating-translate-btn:hover {
  background: linear-gradient(135deg, #2d5a31 0%, var(--primary-green) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(44, 85, 48, 0.4);
}

.floating-translate-btn:active {
  transform: translateY(0);
}

.translate-icon {
  font-size: 1.2rem;
  animation: rotate 2s linear infinite;
}

.translate-text {
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Current Language Indicator */
.current-language-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff6b35;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Translate Popup */
.translate-popup {
  position: absolute;
  bottom: 70px;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--gray-200);
  min-width: 300px;
  max-width: 350px;
  animation: slideUp 0.3s ease-out;
  z-index: 1001;
}

.translate-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: 12px 12px 0 0;
}

.translate-popup-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--gray-800);
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--gray-500);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

.translate-popup-content {
  padding: 20px;
}

.translate-popup-footer {
  padding: 10px 20px;
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: 0 0 12px 12px;
  text-align: center;
}

.translate-popup-footer p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--gray-500);
}

/* Overlay */
.translate-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .translate-widget-container {
    bottom: 20px;
    right: 20px;
  }

  .floating-translate-btn {
    padding: 10px 16px;
    font-size: 0.8rem;
    min-width: 100px;
  }

  .translate-icon {
    font-size: 1rem;
  }

  .translate-popup {
    bottom: 60px;
    right: -20px;
    min-width: 280px;
    max-width: 300px;
  }

  .current-language-indicator {
    font-size: 0.6rem;
    padding: 1px 4px;
    max-width: 50px;
  }
}

@media (max-width: 480px) {
  .translate-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 12px 12px 0 0;
    min-width: auto;
    max-width: none;
    animation: slideUpMobile 0.3s ease-out;
  }

  .floating-translate-btn {
    padding: 8px 12px;
    font-size: 0.75rem;
    min-width: 80px;
  }

  .translate-text {
    display: none;
  }

  .floating-translate-btn {
    border-radius: 50%;
    width: 50px;
    height: 50px;
    padding: 0;
  }
}

@keyframes slideUpMobile {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Language Grid for better mobile experience */
.language-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-top: 15px;
}

.language-option {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: 8px;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: var(--gray-700);
}

.language-option:hover {
  background: var(--primary-green);
  color: white;
  border-color: var(--primary-green);
}

.language-option.active {
  background: var(--primary-green);
  color: white;
  border-color: var(--primary-green);
}

/* Pulse animation for attention */
.floating-translate-btn.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
  }
  50% {
    box-shadow: 0 4px 12px rgba(44, 85, 48, 0.6), 0 0 0 10px rgba(44, 85, 48, 0.1);
  }
  100% {
    box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
  }
}
