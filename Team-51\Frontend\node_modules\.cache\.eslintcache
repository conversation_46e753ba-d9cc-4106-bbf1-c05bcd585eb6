[{"C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js": "3", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js": "4", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js": "5", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js": "7", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js": "9", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\AgreementPDF.js": "11"}, {"size": 394, "mtime": 1752339457265, "results": "12", "hashOfConfig": "13"}, {"size": 3172, "mtime": 1752360348170, "results": "14", "hashOfConfig": "13"}, {"size": 3376, "mtime": 1752339457260, "results": "15", "hashOfConfig": "13"}, {"size": 2467, "mtime": 1752339457264, "results": "16", "hashOfConfig": "13"}, {"size": 3568, "mtime": 1752339457263, "results": "17", "hashOfConfig": "13"}, {"size": 2870, "mtime": 1752349920510, "results": "18", "hashOfConfig": "13"}, {"size": 5668, "mtime": 1752349920507, "results": "19", "hashOfConfig": "13"}, {"size": 20841, "mtime": 1752360090597, "results": "20", "hashOfConfig": "13"}, {"size": 17456, "mtime": 1752347599041, "results": "21", "hashOfConfig": "13"}, {"size": 2770, "mtime": 1752349920512, "results": "22", "hashOfConfig": "13"}, {"size": 7188, "mtime": 1752362411580, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "h97h8f", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 43, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js", ["57", "58", "59", "60"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js", ["61", "62"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\AgreementPDF.js", ["63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110"], [], {"ruleId": "111", "severity": 1, "message": "112", "line": 15, "column": 15, "nodeType": "113", "endLine": 15, "endColumn": 58}, {"ruleId": "111", "severity": 1, "message": "112", "line": 18, "column": 15, "nodeType": "113", "endLine": 18, "endColumn": 58}, {"ruleId": "111", "severity": 1, "message": "112", "line": 21, "column": 15, "nodeType": "113", "endLine": 21, "endColumn": 58}, {"ruleId": "111", "severity": 1, "message": "112", "line": 24, "column": 15, "nodeType": "113", "endLine": 24, "endColumn": 58}, {"ruleId": "114", "severity": 1, "message": "115", "line": 1, "column": 27, "nodeType": "116", "messageId": "117", "endLine": 1, "endColumn": 36}, {"ruleId": "114", "severity": 1, "message": "118", "line": 150, "column": 10, "nodeType": "116", "messageId": "117", "endLine": 150, "endColumn": 24}, {"ruleId": "119", "severity": 2, "message": "120", "line": 28, "column": 25, "nodeType": "116", "messageId": "121", "endLine": 28, "endColumn": 26}, {"ruleId": "119", "severity": 2, "message": "120", "line": 33, "column": 22, "nodeType": "116", "messageId": "121", "endLine": 33, "endColumn": 23}, {"ruleId": "119", "severity": 2, "message": "120", "line": 34, "column": 21, "nodeType": "116", "messageId": "121", "endLine": 34, "endColumn": 22}, {"ruleId": "119", "severity": 2, "message": "120", "line": 35, "column": 22, "nodeType": "116", "messageId": "121", "endLine": 35, "endColumn": 23}, {"ruleId": "114", "severity": 1, "message": "122", "line": 39, "column": 10, "nodeType": "116", "messageId": "117", "endLine": 39, "endColumn": 18}, {"ruleId": "114", "severity": 1, "message": "123", "line": 39, "column": 20, "nodeType": "116", "messageId": "117", "endLine": 39, "endColumn": 31}, {"ruleId": "114", "severity": 1, "message": "124", "line": 41, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 41, "endColumn": 26}, {"ruleId": "114", "severity": 1, "message": "125", "line": 51, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 51, "endColumn": 29}, {"ruleId": "114", "severity": 1, "message": "126", "line": 64, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 64, "endColumn": 20}, {"ruleId": "119", "severity": 2, "message": "120", "line": 72, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 72, "endColumn": 15}, {"ruleId": "119", "severity": 2, "message": "120", "line": 77, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 77, "endColumn": 15}, {"ruleId": "119", "severity": 2, "message": "120", "line": 81, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 81, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 86, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 86, "endColumn": 15}, {"ruleId": "119", "severity": 2, "message": "120", "line": 90, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 90, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 92, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 92, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 94, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 94, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 96, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 96, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 98, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 98, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 103, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 103, "endColumn": 15}, {"ruleId": "119", "severity": 2, "message": "120", "line": 107, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 107, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 109, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 109, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 111, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 111, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 113, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 113, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 118, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 118, "endColumn": 15}, {"ruleId": "119", "severity": 2, "message": "120", "line": 122, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 122, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 124, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 124, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 126, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 126, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 128, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 128, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 131, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 131, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 134, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 134, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 136, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 136, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 141, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 141, "endColumn": 15}, {"ruleId": "119", "severity": 2, "message": "120", "line": 148, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 148, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 150, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 150, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 155, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 155, "endColumn": 15}, {"ruleId": "119", "severity": 2, "message": "120", "line": 160, "column": 13, "nodeType": "116", "messageId": "121", "endLine": 160, "endColumn": 14}, {"ruleId": "119", "severity": 2, "message": "120", "line": 161, "column": 13, "nodeType": "116", "messageId": "121", "endLine": 161, "endColumn": 14}, {"ruleId": "119", "severity": 2, "message": "120", "line": 162, "column": 13, "nodeType": "116", "messageId": "121", "endLine": 162, "endColumn": 14}, {"ruleId": "119", "severity": 2, "message": "120", "line": 163, "column": 13, "nodeType": "116", "messageId": "121", "endLine": 163, "endColumn": 14}, {"ruleId": "119", "severity": 2, "message": "120", "line": 164, "column": 13, "nodeType": "116", "messageId": "121", "endLine": 164, "endColumn": 14}, {"ruleId": "119", "severity": 2, "message": "120", "line": 165, "column": 13, "nodeType": "116", "messageId": "121", "endLine": 165, "endColumn": 14}, {"ruleId": "119", "severity": 2, "message": "120", "line": 166, "column": 13, "nodeType": "116", "messageId": "121", "endLine": 166, "endColumn": 14}, {"ruleId": "119", "severity": 2, "message": "120", "line": 176, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 176, "endColumn": 15}, {"ruleId": "119", "severity": 2, "message": "120", "line": 181, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 181, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 182, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 182, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 191, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 191, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 192, "column": 17, "nodeType": "116", "messageId": "121", "endLine": 192, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 197, "column": 14, "nodeType": "116", "messageId": "121", "endLine": 197, "endColumn": 15}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'showAssignForm' is assigned a value but never used.", "no-undef", "'t' is not defined.", "undef", "'showForm' is assigned a value but never used.", "'setShowForm' is assigned a value but never used.", "'handleInputChange' is assigned a value but never used.", "'calculateTotalAmount' is assigned a value but never used.", "'generatePDF' is assigned a value but never used."]