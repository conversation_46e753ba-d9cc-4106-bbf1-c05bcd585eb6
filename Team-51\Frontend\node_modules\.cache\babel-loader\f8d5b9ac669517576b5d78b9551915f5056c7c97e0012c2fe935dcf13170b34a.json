{"ast": null, "code": "let i18nInstance;\nexport const setI18n = instance => {\n  i18nInstance = instance;\n};\nexport const getI18n = () => i18nInstance;", "map": {"version": 3, "names": ["i18nInstance", "setI18n", "instance", "getI18n"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/node_modules/react-i18next/dist/es/i18nInstance.js"], "sourcesContent": ["let i18nInstance;\nexport const setI18n = instance => {\n  i18nInstance = instance;\n};\nexport const getI18n = () => i18nInstance;"], "mappings": "AAAA,IAAIA,YAAY;AAChB,OAAO,MAAMC,OAAO,GAAGC,QAAQ,IAAI;EACjCF,YAAY,GAAGE,QAAQ;AACzB,CAAC;AACD,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAMH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}