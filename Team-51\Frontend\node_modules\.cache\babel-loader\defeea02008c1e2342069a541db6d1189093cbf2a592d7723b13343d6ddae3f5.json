{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\components\\\\layout\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { userUtils } from '../../services/api';\nimport GoogleTranslate from '../common/GoogleTranslate';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Header({\n  onSignIn\n}) {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const userInfo = userUtils.getUserInfo();\n  const {\n    username,\n    userType,\n    verified\n  } = userInfo;\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  const handleLogout = () => {\n    userUtils.clearUserInfo();\n    window.location.reload();\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/logo192.png\",\n          alt: \"PANS NGO Logo\",\n          className: \"header-logo-img\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-logo-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"PANS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-tagline\",\n            children: \"Empowering Communities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: `header-nav ${isMenuOpen ? 'header-nav-open' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"header-nav-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#home\",\n              className: \"header-nav-link\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#about\",\n              className: \"header-nav-link\",\n              children: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#programs\",\n              className: \"header-nav-link\",\n              children: \"Programs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#get-involved\",\n              className: \"header-nav-link\",\n              children: \"Get Involved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#contact\",\n              className: \"header-nav-link\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: username ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-user-menu\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"header-welcome\",\n              children: [\"Welcome, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this), userType && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"header-user-type\",\n              children: userType.charAt(0).toUpperCase() + userType.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 19\n            }, this), !verified && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"header-verification-status\",\n              children: \"\\u26A0\\uFE0F Account Pending Verification\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"header-btn header-btn-logout\",\n            onClick: handleLogout,\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-auth-buttons\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"header-btn header-btn-primary\",\n            onClick: onSignIn,\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"header-menu-toggle\",\n        onClick: toggleMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "userUtils", "GoogleTranslate", "jsxDEV", "_jsxDEV", "Header", "onSignIn", "_s", "isMenuOpen", "setIsMenuOpen", "userInfo", "getUserInfo", "username", "userType", "verified", "toggleMenu", "handleLogout", "clearUserInfo", "window", "location", "reload", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "char<PERSON>t", "toUpperCase", "slice", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/components/layout/Header.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { userUtils } from '../../services/api';\r\nimport GoogleTranslate from '../common/GoogleTranslate';\r\nimport './Header.css';\r\n\r\nfunction Header({ onSignIn }) {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const userInfo = userUtils.getUserInfo();\r\n  const { username, userType, verified } = userInfo;\r\n\r\n  const toggleMenu = () => {\r\n    setIsMenuOpen(!isMenuOpen);\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    userUtils.clearUserInfo();\r\n    window.location.reload();\r\n  };\r\n\r\n  return (\r\n    <header className=\"header\">\r\n      <div className=\"header-container\">\r\n        <div className=\"header-logo\">\r\n          <img \r\n            src=\"/logo192.png\" \r\n            alt=\"PANS NGO Logo\" \r\n            className=\"header-logo-img\"\r\n          />\r\n          <div className=\"header-logo-text\">\r\n            <h1>PANS</h1>\r\n            <span className=\"header-tagline\">Empowering Communities</span>\r\n          </div>\r\n        </div>\r\n\r\n        <nav className={`header-nav ${isMenuOpen ? 'header-nav-open' : ''}`}>\r\n          <ul className=\"header-nav-list\">\r\n            <li><a href=\"#home\" className=\"header-nav-link\">Home</a></li>\r\n            <li><a href=\"#about\" className=\"header-nav-link\">About Us</a></li>\r\n            <li><a href=\"#programs\" className=\"header-nav-link\">Programs</a></li>\r\n            <li><a href=\"#get-involved\" className=\"header-nav-link\">Get Involved</a></li>\r\n            <li><a href=\"#contact\" className=\"header-nav-link\">Contact</a></li>\r\n          </ul>\r\n        </nav>\r\n\r\n        <div className=\"header-actions\">\r\n          {username ? (\r\n            <div className=\"header-user-menu\">\r\n              <div className=\"header-user-info\">\r\n                <span className=\"header-welcome\">\r\n                  Welcome, <strong>{username}</strong>\r\n                </span>\r\n                {userType && (\r\n                  <span className=\"header-user-type\">\r\n                    {userType.charAt(0).toUpperCase() + userType.slice(1)}\r\n                  </span>\r\n                )}\r\n                {!verified && (\r\n                  <span className=\"header-verification-status\">\r\n                    ⚠️ Account Pending Verification\r\n                  </span>\r\n                )}\r\n              </div>\r\n              <button className=\"header-btn header-btn-logout\" onClick={handleLogout}>\r\n                Logout\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"header-auth-buttons\">\r\n              <button className=\"header-btn header-btn-primary\" onClick={onSignIn}>\r\n                Sign In\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <button className=\"header-menu-toggle\" onClick={toggleMenu}>\r\n          <span></span>\r\n          <span></span>\r\n          <span></span>\r\n        </button>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n\r\nexport default Header;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,eAAe,MAAM,2BAA2B;AACvD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,MAAMA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC5B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMU,QAAQ,GAAGT,SAAS,CAACU,WAAW,CAAC,CAAC;EACxC,MAAM;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC;EAAS,CAAC,GAAGJ,QAAQ;EAEjD,MAAMK,UAAU,GAAGA,CAAA,KAAM;IACvBN,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBf,SAAS,CAACgB,aAAa,CAAC,CAAC;IACzBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,oBACEhB,OAAA;IAAQiB,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBlB,OAAA;MAAKiB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BlB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlB,OAAA;UACEmB,GAAG,EAAC,cAAc;UAClBC,GAAG,EAAC,eAAe;UACnBH,SAAS,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFxB,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BlB,OAAA;YAAAkB,QAAA,EAAI;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbxB,OAAA;YAAMiB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAsB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxB,OAAA;QAAKiB,SAAS,EAAE,cAAcb,UAAU,GAAG,iBAAiB,GAAG,EAAE,EAAG;QAAAc,QAAA,eAClElB,OAAA;UAAIiB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC7BlB,OAAA;YAAAkB,QAAA,eAAIlB,OAAA;cAAGyB,IAAI,EAAC,OAAO;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DxB,OAAA;YAAAkB,QAAA,eAAIlB,OAAA;cAAGyB,IAAI,EAAC,QAAQ;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClExB,OAAA;YAAAkB,QAAA,eAAIlB,OAAA;cAAGyB,IAAI,EAAC,WAAW;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrExB,OAAA;YAAAkB,QAAA,eAAIlB,OAAA;cAAGyB,IAAI,EAAC,eAAe;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ExB,OAAA;YAAAkB,QAAA,eAAIlB,OAAA;cAAGyB,IAAI,EAAC,UAAU;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENxB,OAAA;QAAKiB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BV,QAAQ,gBACPR,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BlB,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BlB,OAAA;cAAMiB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAC,WACtB,eAAAlB,OAAA;gBAAAkB,QAAA,EAASV;cAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,EACNf,QAAQ,iBACPT,OAAA;cAAMiB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC/BT,QAAQ,CAACiB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGlB,QAAQ,CAACmB,KAAK,CAAC,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CACP,EACA,CAACd,QAAQ,iBACRV,OAAA;cAAMiB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNxB,OAAA;YAAQiB,SAAS,EAAC,8BAA8B;YAACY,OAAO,EAAEjB,YAAa;YAAAM,QAAA,EAAC;UAExE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENxB,OAAA;UAAKiB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClClB,OAAA;YAAQiB,SAAS,EAAC,+BAA+B;YAACY,OAAO,EAAE3B,QAAS;YAAAgB,QAAA,EAAC;UAErE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxB,OAAA;QAAQiB,SAAS,EAAC,oBAAoB;QAACY,OAAO,EAAElB,UAAW;QAAAO,QAAA,gBACzDlB,OAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxB,OAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxB,OAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACrB,EAAA,CA9EQF,MAAM;AAAA6B,EAAA,GAAN7B,MAAM;AAgFf,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}