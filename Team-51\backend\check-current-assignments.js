const mongoose = require('mongoose');
const Order = require('./models/Order');
const Artisan = require('./models/Artisan');
const TeamLeader = require('./models/TeamLeader');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/ngo_users')
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.log(err));

// Function to check current assignments
async function checkCurrentAssignments() {
  try {
    console.log('Checking current orders and artisan assignments...');

    // Get all existing orders
    const orders = await Order.find({});
    console.log(`Found ${orders.length} orders`);

    // Get all artisans
    const artisans = await Artisan.find({});
    console.log(`Found ${artisans.length} artisans`);

    // Get all team leaders
    const teamLeaders = await TeamLeader.find({});
    console.log(`Found ${teamLeaders.length} team leaders`);

    // Group artisans by village
    const artisansByVillage = {};
    artisans.forEach(artisan => {
      if (!artisansByVillage[artisan.tl_id]) {
        artisansByVillage[artisan.tl_id] = [];
      }
      artisansByVillage[artisan.tl_id].push(artisan);
    });

    console.log('\nArtisans grouped by village:');
    Object.keys(artisansByVillage).forEach(village => {
      console.log(`  Village ${village}: ${artisansByVillage[village].length} artisans`);
      artisansByVillage[village].forEach(artisan => {
        console.log(`    - ${artisan.customer_id}: ${artisan.name}`);
      });
    });

    console.log('\nCurrent orders and their assignments:');
    orders.forEach((order, index) => {
      console.log(`\nOrder ${index + 1}: ${order.order_id}`);
      console.log(`  Status: ${order.status}`);
      console.log(`  Team Leaders: ${order.team_leads.join(', ')}`);
      console.log(`  Quantities: ${order.quantities.join(', ')}`);
      
      if (order.artisan_assignments && order.artisan_assignments.length > 0) {
        console.log(`  Artisan Assignments:`);
        order.artisan_assignments.forEach((assignment, assIndex) => {
          console.log(`    Team Leader ${assignment.team_leader_id}:`);
          assignment.artisans.forEach((artisan, artIndex) => {
            console.log(`      - ${artisan.artisan_name} (${artisan.artisan_id}): ${artisan.assigned_quantity} units, ${artisan.completed_quantity} completed, status: ${artisan.status}`);
          });
        });
      } else {
        console.log(`  No artisan assignments found`);
      }
    });

  } catch (error) {
    console.error('Error checking assignments:', error);
  } finally {
    mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the check function
checkCurrentAssignments(); 