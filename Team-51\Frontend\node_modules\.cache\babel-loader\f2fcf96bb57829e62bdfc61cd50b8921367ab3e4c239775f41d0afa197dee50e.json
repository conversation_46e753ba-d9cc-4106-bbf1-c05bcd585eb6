{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Header from './components/layout/Header';\nimport Footer from './components/layout/Footer';\nimport Hero from './components/pages/Hero';\nimport About from './components/pages/About';\nimport AuthForm from './components/common/AuthForm';\nimport TLDashboard from './components/dashboard/TLDashboard';\nimport { GoogleOAuthProvider } from '@react-oauth/google';\nimport { userUtils } from './services/api';\nimport './styles/globals.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [authModalOpen, setAuthModalOpen] = useState(false);\n  const [showDashboard, setShowDashboard] = useState(false);\n  const [userInfo, setUserInfo] = useState({\n    username: null,\n    userType: null,\n    verified: false\n  });\n\n  // Check authentication status on component mount\n  useEffect(() => {\n    const userInfo = userUtils.getUserInfo();\n    setUserInfo(userInfo);\n  }, []);\n\n  // Pass these handlers to Header\n  const handleSignIn = () => {\n    setAuthModalOpen(true);\n  };\n  const handleCloseModal = () => setAuthModalOpen(false);\n\n  // Determine if user can access dashboard\n  const canAccessDashboard = userUtils.canAccessDashboard();\n  return /*#__PURE__*/_jsxDEV(GoogleOAuthProvider, {\n    clientId: \"###\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        onSignIn: handleSignIn\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), canAccessDashboard && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px',\n          background: '#f8fafc'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowDashboard(!showDashboard),\n          style: {\n            padding: '12px 24px',\n            backgroundColor: '#2c5530',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '1rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'all 0.3s ease'\n          },\n          children: showDashboard ? 'Show Main Site' : `Show ${userInfo.userType === 'team leader' ? 'Team Leader' : 'Admin'} Dashboard`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this), userInfo.username && !userInfo.verified && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '15px',\n          background: '#fff3cd',\n          borderLeft: '4px solid #ff6b35',\n          margin: '0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#856404'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Account Pending Verification:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), \" Your account is awaiting approval from an administrator. You'll receive access to additional features once verified.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this), showDashboard && canAccessDashboard ? /*#__PURE__*/_jsxDEV(TLDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"main\", {\n        children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AuthForm, {\n        isOpen: authModalOpen,\n        onClose: handleCloseModal,\n        initialMode: \"signin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"0qsRwv9eyX+fTaDtlyFlrz+xW6g=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "Footer", "Hero", "About", "AuthForm", "TLDashboard", "GoogleOAuthProvider", "userUtils", "jsxDEV", "_jsxDEV", "App", "_s", "authModalOpen", "setAuthModalOpen", "showDashboard", "setShowDashboard", "userInfo", "setUserInfo", "username", "userType", "verified", "getUserInfo", "handleSignIn", "handleCloseModal", "canAccessDashboard", "clientId", "children", "className", "onSignIn", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "padding", "background", "onClick", "backgroundColor", "color", "border", "borderRadius", "fontSize", "fontWeight", "cursor", "transition", "borderLeft", "margin", "isOpen", "onClose", "initialMode", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport Header from './components/layout/Header';\r\nimport Footer from './components/layout/Footer';\r\nimport Hero from './components/pages/Hero';\r\nimport About from './components/pages/About';\r\nimport AuthForm from './components/common/AuthForm';\r\nimport TLDashboard from './components/dashboard/TLDashboard';\r\nimport { GoogleOAuthProvider } from '@react-oauth/google';\r\nimport { userUtils } from './services/api';\r\nimport './styles/globals.css';\r\n\r\nfunction App() {\r\n  const [authModalOpen, setAuthModalOpen] = useState(false);\r\n  const [showDashboard, setShowDashboard] = useState(false);\r\n  const [userInfo, setUserInfo] = useState({\r\n    username: null,\r\n    userType: null,\r\n    verified: false\r\n  });\r\n\r\n  // Check authentication status on component mount\r\n  useEffect(() => {\r\n    const userInfo = userUtils.getUserInfo();\r\n    setUserInfo(userInfo);\r\n  }, []);\r\n\r\n  // Pass these handlers to Header\r\n  const handleSignIn = () => {\r\n    setAuthModalOpen(true);\r\n  };\r\n\r\n  const handleCloseModal = () => setAuthModalOpen(false);\r\n\r\n  // Determine if user can access dashboard\r\n  const canAccessDashboard = userUtils.canAccessDashboard();\r\n\r\n  return (\r\n    <GoogleOAuthProvider clientId=\"###\">\r\n      <div className=\"App\">\r\n        <Header onSignIn={handleSignIn} />\r\n\r\n        {/* Dashboard Toggle Button - only show for authorized users */}\r\n        {canAccessDashboard && (\r\n          <div style={{ textAlign: 'center', padding: '20px', background: '#f8fafc' }}>\r\n            <button\r\n              onClick={() => setShowDashboard(!showDashboard)}\r\n              style={{\r\n                padding: '12px 24px',\r\n                backgroundColor: '#2c5530',\r\n                color: 'white',\r\n                border: 'none',\r\n                borderRadius: '8px',\r\n                fontSize: '1rem',\r\n                fontWeight: '600',\r\n                cursor: 'pointer',\r\n                transition: 'all 0.3s ease'\r\n              }}\r\n            >\r\n              {showDashboard ? 'Show Main Site' : `Show ${userInfo.userType === 'team leader' ? 'Team Leader' : 'Admin'} Dashboard`}\r\n            </button>\r\n          </div>\r\n        )}\r\n\r\n        {/* Show verification message for unverified users */}\r\n        {userInfo.username && !userInfo.verified && (\r\n          <div style={{ \r\n            textAlign: 'center', \r\n            padding: '15px', \r\n            background: '#fff3cd', \r\n            borderLeft: '4px solid #ff6b35',\r\n            margin: '0'\r\n          }}>\r\n            <p style={{ margin: 0, color: '#856404' }}>\r\n              <strong>Account Pending Verification:</strong> Your account is awaiting approval from an administrator. \r\n              You'll receive access to additional features once verified.\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {showDashboard && canAccessDashboard ? (\r\n          <TLDashboard />\r\n        ) : (\r\n          <main>\r\n            <Hero />\r\n            <About />\r\n          </main>\r\n        )}\r\n\r\n        <Footer />\r\n        <AuthForm isOpen={authModalOpen} onClose={handleCloseModal} initialMode=\"signin\" />\r\n      </div>\r\n    </GoogleOAuthProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACArB,SAAS,CAAC,MAAM;IACd,MAAMiB,QAAQ,GAAGT,SAAS,CAACc,WAAW,CAAC,CAAC;IACxCJ,WAAW,CAACD,QAAQ,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzBT,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMU,gBAAgB,GAAGA,CAAA,KAAMV,gBAAgB,CAAC,KAAK,CAAC;;EAEtD;EACA,MAAMW,kBAAkB,GAAGjB,SAAS,CAACiB,kBAAkB,CAAC,CAAC;EAEzD,oBACEf,OAAA,CAACH,mBAAmB;IAACmB,QAAQ,EAAC,KAAK;IAAAC,QAAA,eACjCjB,OAAA;MAAKkB,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBjB,OAAA,CAACT,MAAM;QAAC4B,QAAQ,EAAEN;MAAa;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGjCR,kBAAkB,iBACjBf,OAAA;QAAKwB,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAU,CAAE;QAAAV,QAAA,eAC1EjB,OAAA;UACE4B,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAAC,CAACD,aAAa,CAAE;UAChDmB,KAAK,EAAE;YACLE,OAAO,EAAE,WAAW;YACpBG,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE;UACd,CAAE;UAAAnB,QAAA,EAEDZ,aAAa,GAAG,gBAAgB,GAAG,QAAQE,QAAQ,CAACG,QAAQ,KAAK,aAAa,GAAG,aAAa,GAAG,OAAO;QAAY;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAhB,QAAQ,CAACE,QAAQ,IAAI,CAACF,QAAQ,CAACI,QAAQ,iBACtCX,OAAA;QAAKwB,KAAK,EAAE;UACVC,SAAS,EAAE,QAAQ;UACnBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBU,UAAU,EAAE,mBAAmB;UAC/BC,MAAM,EAAE;QACV,CAAE;QAAArB,QAAA,eACAjB,OAAA;UAAGwB,KAAK,EAAE;YAAEc,MAAM,EAAE,CAAC;YAAER,KAAK,EAAE;UAAU,CAAE;UAAAb,QAAA,gBACxCjB,OAAA;YAAAiB,QAAA,EAAQ;UAA6B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,yHAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN,EAEAlB,aAAa,IAAIU,kBAAkB,gBAClCf,OAAA,CAACJ,WAAW;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEfvB,OAAA;QAAAiB,QAAA,gBACEjB,OAAA,CAACP,IAAI;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACRvB,OAAA,CAACN,KAAK;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACP,eAEDvB,OAAA,CAACR,MAAM;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVvB,OAAA,CAACL,QAAQ;QAAC4C,MAAM,EAAEpC,aAAc;QAACqC,OAAO,EAAE1B,gBAAiB;QAAC2B,WAAW,EAAC;MAAQ;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE1B;AAACrB,EAAA,CAlFQD,GAAG;AAAAyC,EAAA,GAAHzC,GAAG;AAoFZ,eAAeA,GAAG;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}