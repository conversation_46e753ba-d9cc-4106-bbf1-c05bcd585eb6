/* Hero Section with Faded Background */
.hero {
  position: relative;
  min-height: 100vh;
  padding: 120px 0 80px 0;
  overflow: hidden;
}

/* Background Image */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

/* Overlay for text readability */
.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.55) 0%,
    rgba(248, 250, 252, 0.50) 40%,
    rgba(226, 232, 240, 0.45) 100%
  );
  z-index: 2;
}

.hero-container {
  position: relative;
  z-index: 3;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Header */
.hero-header {
  text-align: center;
  margin-bottom: 80px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(14, 165, 233, 0.15);
  color: var(--primary-sky-blue);
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 24px;
  border: 2px solid rgba(14, 165, 233, 0.2);
  backdrop-filter: blur(10px);
}

.hero-badge-icon {
  font-size: 1.1rem;
}

.hero-title {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1.1;
  color: #1e293b;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  display: block;
  color: var(--accent-orange);
  font-size: 3.2rem;
  margin-top: 10px;
  font-weight: 800;
}

.hero-description {
  font-size: 1.25rem;
  line-height: 1.7;
  color: #475569;
  margin-bottom: 0;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

/* Main Grid Layout */
.hero-main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  margin-bottom: 80px;
}

/* Left Column - Mission & Actions */
.hero-left-column {
  background: rgba(255, 255, 255, 0.90);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.7);
}

.mission-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
  text-align: center;
}

.mission-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 40px;
}

.mission-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border-left: 4px solid var(--primary-sky-blue);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.mission-card:hover {
  transform: translateX(8px);
  box-shadow: 0 4px 15px rgba(14, 165, 233, 0.2);
  background: rgba(255, 255, 255, 0.9);
}

.mission-card .mission-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-sky-blue), var(--primary-green));
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 10px rgba(14, 165, 233, 0.3);
}

.mission-content h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.mission-content p {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
}

.hero-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.hero-btn {
  padding: 16px 28px;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-family: inherit;
}

.hero-btn-primary {
  background: linear-gradient(135deg, var(--accent-orange), #fb923c);
  color: white;
  box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
}

.hero-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(249, 115, 22, 0.4);
}

.hero-btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-sky-blue);
  border: 2px solid var(--primary-sky-blue);
  backdrop-filter: blur(10px);
}

.hero-btn-secondary:hover {
  background: var(--primary-sky-blue);
  color: white;
  transform: translateY(-2px);
}

/* Right Column - Carousel */
.hero-right-column {
  background: rgba(255, 255, 255, 0.90);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.7);
}

.principles-header {
  text-align: center;
  margin-bottom: 30px;
}

.principles-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
}

.principles-header p {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
}

.principles-carousel {
  position: relative;
}

.carousel-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.carousel-slides {
  flex: 1;
  position: relative;
  height: 350px;
  overflow: hidden;
  border-radius: 15px;
  background: rgba(248, 250, 252, 0.5);
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-slide.active {
  opacity: 1;
}

.principle-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 15px;
}

.carousel-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid var(--primary-sky-blue);
  color: var(--primary-sky-blue);
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(14, 165, 233, 0.2);
}

.carousel-btn:hover {
  background: var(--primary-sky-blue);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(14, 165, 233, 0.3);
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  background: rgba(14, 165, 233, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: var(--primary-sky-blue);
  transform: scale(1.4);
}

/* Impact Statistics */
.impact-stats {
  background: rgba(255, 255, 255, 0.90);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 50px 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.7);
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.stat-item {
  text-align: center;
  position: relative;
}

.stat-number {
  font-size: 3rem;
  font-weight: 900;
  color: var(--accent-orange);
  display: block;
  line-height: 1;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 1rem;
  color: #475569;
  font-weight: 600;
  display: block;
  margin-bottom: 12px;
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.7;
  color: var(--primary-sky-blue);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-main-grid {
    grid-template-columns: 1fr;
    gap: 50px;
  }
  
  .hero-title {
    font-size: 3.2rem;
  }
  
  .hero-subtitle {
    font-size: 2.8rem;
  }
  
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 100px 0 60px 0;
  }
  
  .hero-container {
    padding: 0 15px;
  }
  
  .hero-header {
    margin-bottom: 50px;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 2.2rem;
  }
  
  .hero-description {
    font-size: 1.1rem;
  }
  
  .hero-main-grid {
    gap: 40px;
    margin-bottom: 50px;
  }
  
  .hero-left-column,
  .hero-right-column {
    padding: 30px 20px;
  }
  
  .hero-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .hero-btn {
    padding: 14px 24px;
    font-size: 0.95rem;
    justify-content: center;
  }
  
  .carousel-slides {
    height: 280px;
  }
  
  .carousel-wrapper {
    gap: 15px;
  }
  
  .carousel-btn {
    width: 40px;
    height: 40px;
  }
  
  .stats-container {
    grid-template-columns: 1fr;
    gap: 25px;
  }
  
  .impact-stats {
    padding: 40px 20px;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.8rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .hero-left-column,
  .hero-right-column {
    padding: 25px 15px;
  }
  
  .mission-card {
    padding: 15px;
  }
  
  .mission-card .mission-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
  
  .carousel-slides {
    height: 220px;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .stat-label {
    font-size: 0.9rem;
  }
  
  .stat-icon {
    font-size: 1.5rem;
  }
}

/* Stats Section */
.hero-stats-section {
  animation: fadeInUp 1s ease-out 1s both;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
}

.hero-stat {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  color: white;
}

.hero-stat h3 {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 8px 0;
  color: var(--accent-orange-lighter);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-stat p {
  font-size: 0.95rem;
  margin: 0 0 10px 0;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  font-size: 1.5rem;
  opacity: 0.8;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .hero-main {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .hero-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 80px 0 60px 0;
    background-attachment: scroll;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-description {
    font-size: 1.1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    gap: 15px;
  }
  
  .hero-btn {
    padding: 14px 24px;
    font-size: 1rem;
    justify-content: center;
  }
  
  .carousel-slides {
    height: 280px;
  }
  
  .carousel-btn {
    width: 40px;
    height: 40px;
  }
  
  .principles-carousel {
    padding: 30px 20px;
  }
  
  .principles-header h2 {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .hero-stats-section {
    padding: 40px 20px;
  }
  
  .stat-content h3 {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .hero-container {
    padding: 0 15px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .principles-carousel {
    padding: 20px 15px;
  }
  
  .carousel-header h3 {
    font-size: 1.5rem;
  }
  
  .carousel-slides {
    height: 220px;
  }
  
  .principle-image {
    object-fit: contain;
    border-radius: 14px;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 40px 0 0 0;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
    padding: 20px 0 60px 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-stat {
    padding: 15px;
  }

  .hero-stat h3 {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .hero-btn {
    padding: 12px 24px;
    font-size: 1rem;
  }

  .hero-img {
    height: 300px;
  }

  .hero-image-overlay {
    top: -10px;
    right: -10px;
  }

  .hero-badge {
    padding: 8px 16px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .hero-container {
    padding: 0 15px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-stat h3 {
    font-size: 1.8rem;
  }

  .hero-img {
    height: 250px;
  }
}
