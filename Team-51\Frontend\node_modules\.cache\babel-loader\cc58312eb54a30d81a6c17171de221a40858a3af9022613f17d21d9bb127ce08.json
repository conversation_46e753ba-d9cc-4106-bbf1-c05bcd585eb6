{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\components\\\\dashboard\\\\ArtisanAssignment.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './ArtisanAssignment.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ArtisanAssignment() {\n  _s();\n  var _assignmentData$order;\n  const [assignmentData, setAssignmentData] = useState({\n    totalOrdersReceived: 500,\n    totalProductsAssigned: 420,\n    pendingAssignment: 80,\n    orders: [{\n      id: 'ORD001',\n      product: 'Handwoven Sarees',\n      totalQuantity: 150,\n      assignedQuantity: 125,\n      pendingQuantity: 25,\n      deadline: '2024-02-15',\n      status: 'In Progress'\n    }],\n    artisans: [{\n      id: 1,\n      name: '<PERSON><PERSON>',\n      skill: 'Handloom & Textile Crafts',\n      available: true\n    }, {\n      id: 2,\n      name: '<PERSON><PERSON>',\n      skill: 'Handloom & Textile Crafts',\n      available: true\n    }, {\n      id: 3,\n      name: '<PERSON><PERSON>',\n      skill: 'Handloom & Textile Crafts',\n      available: false\n    }, {\n      id: 4,\n      name: '<PERSON><PERSON> <PERSON>',\n      skill: 'Handloom & Textile Crafts',\n      available: true\n    }, {\n      id: 5,\n      name: 'Sunita Devi',\n      skill: 'Handloom & Textile Crafts',\n      available: true\n    }, {\n      id: 6,\n      name: 'Kavita Devi',\n      skill: 'Handloom & Textile Crafts',\n      available: true\n    }],\n    assignments: [{\n      id: 1,\n      orderId: 'ORD001',\n      artisanId: 1,\n      artisanName: 'Priya Sharma',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 40,\n      manufacturedQuantity: 32,\n      assignedDate: '2024-01-10',\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: '2024-02-15'\n    }, {\n      id: 2,\n      orderId: 'ORD001',\n      artisanId: 3,\n      artisanName: 'Meera Patel',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 45,\n      manufacturedQuantity: 45,\n      assignedDate: '2024-01-08',\n      submissionDate: '2024-01-28',\n      status: 'Completed',\n      deadline: '2024-02-15'\n    }, {\n      id: 3,\n      orderId: 'ORD001',\n      artisanId: 2,\n      artisanName: 'Rajesh Kumar',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 40,\n      manufacturedQuantity: 35,\n      assignedDate: '2024-01-12',\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: '2024-02-15'\n    }, {\n      id: 4,\n      orderId: 'ORD002',\n      artisanId: 5,\n      artisanName: 'Sunita Devi',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 60,\n      manufacturedQuantity: 60,\n      assignedDate: '2024-01-15',\n      submissionDate: '2024-02-05',\n      status: 'Completed',\n      deadline: '2024-02-28'\n    }, {\n      id: 5,\n      orderId: 'ORD002',\n      artisanId: 4,\n      artisanName: 'Amit Singh',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 70,\n      manufacturedQuantity: 55,\n      assignedDate: '2024-01-18',\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: '2024-02-28'\n    }, {\n      id: 6,\n      orderId: 'ORD002',\n      artisanId: 6,\n      artisanName: 'Kavita Devi',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 50,\n      manufacturedQuantity: 38,\n      assignedDate: '2024-01-20',\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: '2024-02-28'\n    }, {\n      id: 7,\n      orderId: 'ORD003',\n      artisanId: 1,\n      artisanName: 'Priya Sharma',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 35,\n      manufacturedQuantity: 20,\n      assignedDate: '2024-02-01',\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: '2024-03-10'\n    }, {\n      id: 8,\n      orderId: 'ORD003',\n      artisanId: 2,\n      artisanName: 'Rajesh Kumar',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 40,\n      manufacturedQuantity: 25,\n      assignedDate: '2024-02-03',\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: '2024-03-10'\n    }, {\n      id: 9,\n      orderId: 'ORD003',\n      artisanId: 4,\n      artisanName: 'Amit Singh',\n      product: 'Handwoven Sarees',\n      assignedQuantity: 40,\n      manufacturedQuantity: 15,\n      assignedDate: '2024-02-05',\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: '2024-03-10'\n    }]\n  });\n  const [showAssignForm, setShowAssignForm] = useState(false);\n  const [newAssignment, setNewAssignment] = useState({\n    orderId: '',\n    artisanId: '',\n    quantity: ''\n  });\n  const [activeTab, setActiveTab] = useState('overview');\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewAssignment(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleAssignProduct = e => {\n    e.preventDefault();\n    const selectedOrder = assignmentData.orders.find(order => order.id === newAssignment.orderId);\n    const selectedArtisan = assignmentData.artisans.find(artisan => artisan.id === parseInt(newAssignment.artisanId));\n    if (!selectedOrder || !selectedArtisan) return;\n    const newAssignmentRecord = {\n      id: assignmentData.assignments.length + 1,\n      orderId: newAssignment.orderId,\n      artisanId: selectedArtisan.id,\n      artisanName: selectedArtisan.name,\n      product: selectedOrder.product,\n      assignedQuantity: parseInt(newAssignment.quantity),\n      manufacturedQuantity: 0,\n      assignedDate: new Date().toISOString().split('T')[0],\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: selectedOrder.deadline\n    };\n    setAssignmentData(prev => ({\n      ...prev,\n      assignments: [...prev.assignments, newAssignmentRecord],\n      totalProductsAssigned: prev.totalProductsAssigned + parseInt(newAssignment.quantity),\n      pendingAssignment: prev.pendingAssignment - parseInt(newAssignment.quantity),\n      orders: prev.orders.map(order => order.id === newAssignment.orderId ? {\n        ...order,\n        assignedQuantity: order.assignedQuantity + parseInt(newAssignment.quantity),\n        pendingQuantity: order.pendingQuantity - parseInt(newAssignment.quantity)\n      } : order)\n    }));\n    setNewAssignment({\n      orderId: '',\n      artisanId: '',\n      quantity: ''\n    });\n    setShowAssignForm(false);\n  };\n  const updateManufacturedQuantity = (assignmentId, quantity) => {\n    setAssignmentData(prev => ({\n      ...prev,\n      assignments: prev.assignments.map(assignment => assignment.id === assignmentId ? {\n        ...assignment,\n        manufacturedQuantity: parseInt(quantity) || 0\n      } : assignment)\n    }));\n  };\n  const markAsCompleted = assignmentId => {\n    setAssignmentData(prev => ({\n      ...prev,\n      assignments: prev.assignments.map(assignment => assignment.id === assignmentId ? {\n        ...assignment,\n        status: 'Completed',\n        submissionDate: new Date().toISOString().split('T')[0]\n      } : assignment)\n    }));\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Completed':\n        return '#10b981';\n      case 'In Progress':\n        return '#3b82f6';\n      case 'Overdue':\n        return '#ef4444';\n      default:\n        return '#6b7280';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"artisan-assignment\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"assignment-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Handwoven Sarees Production\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage saree assignments and track artisan progress\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-badge\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83E\\uDDF5 Product: Handwoven Sarees\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"summary-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-card primary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Total Sarees Ordered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"summary-number\",\n          children: assignmentData.totalOrdersReceived\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"summary-label\",\n          children: \"Sarees\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-card success\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Sarees Assigned\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"summary-number\",\n          children: assignmentData.totalProductsAssigned\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"summary-label\",\n          children: \"To Artisans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-card warning\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Pending Assignment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"summary-number\",\n          children: assignmentData.pendingAssignment\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"summary-label\",\n          children: \"Sarees\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-card info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Assignment Rate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"summary-number\",\n          children: [(assignmentData.totalProductsAssigned / assignmentData.totalOrdersReceived * 100).toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"summary-label\",\n          children: \"Completed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"assignment-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'overview' ? 'active' : ''}`,\n        onClick: () => setActiveTab('overview'),\n        children: \"Saree Orders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'assignments' ? 'active' : ''}`,\n        onClick: () => setActiveTab('assignments'),\n        children: \"Artisan Assignments\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'assign' ? 'active' : ''}`,\n        onClick: () => setActiveTab('assign'),\n        children: \"Assign Sarees\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"orders-overview\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Current Saree Orders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"orders-grid\",\n          children: assignmentData.orders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"order-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: order.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"order-status\",\n                children: order.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: order.product\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"order-progress\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-item\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Total: \", order.totalQuantity]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-item\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Assigned: \", order.assignedQuantity]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-item\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Pending: \", order.pendingQuantity]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-bar\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-fill\",\n                style: {\n                  width: `${order.assignedQuantity / order.totalQuantity * 100}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"order-deadline\",\n              children: [\"Deadline: \", order.deadline]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 19\n            }, this)]\n          }, order.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 9\n    }, this), activeTab === 'assignments' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"assignments-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Saree Production Assignments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"assignments-table\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Order ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Artisan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Assigned Qty\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Manufactured\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Assigned Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Submission Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: assignmentData.assignments.map(assignment => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: assignment.orderId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: assignment.artisanName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: assignment.product\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: assignment.assignedQuantity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: assignment.manufacturedQuantity,\n                    onChange: e => updateManufacturedQuantity(assignment.id, e.target.value),\n                    max: assignment.assignedQuantity,\n                    min: \"0\",\n                    className: \"quantity-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: assignment.assignedDate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: assignment.submissionDate || 'Pending'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"status-badge\",\n                    style: {\n                      backgroundColor: getStatusColor(assignment.status)\n                    },\n                    children: assignment.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: assignment.status === 'In Progress' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"complete-btn\",\n                    onClick: () => markAsCompleted(assignment.id),\n                    children: \"Mark Complete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 23\n                }, this)]\n              }, assignment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this), activeTab === 'assign' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"assign-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Assign Sarees to Artisans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"assign-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"New Assignment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleAssignProduct,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Select Order\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"orderId\",\n                  value: newAssignment.orderId,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Choose an order...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this), assignmentData.orders.filter(order => order.pendingQuantity > 0).map(order => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: order.id,\n                    children: [order.id, \" - \", order.product, \" (Pending: \", order.pendingQuantity, \")\"]\n                  }, order.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Select Artisan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"artisanId\",\n                  value: newAssignment.artisanId,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Choose an artisan...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this), assignmentData.artisans.filter(artisan => artisan.available).map(artisan => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: artisan.id,\n                    children: [artisan.name, \" - Available\"]\n                  }, artisan.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Quantity to Assign\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"quantity\",\n                  value: newAssignment.quantity,\n                  onChange: handleInputChange,\n                  min: \"1\",\n                  max: newAssignment.orderId ? ((_assignmentData$order = assignmentData.orders.find(o => o.id === newAssignment.orderId)) === null || _assignmentData$order === void 0 ? void 0 : _assignmentData$order.pendingQuantity) || 0 : 0,\n                  required: true,\n                  placeholder: \"Enter quantity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"cancel-btn\",\n                onClick: () => setNewAssignment({\n                  orderId: '',\n                  artisanId: '',\n                  quantity: ''\n                }),\n                children: \"Clear\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"assign-btn\",\n                children: \"Assign Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"available-artisans\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Available Artisans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"artisans-grid\",\n            children: assignmentData.artisans.map(artisan => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `artisan-card ${artisan.available ? 'available' : 'unavailable'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: artisan.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Skill: \", artisan.skill]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `availability-badge ${artisan.available ? 'available' : 'unavailable'}`,\n                children: artisan.available ? 'Available' : 'Busy'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 21\n              }, this)]\n            }, artisan.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 245,\n    columnNumber: 5\n  }, this);\n}\n_s(ArtisanAssignment, \"r3QJA4xNfbbVrbZ2z9J9h/38aJw=\");\n_c = ArtisanAssignment;\nexport default ArtisanAssignment;\nvar _c;\n$RefreshReg$(_c, \"ArtisanAssignment\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ArtisanAssignment", "_s", "_assignmentData$order", "assignmentData", "setAssignmentData", "totalOrdersReceived", "totalProductsAssigned", "pendingAssignment", "orders", "id", "product", "totalQuantity", "assignedQuantity", "pendingQuantity", "deadline", "status", "artisans", "name", "skill", "available", "assignments", "orderId", "artisanId", "artisanName", "manufacturedQuantity", "assignedDate", "submissionDate", "showAssignForm", "setShowAssignForm", "newAssignment", "setNewAssignment", "quantity", "activeTab", "setActiveTab", "handleInputChange", "e", "value", "target", "prev", "handleAssignProduct", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "find", "order", "selectedArtisan", "artisan", "parseInt", "newAssignmentRecord", "length", "Date", "toISOString", "split", "map", "updateManufacturedQuantity", "assignmentId", "assignment", "mark<PERSON><PERSON>ompleted", "getStatusColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toFixed", "onClick", "style", "width", "type", "onChange", "max", "min", "backgroundColor", "onSubmit", "required", "filter", "o", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/components/dashboard/ArtisanAssignment.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './ArtisanAssignment.css';\n\nfunction ArtisanAssignment() {\n  const [assignmentData, setAssignmentData] = useState({\n    totalOrdersReceived: 500,\n    totalProductsAssigned: 420,\n    pendingAssignment: 80,\n    orders: [\n      {\n        id: 'ORD001',\n        product: 'Handwoven Sarees',\n        totalQuantity: 150,\n        assignedQuantity: 125,\n        pendingQuantity: 25,\n        deadline: '2024-02-15',\n        status: 'In Progress'\n      },\n  \n    ],\n    artisans: [\n      { id: 1, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true },\n      { id: 2, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true },\n      { id: 3, name: '<PERSON><PERSON> <PERSON>', skill: 'Handloom & Textile Crafts', available: false },\n      { id: 4, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true },\n      { id: 5, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true },\n      { id: 6, name: '<PERSON><PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true }\n    ],\n    assignments: [\n      {\n        id: 1,\n        orderId: 'ORD001',\n        artisanId: 1,\n        artisanName: '<PERSON><PERSON>',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 40,\n        manufacturedQuantity: 32,\n        assignedDate: '2024-01-10',\n        submissionDate: null,\n        status: 'In Progress',\n        deadline: '2024-02-15'\n      },\n      {\n        id: 2,\n        orderId: 'ORD001',\n        artisanId: 3,\n        artisanName: 'Meera Patel',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 45,\n        manufacturedQuantity: 45,\n        assignedDate: '2024-01-08',\n        submissionDate: '2024-01-28',\n        status: 'Completed',\n        deadline: '2024-02-15'\n      },\n      {\n        id: 3,\n        orderId: 'ORD001',\n        artisanId: 2,\n        artisanName: 'Rajesh Kumar',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 40,\n        manufacturedQuantity: 35,\n        assignedDate: '2024-01-12',\n        submissionDate: null,\n        status: 'In Progress',\n        deadline: '2024-02-15'\n      },\n      {\n        id: 4,\n        orderId: 'ORD002',\n        artisanId: 5,\n        artisanName: 'Sunita Devi',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 60,\n        manufacturedQuantity: 60,\n        assignedDate: '2024-01-15',\n        submissionDate: '2024-02-05',\n        status: 'Completed',\n        deadline: '2024-02-28'\n      },\n      {\n        id: 5,\n        orderId: 'ORD002',\n        artisanId: 4,\n        artisanName: 'Amit Singh',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 70,\n        manufacturedQuantity: 55,\n        assignedDate: '2024-01-18',\n        submissionDate: null,\n        status: 'In Progress',\n        deadline: '2024-02-28'\n      },\n      {\n        id: 6,\n        orderId: 'ORD002',\n        artisanId: 6,\n        artisanName: 'Kavita Devi',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 50,\n        manufacturedQuantity: 38,\n        assignedDate: '2024-01-20',\n        submissionDate: null,\n        status: 'In Progress',\n        deadline: '2024-02-28'\n      },\n      {\n        id: 7,\n        orderId: 'ORD003',\n        artisanId: 1,\n        artisanName: 'Priya Sharma',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 35,\n        manufacturedQuantity: 20,\n        assignedDate: '2024-02-01',\n        submissionDate: null,\n        status: 'In Progress',\n        deadline: '2024-03-10'\n      },\n      {\n        id: 8,\n        orderId: 'ORD003',\n        artisanId: 2,\n        artisanName: 'Rajesh Kumar',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 40,\n        manufacturedQuantity: 25,\n        assignedDate: '2024-02-03',\n        submissionDate: null,\n        status: 'In Progress',\n        deadline: '2024-03-10'\n      },\n      {\n        id: 9,\n        orderId: 'ORD003',\n        artisanId: 4,\n        artisanName: 'Amit Singh',\n        product: 'Handwoven Sarees',\n        assignedQuantity: 40,\n        manufacturedQuantity: 15,\n        assignedDate: '2024-02-05',\n        submissionDate: null,\n        status: 'In Progress',\n        deadline: '2024-03-10'\n      }\n    ]\n  });\n\n  const [showAssignForm, setShowAssignForm] = useState(false);\n  const [newAssignment, setNewAssignment] = useState({\n    orderId: '',\n    artisanId: '',\n    quantity: ''\n  });\n\n  const [activeTab, setActiveTab] = useState('overview');\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewAssignment(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleAssignProduct = (e) => {\n    e.preventDefault();\n    \n    const selectedOrder = assignmentData.orders.find(order => order.id === newAssignment.orderId);\n    const selectedArtisan = assignmentData.artisans.find(artisan => artisan.id === parseInt(newAssignment.artisanId));\n    \n    if (!selectedOrder || !selectedArtisan) return;\n\n    const newAssignmentRecord = {\n      id: assignmentData.assignments.length + 1,\n      orderId: newAssignment.orderId,\n      artisanId: selectedArtisan.id,\n      artisanName: selectedArtisan.name,\n      product: selectedOrder.product,\n      assignedQuantity: parseInt(newAssignment.quantity),\n      manufacturedQuantity: 0,\n      assignedDate: new Date().toISOString().split('T')[0],\n      submissionDate: null,\n      status: 'In Progress',\n      deadline: selectedOrder.deadline\n    };\n\n    setAssignmentData(prev => ({\n      ...prev,\n      assignments: [...prev.assignments, newAssignmentRecord],\n      totalProductsAssigned: prev.totalProductsAssigned + parseInt(newAssignment.quantity),\n      pendingAssignment: prev.pendingAssignment - parseInt(newAssignment.quantity),\n      orders: prev.orders.map(order => \n        order.id === newAssignment.orderId \n          ? {\n              ...order,\n              assignedQuantity: order.assignedQuantity + parseInt(newAssignment.quantity),\n              pendingQuantity: order.pendingQuantity - parseInt(newAssignment.quantity)\n            }\n          : order\n      )\n    }));\n\n    setNewAssignment({ orderId: '', artisanId: '', quantity: '' });\n    setShowAssignForm(false);\n  };\n\n  const updateManufacturedQuantity = (assignmentId, quantity) => {\n    setAssignmentData(prev => ({\n      ...prev,\n      assignments: prev.assignments.map(assignment =>\n        assignment.id === assignmentId\n          ? { ...assignment, manufacturedQuantity: parseInt(quantity) || 0 }\n          : assignment\n      )\n    }));\n  };\n\n  const markAsCompleted = (assignmentId) => {\n    setAssignmentData(prev => ({\n      ...prev,\n      assignments: prev.assignments.map(assignment =>\n        assignment.id === assignmentId\n          ? { \n              ...assignment, \n              status: 'Completed',\n              submissionDate: new Date().toISOString().split('T')[0]\n            }\n          : assignment\n      )\n    }));\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Completed': return '#10b981';\n      case 'In Progress': return '#3b82f6';\n      case 'Overdue': return '#ef4444';\n      default: return '#6b7280';\n    }\n  };\n\n  return (\n    <div className=\"artisan-assignment\">\n      <div className=\"assignment-header\">\n        <h1>Handwoven Sarees Production</h1>\n        <p>Manage saree assignments and track artisan progress</p>\n        <div className=\"product-badge\">\n          <span>🧵 Product: Handwoven Sarees</span>\n        </div>\n      </div>\n\n      {/* Summary Cards */}\n      <div className=\"summary-cards\">\n        <div className=\"summary-card primary\">\n          <h3>Total Sarees Ordered</h3>\n          <p className=\"summary-number\">{assignmentData.totalOrdersReceived}</p>\n          <span className=\"summary-label\">Sarees</span>\n        </div>\n        <div className=\"summary-card success\">\n          <h3>Sarees Assigned</h3>\n          <p className=\"summary-number\">{assignmentData.totalProductsAssigned}</p>\n          <span className=\"summary-label\">To Artisans</span>\n        </div>\n        <div className=\"summary-card warning\">\n          <h3>Pending Assignment</h3>\n          <p className=\"summary-number\">{assignmentData.pendingAssignment}</p>\n          <span className=\"summary-label\">Sarees</span>\n        </div>\n        <div className=\"summary-card info\">\n          <h3>Assignment Rate</h3>\n          <p className=\"summary-number\">{((assignmentData.totalProductsAssigned / assignmentData.totalOrdersReceived) * 100).toFixed(1)}%</p>\n          <span className=\"summary-label\">Completed</span>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"assignment-tabs\">\n        <button\n          className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}\n          onClick={() => setActiveTab('overview')}\n        >\n          Saree Orders\n        </button>\n        <button\n          className={`tab-btn ${activeTab === 'assignments' ? 'active' : ''}`}\n          onClick={() => setActiveTab('assignments')}\n        >\n          Artisan Assignments\n        </button>\n        <button\n          className={`tab-btn ${activeTab === 'assign' ? 'active' : ''}`}\n          onClick={() => setActiveTab('assign')}\n        >\n          Assign Sarees\n        </button>\n      </div>\n\n      {/* Orders Overview Tab */}\n      {activeTab === 'overview' && (\n        <div className=\"tab-content\">\n          <div className=\"orders-overview\">\n            <h2>Current Saree Orders</h2>\n            <div className=\"orders-grid\">\n              {assignmentData.orders.map(order => (\n                <div key={order.id} className=\"order-card\">\n                  <div className=\"order-header\">\n                    <h3>{order.id}</h3>\n                    <span className=\"order-status\">{order.status}</span>\n                  </div>\n                  <h4>{order.product}</h4>\n                  <div className=\"order-progress\">\n                    <div className=\"progress-item\">\n                      <span>Total: {order.totalQuantity}</span>\n                    </div>\n                    <div className=\"progress-item\">\n                      <span>Assigned: {order.assignedQuantity}</span>\n                    </div>\n                    <div className=\"progress-item\">\n                      <span>Pending: {order.pendingQuantity}</span>\n                    </div>\n                  </div>\n                  <div className=\"progress-bar\">\n                    <div \n                      className=\"progress-fill\"\n                      style={{ width: `${(order.assignedQuantity / order.totalQuantity) * 100}%` }}\n                    ></div>\n                  </div>\n                  <p className=\"order-deadline\">Deadline: {order.deadline}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Assignments Tab */}\n      {activeTab === 'assignments' && (\n        <div className=\"tab-content\">\n          <div className=\"assignments-section\">\n            <h2>Saree Production Assignments</h2>\n            <div className=\"assignments-table\">\n              <table>\n                <thead>\n                  <tr>\n                    <th>Order ID</th>\n                    <th>Artisan</th>\n                    <th>Product</th>\n                    <th>Assigned Qty</th>\n                    <th>Manufactured</th>\n                    <th>Assigned Date</th>\n                    <th>Submission Date</th>\n                    <th>Status</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {assignmentData.assignments.map(assignment => (\n                    <tr key={assignment.id}>\n                      <td>{assignment.orderId}</td>\n                      <td>{assignment.artisanName}</td>\n                      <td>{assignment.product}</td>\n                      <td>{assignment.assignedQuantity}</td>\n                      <td>\n                        <input\n                          type=\"number\"\n                          value={assignment.manufacturedQuantity}\n                          onChange={(e) => updateManufacturedQuantity(assignment.id, e.target.value)}\n                          max={assignment.assignedQuantity}\n                          min=\"0\"\n                          className=\"quantity-input\"\n                        />\n                      </td>\n                      <td>{assignment.assignedDate}</td>\n                      <td>{assignment.submissionDate || 'Pending'}</td>\n                      <td>\n                        <span \n                          className=\"status-badge\"\n                          style={{ backgroundColor: getStatusColor(assignment.status) }}\n                        >\n                          {assignment.status}\n                        </span>\n                      </td>\n                      <td>\n                        {assignment.status === 'In Progress' && (\n                          <button\n                            className=\"complete-btn\"\n                            onClick={() => markAsCompleted(assignment.id)}\n                          >\n                            Mark Complete\n                          </button>\n                        )}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Assign Products Tab */}\n      {activeTab === 'assign' && (\n        <div className=\"tab-content\">\n          <div className=\"assign-section\">\n            <h2>Assign Sarees to Artisans</h2>\n\n            <div className=\"assign-form\">\n              <h3>New Assignment</h3>\n              <form onSubmit={handleAssignProduct}>\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label>Select Order</label>\n                    <select\n                      name=\"orderId\"\n                      value={newAssignment.orderId}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      <option value=\"\">Choose an order...</option>\n                      {assignmentData.orders\n                        .filter(order => order.pendingQuantity > 0)\n                        .map(order => (\n                          <option key={order.id} value={order.id}>\n                            {order.id} - {order.product} (Pending: {order.pendingQuantity})\n                          </option>\n                        ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Select Artisan</label>\n                    <select\n                      name=\"artisanId\"\n                      value={newAssignment.artisanId}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      <option value=\"\">Choose an artisan...</option>\n                      {assignmentData.artisans\n                        .filter(artisan => artisan.available)\n                        .map(artisan => (\n                          <option key={artisan.id} value={artisan.id}>\n                            {artisan.name} - Available\n                          </option>\n                        ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Quantity to Assign</label>\n                    <input\n                      type=\"number\"\n                      name=\"quantity\"\n                      value={newAssignment.quantity}\n                      onChange={handleInputChange}\n                      min=\"1\"\n                      max={\n                        newAssignment.orderId\n                          ? assignmentData.orders.find(o => o.id === newAssignment.orderId)?.pendingQuantity || 0\n                          : 0\n                      }\n                      required\n                      placeholder=\"Enter quantity\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-actions\">\n                  <button\n                    type=\"button\"\n                    className=\"cancel-btn\"\n                    onClick={() => setNewAssignment({ orderId: '', artisanId: '', quantity: '' })}\n                  >\n                    Clear\n                  </button>\n                  <button type=\"submit\" className=\"assign-btn\">\n                    Assign Product\n                  </button>\n                </div>\n              </form>\n            </div>\n\n            {/* Available Artisans */}\n            <div className=\"available-artisans\">\n              <h3>Available Artisans</h3>\n              <div className=\"artisans-grid\">\n                {assignmentData.artisans.map(artisan => (\n                  <div key={artisan.id} className={`artisan-card ${artisan.available ? 'available' : 'unavailable'}`}>\n                    <h4>{artisan.name}</h4>\n                    <p>Skill: {artisan.skill}</p>\n                    <span className={`availability-badge ${artisan.available ? 'available' : 'unavailable'}`}>\n                      {artisan.available ? 'Available' : 'Busy'}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default ArtisanAssignment;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC3B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGR,QAAQ,CAAC;IACnDS,mBAAmB,EAAE,GAAG;IACxBC,qBAAqB,EAAE,GAAG;IAC1BC,iBAAiB,EAAE,EAAE;IACrBC,MAAM,EAAE,CACN;MACEC,EAAE,EAAE,QAAQ;MACZC,OAAO,EAAE,kBAAkB;MAC3BC,aAAa,EAAE,GAAG;MAClBC,gBAAgB,EAAE,GAAG;MACrBC,eAAe,EAAE,EAAE;MACnBC,QAAQ,EAAE,YAAY;MACtBC,MAAM,EAAE;IACV,CAAC,CAEF;IACDC,QAAQ,EAAE,CACR;MAAEP,EAAE,EAAE,CAAC;MAAEQ,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAK,CAAC,EACpF;MAAEV,EAAE,EAAE,CAAC;MAAEQ,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAK,CAAC,EACpF;MAAEV,EAAE,EAAE,CAAC;MAAEQ,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAM,CAAC,EACpF;MAAEV,EAAE,EAAE,CAAC;MAAEQ,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAK,CAAC,EAClF;MAAEV,EAAE,EAAE,CAAC;MAAEQ,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAK,CAAC,EACnF;MAAEV,EAAE,EAAE,CAAC;MAAEQ,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAK,CAAC,CACpF;IACDC,WAAW,EAAE,CACX;MACEX,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,cAAc;MAC3Bb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,IAAI;MACpBX,MAAM,EAAE,aAAa;MACrBD,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,aAAa;MAC1Bb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,YAAY;MAC5BX,MAAM,EAAE,WAAW;MACnBD,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,cAAc;MAC3Bb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,IAAI;MACpBX,MAAM,EAAE,aAAa;MACrBD,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,aAAa;MAC1Bb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,YAAY;MAC5BX,MAAM,EAAE,WAAW;MACnBD,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,YAAY;MACzBb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,IAAI;MACpBX,MAAM,EAAE,aAAa;MACrBD,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,aAAa;MAC1Bb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,IAAI;MACpBX,MAAM,EAAE,aAAa;MACrBD,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,cAAc;MAC3Bb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,IAAI;MACpBX,MAAM,EAAE,aAAa;MACrBD,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,cAAc;MAC3Bb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,IAAI;MACpBX,MAAM,EAAE,aAAa;MACrBD,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLY,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,YAAY;MACzBb,OAAO,EAAE,kBAAkB;MAC3BE,gBAAgB,EAAE,EAAE;MACpBY,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,YAAY;MAC1BC,cAAc,EAAE,IAAI;MACpBX,MAAM,EAAE,aAAa;MACrBD,QAAQ,EAAE;IACZ,CAAC;EAEL,CAAC,CAAC;EAEF,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC;IACjDyB,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbS,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,UAAU,CAAC;EAEtD,MAAMsC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAElB,IAAI;MAAEmB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCP,gBAAgB,CAACQ,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACrB,IAAI,GAAGmB;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,mBAAmB,GAAIJ,CAAC,IAAK;IACjCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,MAAMC,aAAa,GAAGtC,cAAc,CAACK,MAAM,CAACkC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAClC,EAAE,KAAKoB,aAAa,CAACR,OAAO,CAAC;IAC7F,MAAMuB,eAAe,GAAGzC,cAAc,CAACa,QAAQ,CAAC0B,IAAI,CAACG,OAAO,IAAIA,OAAO,CAACpC,EAAE,KAAKqC,QAAQ,CAACjB,aAAa,CAACP,SAAS,CAAC,CAAC;IAEjH,IAAI,CAACmB,aAAa,IAAI,CAACG,eAAe,EAAE;IAExC,MAAMG,mBAAmB,GAAG;MAC1BtC,EAAE,EAAEN,cAAc,CAACiB,WAAW,CAAC4B,MAAM,GAAG,CAAC;MACzC3B,OAAO,EAAEQ,aAAa,CAACR,OAAO;MAC9BC,SAAS,EAAEsB,eAAe,CAACnC,EAAE;MAC7Bc,WAAW,EAAEqB,eAAe,CAAC3B,IAAI;MACjCP,OAAO,EAAE+B,aAAa,CAAC/B,OAAO;MAC9BE,gBAAgB,EAAEkC,QAAQ,CAACjB,aAAa,CAACE,QAAQ,CAAC;MAClDP,oBAAoB,EAAE,CAAC;MACvBC,YAAY,EAAE,IAAIwB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDzB,cAAc,EAAE,IAAI;MACpBX,MAAM,EAAE,aAAa;MACrBD,QAAQ,EAAE2B,aAAa,CAAC3B;IAC1B,CAAC;IAEDV,iBAAiB,CAACkC,IAAI,KAAK;MACzB,GAAGA,IAAI;MACPlB,WAAW,EAAE,CAAC,GAAGkB,IAAI,CAAClB,WAAW,EAAE2B,mBAAmB,CAAC;MACvDzC,qBAAqB,EAAEgC,IAAI,CAAChC,qBAAqB,GAAGwC,QAAQ,CAACjB,aAAa,CAACE,QAAQ,CAAC;MACpFxB,iBAAiB,EAAE+B,IAAI,CAAC/B,iBAAiB,GAAGuC,QAAQ,CAACjB,aAAa,CAACE,QAAQ,CAAC;MAC5EvB,MAAM,EAAE8B,IAAI,CAAC9B,MAAM,CAAC4C,GAAG,CAACT,KAAK,IAC3BA,KAAK,CAAClC,EAAE,KAAKoB,aAAa,CAACR,OAAO,GAC9B;QACE,GAAGsB,KAAK;QACR/B,gBAAgB,EAAE+B,KAAK,CAAC/B,gBAAgB,GAAGkC,QAAQ,CAACjB,aAAa,CAACE,QAAQ,CAAC;QAC3ElB,eAAe,EAAE8B,KAAK,CAAC9B,eAAe,GAAGiC,QAAQ,CAACjB,aAAa,CAACE,QAAQ;MAC1E,CAAC,GACDY,KACN;IACF,CAAC,CAAC,CAAC;IAEHb,gBAAgB,CAAC;MAAET,OAAO,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAG,CAAC,CAAC;IAC9DH,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMyB,0BAA0B,GAAGA,CAACC,YAAY,EAAEvB,QAAQ,KAAK;IAC7D3B,iBAAiB,CAACkC,IAAI,KAAK;MACzB,GAAGA,IAAI;MACPlB,WAAW,EAAEkB,IAAI,CAAClB,WAAW,CAACgC,GAAG,CAACG,UAAU,IAC1CA,UAAU,CAAC9C,EAAE,KAAK6C,YAAY,GAC1B;QAAE,GAAGC,UAAU;QAAE/B,oBAAoB,EAAEsB,QAAQ,CAACf,QAAQ,CAAC,IAAI;MAAE,CAAC,GAChEwB,UACN;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,eAAe,GAAIF,YAAY,IAAK;IACxClD,iBAAiB,CAACkC,IAAI,KAAK;MACzB,GAAGA,IAAI;MACPlB,WAAW,EAAEkB,IAAI,CAAClB,WAAW,CAACgC,GAAG,CAACG,UAAU,IAC1CA,UAAU,CAAC9C,EAAE,KAAK6C,YAAY,GAC1B;QACE,GAAGC,UAAU;QACbxC,MAAM,EAAE,WAAW;QACnBW,cAAc,EAAE,IAAIuB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MACvD,CAAC,GACDI,UACN;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,cAAc,GAAI1C,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACEhB,OAAA;IAAK2D,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC5D,OAAA;MAAK2D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5D,OAAA;QAAA4D,QAAA,EAAI;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpChE,OAAA;QAAA4D,QAAA,EAAG;MAAmD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC1DhE,OAAA;QAAK2D,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B5D,OAAA;UAAA4D,QAAA,EAAM;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B5D,OAAA;QAAK2D,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC5D,OAAA;UAAA4D,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BhE,OAAA;UAAG2D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAExD,cAAc,CAACE;QAAmB;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEhE,OAAA;UAAM2D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACNhE,OAAA;QAAK2D,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC5D,OAAA;UAAA4D,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBhE,OAAA;UAAG2D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAExD,cAAc,CAACG;QAAqB;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEhE,OAAA;UAAM2D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNhE,OAAA;QAAK2D,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC5D,OAAA;UAAA4D,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BhE,OAAA;UAAG2D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAExD,cAAc,CAACI;QAAiB;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEhE,OAAA;UAAM2D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACNhE,OAAA;QAAK2D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5D,OAAA;UAAA4D,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBhE,OAAA;UAAG2D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAAE,CAAExD,cAAc,CAACG,qBAAqB,GAAGH,cAAc,CAACE,mBAAmB,GAAI,GAAG,EAAE2D,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnIhE,OAAA;UAAM2D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B5D,OAAA;QACE2D,SAAS,EAAE,WAAW1B,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACjEiC,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,UAAU,CAAE;QAAA0B,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThE,OAAA;QACE2D,SAAS,EAAE,WAAW1B,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;QACpEiC,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,aAAa,CAAE;QAAA0B,QAAA,EAC5C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThE,OAAA;QACE2D,SAAS,EAAE,WAAW1B,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC/DiC,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,QAAQ,CAAE;QAAA0B,QAAA,EACvC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL/B,SAAS,KAAK,UAAU,iBACvBjC,OAAA;MAAK2D,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B5D,OAAA;QAAK2D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B5D,OAAA;UAAA4D,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BhE,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBxD,cAAc,CAACK,MAAM,CAAC4C,GAAG,CAACT,KAAK,iBAC9B5C,OAAA;YAAoB2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACxC5D,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBAAA4D,QAAA,EAAKhB,KAAK,CAAClC;cAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnBhE,OAAA;gBAAM2D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEhB,KAAK,CAAC5B;cAAM;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACNhE,OAAA;cAAA4D,QAAA,EAAKhB,KAAK,CAACjC;YAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBhE,OAAA;cAAK2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5D,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B5D,OAAA;kBAAA4D,QAAA,GAAM,SAAO,EAAChB,KAAK,CAAChC,aAAa;gBAAA;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACNhE,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B5D,OAAA;kBAAA4D,QAAA,GAAM,YAAU,EAAChB,KAAK,CAAC/B,gBAAgB;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNhE,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B5D,OAAA;kBAAA4D,QAAA,GAAM,WAAS,EAAChB,KAAK,CAAC9B,eAAe;gBAAA;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5D,OAAA;gBACE2D,SAAS,EAAC,eAAe;gBACzBQ,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAIxB,KAAK,CAAC/B,gBAAgB,GAAG+B,KAAK,CAAChC,aAAa,GAAI,GAAG;gBAAI;cAAE;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhE,OAAA;cAAG2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAC,YAAU,EAAChB,KAAK,CAAC7B,QAAQ;YAAA;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAvBpDpB,KAAK,CAAClC,EAAE;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA/B,SAAS,KAAK,aAAa,iBAC1BjC,OAAA;MAAK2D,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B5D,OAAA;QAAK2D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC5D,OAAA;UAAA4D,QAAA,EAAI;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrChE,OAAA;UAAK2D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC5D,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,eACE5D,OAAA;gBAAA4D,QAAA,gBACE5D,OAAA;kBAAA4D,QAAA,EAAI;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBhE,OAAA;kBAAA4D,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChBhE,OAAA;kBAAA4D,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChBhE,OAAA;kBAAA4D,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrBhE,OAAA;kBAAA4D,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrBhE,OAAA;kBAAA4D,QAAA,EAAI;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtBhE,OAAA;kBAAA4D,QAAA,EAAI;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxBhE,OAAA;kBAAA4D,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACfhE,OAAA;kBAAA4D,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRhE,OAAA;cAAA4D,QAAA,EACGxD,cAAc,CAACiB,WAAW,CAACgC,GAAG,CAACG,UAAU,iBACxCxD,OAAA;gBAAA4D,QAAA,gBACE5D,OAAA;kBAAA4D,QAAA,EAAKJ,UAAU,CAAClC;gBAAO;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7BhE,OAAA;kBAAA4D,QAAA,EAAKJ,UAAU,CAAChC;gBAAW;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjChE,OAAA;kBAAA4D,QAAA,EAAKJ,UAAU,CAAC7C;gBAAO;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7BhE,OAAA;kBAAA4D,QAAA,EAAKJ,UAAU,CAAC3C;gBAAgB;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtChE,OAAA;kBAAA4D,QAAA,eACE5D,OAAA;oBACEqE,IAAI,EAAC,QAAQ;oBACbhC,KAAK,EAAEmB,UAAU,CAAC/B,oBAAqB;oBACvC6C,QAAQ,EAAGlC,CAAC,IAAKkB,0BAA0B,CAACE,UAAU,CAAC9C,EAAE,EAAE0B,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;oBAC3EkC,GAAG,EAAEf,UAAU,CAAC3C,gBAAiB;oBACjC2D,GAAG,EAAC,GAAG;oBACPb,SAAS,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLhE,OAAA;kBAAA4D,QAAA,EAAKJ,UAAU,CAAC9B;gBAAY;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClChE,OAAA;kBAAA4D,QAAA,EAAKJ,UAAU,CAAC7B,cAAc,IAAI;gBAAS;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDhE,OAAA;kBAAA4D,QAAA,eACE5D,OAAA;oBACE2D,SAAS,EAAC,cAAc;oBACxBQ,KAAK,EAAE;sBAAEM,eAAe,EAAEf,cAAc,CAACF,UAAU,CAACxC,MAAM;oBAAE,CAAE;oBAAA4C,QAAA,EAE7DJ,UAAU,CAACxC;kBAAM;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLhE,OAAA;kBAAA4D,QAAA,EACGJ,UAAU,CAACxC,MAAM,KAAK,aAAa,iBAClChB,OAAA;oBACE2D,SAAS,EAAC,cAAc;oBACxBO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAACD,UAAU,CAAC9C,EAAE,CAAE;oBAAAkD,QAAA,EAC/C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAlCER,UAAU,CAAC9C,EAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmClB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA/B,SAAS,KAAK,QAAQ,iBACrBjC,OAAA;MAAK2D,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B5D,OAAA;QAAK2D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B5D,OAAA;UAAA4D,QAAA,EAAI;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAElChE,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5D,OAAA;YAAA4D,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBhE,OAAA;YAAM0E,QAAQ,EAAElC,mBAAoB;YAAAoB,QAAA,gBAClC5D,OAAA;cAAK2D,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB5D,OAAA;gBAAK2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5D,OAAA;kBAAA4D,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BhE,OAAA;kBACEkB,IAAI,EAAC,SAAS;kBACdmB,KAAK,EAAEP,aAAa,CAACR,OAAQ;kBAC7BgD,QAAQ,EAAEnC,iBAAkB;kBAC5BwC,QAAQ;kBAAAf,QAAA,gBAER5D,OAAA;oBAAQqC,KAAK,EAAC,EAAE;oBAAAuB,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC3C5D,cAAc,CAACK,MAAM,CACnBmE,MAAM,CAAChC,KAAK,IAAIA,KAAK,CAAC9B,eAAe,GAAG,CAAC,CAAC,CAC1CuC,GAAG,CAACT,KAAK,iBACR5C,OAAA;oBAAuBqC,KAAK,EAAEO,KAAK,CAAClC,EAAG;oBAAAkD,QAAA,GACpChB,KAAK,CAAClC,EAAE,EAAC,KAAG,EAACkC,KAAK,CAACjC,OAAO,EAAC,aAAW,EAACiC,KAAK,CAAC9B,eAAe,EAAC,GAChE;kBAAA,GAFa8B,KAAK,CAAClC,EAAE;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENhE,OAAA;gBAAK2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5D,OAAA;kBAAA4D,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BhE,OAAA;kBACEkB,IAAI,EAAC,WAAW;kBAChBmB,KAAK,EAAEP,aAAa,CAACP,SAAU;kBAC/B+C,QAAQ,EAAEnC,iBAAkB;kBAC5BwC,QAAQ;kBAAAf,QAAA,gBAER5D,OAAA;oBAAQqC,KAAK,EAAC,EAAE;oBAAAuB,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7C5D,cAAc,CAACa,QAAQ,CACrB2D,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAAC1B,SAAS,CAAC,CACpCiC,GAAG,CAACP,OAAO,iBACV9C,OAAA;oBAAyBqC,KAAK,EAAES,OAAO,CAACpC,EAAG;oBAAAkD,QAAA,GACxCd,OAAO,CAAC5B,IAAI,EAAC,cAChB;kBAAA,GAFa4B,OAAO,CAACpC,EAAE;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENhE,OAAA;gBAAK2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5D,OAAA;kBAAA4D,QAAA,EAAO;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjChE,OAAA;kBACEqE,IAAI,EAAC,QAAQ;kBACbnD,IAAI,EAAC,UAAU;kBACfmB,KAAK,EAAEP,aAAa,CAACE,QAAS;kBAC9BsC,QAAQ,EAAEnC,iBAAkB;kBAC5BqC,GAAG,EAAC,GAAG;kBACPD,GAAG,EACDzC,aAAa,CAACR,OAAO,GACjB,EAAAnB,qBAAA,GAAAC,cAAc,CAACK,MAAM,CAACkC,IAAI,CAACkC,CAAC,IAAIA,CAAC,CAACnE,EAAE,KAAKoB,aAAa,CAACR,OAAO,CAAC,cAAAnB,qBAAA,uBAA/DA,qBAAA,CAAiEW,eAAe,KAAI,CAAC,GACrF,CACL;kBACD6D,QAAQ;kBACRG,WAAW,EAAC;gBAAgB;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBACEqE,IAAI,EAAC,QAAQ;gBACbV,SAAS,EAAC,YAAY;gBACtBO,OAAO,EAAEA,CAAA,KAAMnC,gBAAgB,CAAC;kBAAET,OAAO,EAAE,EAAE;kBAAEC,SAAS,EAAE,EAAE;kBAAES,QAAQ,EAAE;gBAAG,CAAC,CAAE;gBAAA4B,QAAA,EAC/E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThE,OAAA;gBAAQqE,IAAI,EAAC,QAAQ;gBAACV,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE7C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNhE,OAAA;UAAK2D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC5D,OAAA;YAAA4D,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BhE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BxD,cAAc,CAACa,QAAQ,CAACoC,GAAG,CAACP,OAAO,iBAClC9C,OAAA;cAAsB2D,SAAS,EAAE,gBAAgBb,OAAO,CAAC1B,SAAS,GAAG,WAAW,GAAG,aAAa,EAAG;cAAAwC,QAAA,gBACjG5D,OAAA;gBAAA4D,QAAA,EAAKd,OAAO,CAAC5B;cAAI;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBhE,OAAA;gBAAA4D,QAAA,GAAG,SAAO,EAACd,OAAO,CAAC3B,KAAK;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BhE,OAAA;gBAAM2D,SAAS,EAAE,sBAAsBb,OAAO,CAAC1B,SAAS,GAAG,WAAW,GAAG,aAAa,EAAG;gBAAAwC,QAAA,EACtFd,OAAO,CAAC1B,SAAS,GAAG,WAAW,GAAG;cAAM;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA,GALClB,OAAO,CAACpC,EAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMf,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC9D,EAAA,CAtfQD,iBAAiB;AAAA8E,EAAA,GAAjB9E,iBAAiB;AAwf1B,eAAeA,iBAAiB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}