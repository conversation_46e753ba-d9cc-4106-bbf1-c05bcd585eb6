import React, { useState, useEffect } from 'react';
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import Hero from './components/pages/Hero';
import About from './components/pages/About';
import Projects from './components/pages/Projects';
import AuthForm from './components/common/AuthForm';
import TranslateWidget from './components/common/TranslateWidget';
import TLDashboard from './components/dashboard/TLDashboard';
import TranslateButton from './components/common/TranslateButton';
import AdminDashboard from './components/dashboard/AdminDashboard';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { userUtils } from './services/api';
import './styles/globals.css';

function App() {
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [showDashboard, setShowDashboard] = useState(true); // Changed to true for direct dashboard access
  const [userInfo, setUserInfo] = useState({
    username: null,
    userType: null,
    verified: false
  });

  // Check authentication status on component mount
  useEffect(() => {
    const userInfo = userUtils.getUserInfo();
    setUserInfo(userInfo);
  }, []);

  // Pass these handlers to Header
  const handleSignIn = () => {
    setAuthModalOpen(true);
  };

  const handleCloseModal = () => setAuthModalOpen(false);

  // Temporarily bypassing authentication for direct dashboard access
  // const canAccessDashboard = userUtils.canAccessDashboard();

  return (
    <GoogleOAuthProvider clientId="###">
      <div className="App">
        <Header onSignIn={handleSignIn} />

        {/* Dashboard Toggle Button - always show for testing */}
        <div style={{ textAlign: 'center', padding: '20px', background: '#f8fafc' }}>
          <button
            onClick={() => setShowDashboard(!showDashboard)}
            style={{
              padding: '12px 24px',
              backgroundColor: '#2c5530',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}
          >
            {showDashboard ? 'Show Main Site' : 'Show Team Leader Dashboard'}
          </button>
        </div>

        {/* Show verification message for unverified users */}
        {userInfo.username && !userInfo.verified && (
          <div style={{ 
            textAlign: 'center', 
            padding: '15px', 
            background: '#fff3cd', 
            borderLeft: '4px solid #ff6b35',
            margin: '0'
          }}>
            <p style={{ margin: 0, color: '#856404' }}>
              <strong>Account Pending Verification:</strong> Your account is awaiting approval from an administrator. 
              You'll receive access to additional features once verified.
            </p>
          </div>
        )}

<<<<<<< HEAD
        {/* Temporarily show TLDashboard directly */}
        {showDashboard ? (
          <TLDashboard />
=======
        {showDashboard && canAccessDashboard ? (
          userInfo.userType === 'admin-pan' ? (
            <AdminDashboard />
          ) : (
            <TLDashboard />
          )
>>>>>>> 232ca5c8332f358bdc6a5a69921328c0376a2dc6
        ) : (
          <main>
            <Hero />
            <About />
            <Projects />
          </main>
        )}

        <Footer />
        <TranslateWidget />
        <AuthForm isOpen={authModalOpen} onClose={handleCloseModal} initialMode="signin" />

        {/* Floating Translate Button */}
        <TranslateButton />
      </div>
    </GoogleOAuthProvider>
  );
}

export default App;
