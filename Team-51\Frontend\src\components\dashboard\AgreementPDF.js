import React, { useState } from 'react';
import jsPDF from 'jspdf';
import './AgreementPDF.css';

function AgreementPDF() {

  const [agreementData, setAgreementData] = useState({
    teamLeader: {
      name: '<PERSON><PERSON>',
      village: 'Kumargram Village',
      clusterId: 'TL-001',
      phone: '+91-9876543212',
      email: '<EMAIL>'
    },
    artisan: {
      name: '',
      phone: '',
      address: '',
      experience: ''
    },
    order: {
      orderId: '',
      product: 'Handwoven Sarees',
      quantity: '',
      pricePerUnit: '',
      totalAmount: '',
      deadline: '',
      qualityStandards: 'Premium handloom quality with traditional patterns'
    },
    terms: {
      advancePayment: '',
      finalPayment: '',
      deliveryTerms: 'Delivery to be made at the designated collection center',
      qualityCheck: 'All products subject to quality inspection before final payment',
      penaltyClause: '2% deduction for each day of delay beyond agreed deadline'
    }
  });

  const [showForm, setShowForm] = useState(false);

  const handleInputChange = (section, field, value) => {
    setAgreementData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const calculateTotalAmount = () => {
    const quantity = parseFloat(agreementData.order.quantity) || 0;
    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;
    const total = quantity * pricePerUnit;
    setAgreementData(prev => ({
      ...prev,
      order: {
        ...prev.order,
        totalAmount: total.toFixed(2)
      }
    }));
  };

  const generatePDF = () => {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    let yPosition = 30;

    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('ARTISAN PRODUCTION AGREEMENT', pageWidth / 2, yPosition, { align: 'center' });

    yPosition += 15;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text('Between Team Leader and Artisan', pageWidth / 2, yPosition, { align: 'center' });

    yPosition += 20;
    doc.setFontSize(10);
    doc.text(`Agreement Date: ${new Date().toLocaleDateString()}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('TEAM LEADER DETAILS:', margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Name: ${agreementData.teamLeader.name}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Village: ${agreementData.teamLeader.village}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Cluster ID: ${agreementData.teamLeader.clusterId}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Phone: ${agreementData.teamLeader.phone}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Email: ${agreementData.teamLeader.email}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('ARTISAN DETAILS:', margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Name: ${agreementData.artisan.name}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Phone: ${agreementData.artisan.phone}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Address: ${agreementData.artisan.address}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Experience: ${agreementData.artisan.experience}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('ORDER DETAILS:', margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Order ID: ${agreementData.order.orderId}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Product: ${agreementData.order.product}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Quantity: ${agreementData.order.quantity} pieces`, margin, yPosition);
    yPosition += 6;
    doc.text(`Price per Unit: ₹${agreementData.order.pricePerUnit}`, margin, yPosition);
    yPosition += 6;
    doc.setFont('helvetica', 'bold');
    doc.text(`Total Amount: ₹${agreementData.order.totalAmount}`, margin, yPosition);
    doc.setFont('helvetica', 'normal');
    yPosition += 6;
    doc.text(`Delivery Deadline: ${agreementData.order.deadline}`, margin, yPosition);
    yPosition += 6;
    doc.text(`Quality Standards: ${agreementData.order.qualityStandards}`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('PAYMENT TERMS:', margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const advance = parseFloat(agreementData.terms.advancePayment);
    const total = parseFloat(agreementData.order.totalAmount);
    const percent = total ? ((advance / total) * 100).toFixed(1) : '0';
    doc.text(`Advance Payment: ₹${agreementData.terms.advancePayment} (${percent}%)`, margin, yPosition);
    yPosition += 6;
    doc.text(`Final Payment: ₹${agreementData.terms.finalPayment} (Upon delivery and quality approval)`, margin, yPosition);
    yPosition += 15;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('TERMS AND CONDITIONS:', margin, yPosition);
    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const terms = [
      `1. Delivery: ${agreementData.terms.deliveryTerms}`,
      `2. Quality: ${agreementData.terms.qualityCheck}`,
      `3. Penalty: ${agreementData.terms.penaltyClause}`,
      `4. The artisan agrees to maintain the specified quality standards throughout production.`,
      `5. Any defective products will be replaced at the artisan's cost.`,
      `6. This agreement is valid for the specified order only.`,
      `7. Both parties agree to resolve disputes through mutual discussion.`
    ];
    terms.forEach(term => {
      doc.text(term, margin, yPosition, { maxWidth: pageWidth - 2 * margin });
      yPosition += 8;
    });
    yPosition += 20;

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('SIGNATURES:', margin, yPosition);
    yPosition += 20;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text('Team Leader:', margin, yPosition);
    doc.text('Artisan:', pageWidth - margin - 60, yPosition);
    yPosition += 15;

    doc.text('_____________________', margin, yPosition);
    doc.text('_____________________', pageWidth - margin - 60, yPosition);
    yPosition += 8;
    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);
    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);
    yPosition += 8;
    doc.text('Date: _______________', margin, yPosition);
    doc.text('Date: _______________', pageWidth - margin - 60, yPosition);

    yPosition += 20;
    doc.setFontSize(8);
    doc.setFont('helvetica', 'italic');
    doc.text('This agreement is generated electronically and is valid without physical signatures for record purposes.', pageWidth / 2, yPosition, { align: 'center' });

    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);
  };

  return (
    <div className="agreement-pdf-container">
      <div className="agreement-header">
        <h2>Generate Artisan Agreement</h2>
        <p>Create a formal agreement between Team Leader and Artisan with pricing details</p>
        <button
          className="toggle-form-btn"
          onClick={() => setShowForm(!showForm)}
        >
          {showForm ? 'Hide Form' : 'Create New Agreement'}
        </button>
      </div>

      {showForm && (
        <div className="agreement-form">
          <div className="form-section">
            <h3>Artisan Information</h3>
            <div className="form-row">
              <div className="form-group">
                <label>Artisan Name *</label>
                <input
                  type="text"
                  value={agreementData.artisan.name}
                  onChange={(e) => handleInputChange('artisan', 'name', e.target.value)}
                  placeholder="Enter artisan's full name"
                  required
                />
              </div>
              <div className="form-group">
                <label>Phone Number *</label>
                <input
                  type="tel"
                  value={agreementData.artisan.phone}
                  onChange={(e) => handleInputChange('artisan', 'phone', e.target.value)}
                  placeholder="+91-XXXXXXXXXX"
                  required
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group full-width">
                <label>Address *</label>
                <textarea
                  value={agreementData.artisan.address}
                  onChange={(e) => handleInputChange('artisan', 'address', e.target.value)}
                  placeholder="Enter complete address"
                  rows="2"
                  required
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label>Experience</label>
                <input
                  type="text"
                  value={agreementData.artisan.experience}
                  onChange={(e) => handleInputChange('artisan', 'experience', e.target.value)}
                  placeholder="e.g., 5 years in handloom"
                />
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3>Order Details</h3>
            <div className="form-row">
              <div className="form-group">
                <label>Order ID *</label>
                <input
                  type="text"
                  value={agreementData.order.orderId}
                  onChange={(e) => handleInputChange('order', 'orderId', e.target.value)}
                  placeholder="e.g., ORD001"
                  required
                />
              </div>
              <div className="form-group">
                <label>Quantity *</label>
                <input
                  type="number"
                  value={agreementData.order.quantity}
                  onChange={(e) => {
                    handleInputChange('order', 'quantity', e.target.value);
                    setTimeout(calculateTotalAmount, 100);
                  }}
                  placeholder="Number of pieces"
                  required
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label>Price per Unit (₹) *</label>
                <input
                  type="number"
                  step="0.01"
                  value={agreementData.order.pricePerUnit}
                  onChange={(e) => {
                    handleInputChange('order', 'pricePerUnit', e.target.value);
                    setTimeout(calculateTotalAmount, 100);
                  }}
                  placeholder="Price per saree"
                  required
                />
              </div>
              <div className="form-group">
                <label>Total Amount (₹)</label>
                <input
                  type="text"
                  value={agreementData.order.totalAmount}
                  readOnly
                  className="readonly-field"
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label>Delivery Deadline *</label>
                <input
                  type="date"
                  value={agreementData.order.deadline}
                  onChange={(e) => handleInputChange('order', 'deadline', e.target.value)}
                  required
                />
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3>Payment Terms</h3>
            <div className="form-row">
              <div className="form-group">
                <label>Advance Payment (₹) *</label>
                <input
                  type="number"
                  step="0.01"
                  value={agreementData.terms.advancePayment}
                  onChange={(e) => {
                    handleInputChange('terms', 'advancePayment', e.target.value);
                    const remaining = parseFloat(agreementData.order.totalAmount) - parseFloat(e.target.value);
                    handleInputChange('terms', 'finalPayment', remaining.toFixed(2));
                  }}
                  placeholder="Advance amount"
                  required
                />
              </div>
              <div className="form-group">
                <label>Final Payment (₹)</label>
                <input
                  type="text"
                  value={agreementData.terms.finalPayment}
                  readOnly
                  className="readonly-field"
                />
              </div>
            </div>
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="cancel-btn"
              onClick={() => setShowForm(false)}
            >
              Cancel
            </button>
            <button
              type="button"
              className="generate-btn"
              onClick={generatePDF}
              disabled={!agreementData.artisan.name || !agreementData.order.orderId || !agreementData.order.quantity}
            >
              Generate PDF Agreement
            </button>
          </div>
        </div>
      )}

      <div className="sample-agreements">
        <h3>Recent Agreements</h3>
        <div className="agreements-list">
          <div className="agreement-item">
            <div className="agreement-info">
              <h4>Agreement with Priya Sharma</h4>
              <p>Order: ORD001 | 40 Handwoven Sarees | ₹32,000</p>
              <span className="agreement-date">Created: 2024-01-10</span>
            </div>
            <button className="download-btn">Download PDF</button>
          </div>
          <div className="agreement-item">
            <div className="agreement-info">
              <h4>Agreement with Meera Patel</h4>
              <p>Order: ORD002 | 45 Handwoven Sarees | ₹36,000</p>
              <span className="agreement-date">Created: 2024-01-08</span>
            </div>
            <button className="download-btn">Download PDF</button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AgreementPDF;
