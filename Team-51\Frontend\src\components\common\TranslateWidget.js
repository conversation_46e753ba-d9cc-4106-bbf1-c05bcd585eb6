import React, { useEffect } from 'react';

const TranslateWidget = () => {
  useEffect(() => {
    // Add Google Translate script to head if not already present
    if (!document.querySelector('script[src*="translate.google.com"]')) {
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
      script.async = true;
      document.head.appendChild(script);
    }

    // Initialize Google Translate
    window.googleTranslateElementInit = function() {
      new window.google.translate.TranslateElement(
        {
          pageLanguage: 'en',
          includedLanguages: 'en,hi,ta,ml,te,kn,fr,de,es',
          layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
          multilanguagePage: true,
          gaTrack: true,
          gaId: 'UA-XXXXX-Y'
        },
        'google_translate_element'
      );
    };

    // If Google Translate is already loaded, initialize immediately
    if (window.google && window.google.translate) {
      window.googleTranslateElementInit();
    }
  }, []);

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      zIndex: 1000,
      background: 'white',
      padding: '10px',
      borderRadius: '8px',
      boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
      border: '1px solid #e5e7eb',
      minWidth: '200px'
    }}>
      <div id="google_translate_element"></div>
    </div>
  );
};

export default TranslateWidget;
