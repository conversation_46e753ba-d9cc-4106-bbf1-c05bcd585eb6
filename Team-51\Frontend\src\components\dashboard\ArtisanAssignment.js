import React, { useState, useEffect } from 'react';
import './ArtisanAssignment.css';

function ArtisanAssignment() {
  const [assignmentData, setAssignmentData] = useState({
    totalOrdersReceived: 500,
    totalProductsAssigned: 475,
    pendingAssignment: 25,
    orders: [
      {
        id: 'ORD001',
        product: 'Handwoven Sarees',
        totalQuantity: 150,
        assignedQuantity: 125,
        pendingQuantity: 25,
        deadline: '2024-02-15',
        status: 'In Progress'
      },
  
    ],
    artisans: [
      { id: 1, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true },
      { id: 2, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true },
      { id: 3, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: false },
      { id: 4, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true },
      { id: 5, name: '<PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true },
      { id: 6, name: '<PERSON><PERSON><PERSON>', skill: 'Handloom & Textile Crafts', available: true }
    ],
    assignments: [
      {
        id: 1,
        orderId: 'ORD001',
        artisanId: 1,
        artisanName: '<PERSON><PERSON>',
        product: 'Handwoven Sarees',
        assignedQuantity: 40,
        manufacturedQuantity: 32,
        assignedDate: '2024-01-10',
        submissionDate: null,
        status: 'In Progress',
        deadline: '2024-02-15'
      },
      {
        id: 2,
        orderId: 'ORD001',
        artisanId: 3,
        artisanName: 'Meera Patel',
        product: 'Handwoven Sarees',
        assignedQuantity: 45,
        manufacturedQuantity: 45,
        assignedDate: '2024-01-08',
        submissionDate: '2024-01-28',
        status: 'Completed',
        deadline: '2024-02-15'
      },
      {
        id: 3,
        orderId: 'ORD001',
        artisanId: 2,
        artisanName: 'Rajesh Kumar',
        product: 'Handwoven Sarees',
        assignedQuantity: 40,
        manufacturedQuantity: 35,
        assignedDate: '2024-01-12',
        submissionDate: null,
        status: 'In Progress',
        deadline: '2024-02-15'
      },
      {
        id: 4,
        orderId: 'ORD002',
        artisanId: 5,
        artisanName: 'Sunita Devi',
        product: 'Handwoven Sarees',
        assignedQuantity: 60,
        manufacturedQuantity: 60,
        assignedDate: '2024-01-15',
        submissionDate: '2024-02-05',
        status: 'Completed',
        deadline: '2024-02-28'
      },
      {
        id: 5,
        orderId: 'ORD002',
        artisanId: 4,
        artisanName: 'Amit Singh',
        product: 'Handwoven Sarees',
        assignedQuantity: 70,
        manufacturedQuantity: 55,
        assignedDate: '2024-01-18',
        submissionDate: null,
        status: 'In Progress',
        deadline: '2024-02-28'
      },
      {
        id: 6,
        orderId: 'ORD002',
        artisanId: 6,
        artisanName: 'Kavita Devi',
        product: 'Handwoven Sarees',
        assignedQuantity: 50,
        manufacturedQuantity: 38,
        assignedDate: '2024-01-20',
        submissionDate: null,
        status: 'In Progress',
        deadline: '2024-02-28'
      },
      {
        id: 7,
        orderId: 'ORD003',
        artisanId: 1,
        artisanName: 'Priya Sharma',
        product: 'Handwoven Sarees',
        assignedQuantity: 35,
        manufacturedQuantity: 20,
        assignedDate: '2024-02-01',
        submissionDate: null,
        status: 'In Progress',
        deadline: '2024-03-10'
      },
      {
        id: 8,
        orderId: 'ORD003',
        artisanId: 2,
        artisanName: 'Rajesh Kumar',
        product: 'Handwoven Sarees',
        assignedQuantity: 40,
        manufacturedQuantity: 25,
        assignedDate: '2024-02-03',
        submissionDate: null,
        status: 'In Progress',
        deadline: '2024-03-10'
      },
      {
        id: 9,
        orderId: 'ORD003',
        artisanId: 4,
        artisanName: 'Amit Singh',
        product: 'Handwoven Sarees',
        assignedQuantity: 40,
        manufacturedQuantity: 15,
        assignedDate: '2024-02-05',
        submissionDate: null,
        status: 'In Progress',
        deadline: '2024-03-10'
      }
    ]
  });

  const [showAssignForm, setShowAssignForm] = useState(false);
  const [newAssignment, setNewAssignment] = useState({
    orderId: '',
    artisanId: '',
    quantity: ''
  });

  const [activeTab, setActiveTab] = useState('overview');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewAssignment(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAssignProduct = (e) => {
    e.preventDefault();
    
    const selectedOrder = assignmentData.orders.find(order => order.id === newAssignment.orderId);
    const selectedArtisan = assignmentData.artisans.find(artisan => artisan.id === parseInt(newAssignment.artisanId));
    
    if (!selectedOrder || !selectedArtisan) return;

    const newAssignmentRecord = {
      id: assignmentData.assignments.length + 1,
      orderId: newAssignment.orderId,
      artisanId: selectedArtisan.id,
      artisanName: selectedArtisan.name,
      product: selectedOrder.product,
      assignedQuantity: parseInt(newAssignment.quantity),
      manufacturedQuantity: 0,
      assignedDate: new Date().toISOString().split('T')[0],
      submissionDate: null,
      status: 'In Progress',
      deadline: selectedOrder.deadline
    };

    setAssignmentData(prev => ({
      ...prev,
      assignments: [...prev.assignments, newAssignmentRecord],
      totalProductsAssigned: prev.totalProductsAssigned + parseInt(newAssignment.quantity),
      pendingAssignment: prev.pendingAssignment - parseInt(newAssignment.quantity),
      orders: prev.orders.map(order => 
        order.id === newAssignment.orderId 
          ? {
              ...order,
              assignedQuantity: order.assignedQuantity + parseInt(newAssignment.quantity),
              pendingQuantity: order.pendingQuantity - parseInt(newAssignment.quantity)
            }
          : order
      )
    }));

    setNewAssignment({ orderId: '', artisanId: '', quantity: '' });
    setShowAssignForm(false);
  };

  const updateManufacturedQuantity = (assignmentId, quantity) => {
    setAssignmentData(prev => ({
      ...prev,
      assignments: prev.assignments.map(assignment =>
        assignment.id === assignmentId
          ? { ...assignment, manufacturedQuantity: parseInt(quantity) || 0 }
          : assignment
      )
    }));
  };

  const markAsCompleted = (assignmentId) => {
    setAssignmentData(prev => ({
      ...prev,
      assignments: prev.assignments.map(assignment =>
        assignment.id === assignmentId
          ? { 
              ...assignment, 
              status: 'Completed',
              submissionDate: new Date().toISOString().split('T')[0]
            }
          : assignment
      )
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return '#10b981';
      case 'In Progress': return '#3b82f6';
      case 'Overdue': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <div className="artisan-assignment">
      <div className="assignment-header">
        <h1>Handwoven Sarees Production</h1>
        <p>Manage saree assignments and track artisan progress</p>
        <div className="product-badge">
          <span>🧵 Product: Handwoven Sarees</span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="summary-cards">
        <div className="summary-card primary">
          <h3>Total Sarees Ordered</h3>
          <p className="summary-number">{assignmentData.totalOrdersReceived}</p>
          <span className="summary-label">Sarees</span>
        </div>
        <div className="summary-card success">
          <h3>Sarees Assigned</h3>
          <p className="summary-number">{assignmentData.totalProductsAssigned}</p>
          <span className="summary-label">To Artisans</span>
        </div>
        <div className="summary-card warning">
          <h3>Pending Assignment</h3>
          <p className="summary-number">{assignmentData.pendingAssignment}</p>
          <span className="summary-label">Sarees</span>
        </div>
        <div className="summary-card info">
          <h3>Assignment Rate</h3>
          <p className="summary-number">{((assignmentData.totalProductsAssigned / assignmentData.totalOrdersReceived) * 100).toFixed(1)}%</p>
          <span className="summary-label">Completed</span>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="assignment-tabs">
        <button
          className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Saree Orders
        </button>
        <button
          className={`tab-btn ${activeTab === 'assignments' ? 'active' : ''}`}
          onClick={() => setActiveTab('assignments')}
        >
          Artisan Assignments
        </button>
        <button
          className={`tab-btn ${activeTab === 'assign' ? 'active' : ''}`}
          onClick={() => setActiveTab('assign')}
        >
          Assign Sarees
        </button>
      </div>

      {/* Orders Overview Tab */}
      {activeTab === 'overview' && (
        <div className="tab-content">
          <div className="orders-overview">
            <h2>Current Saree Orders</h2>
            <div className="orders-grid">
              {assignmentData.orders.map(order => (
                <div key={order.id} className="order-card">
                  <div className="order-header">
                    <h3>{order.id}</h3>
                    <span className="order-status">{order.status}</span>
                  </div>
                  <h4>{order.product}</h4>
                  <div className="order-progress">
                    <div className="progress-item">
                      <span>Total: {order.totalQuantity}</span>
                    </div>
                    <div className="progress-item">
                      <span>Assigned: {order.assignedQuantity}</span>
                    </div>
                    <div className="progress-item">
                      <span>Pending: {order.pendingQuantity}</span>
                    </div>
                  </div>
                  <div className="progress-bar">
                    <div 
                      className="progress-fill"
                      style={{ width: `${(order.assignedQuantity / order.totalQuantity) * 100}%` }}
                    ></div>
                  </div>
                  <p className="order-deadline">Deadline: {order.deadline}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Assignments Tab */}
      {activeTab === 'assignments' && (
        <div className="tab-content">
          <div className="assignments-section">
            <h2>Saree Production Assignments</h2>
            <div className="assignments-table">
              <table>
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Artisan</th>
                    <th>Product</th>
                    <th>Assigned Qty</th>
                    <th>Manufactured</th>
                    <th>Assigned Date</th>
                    <th>Submission Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {assignmentData.assignments.map(assignment => (
                    <tr key={assignment.id}>
                      <td>{assignment.orderId}</td>
                      <td>{assignment.artisanName}</td>
                      <td>{assignment.product}</td>
                      <td>{assignment.assignedQuantity}</td>
                      <td>
                        <input
                          type="number"
                          value={assignment.manufacturedQuantity}
                          onChange={(e) => updateManufacturedQuantity(assignment.id, e.target.value)}
                          max={assignment.assignedQuantity}
                          min="0"
                          className="quantity-input"
                        />
                      </td>
                      <td>{assignment.assignedDate}</td>
                      <td>{assignment.submissionDate || 'Pending'}</td>
                      <td>
                        <span 
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(assignment.status) }}
                        >
                          {assignment.status}
                        </span>
                      </td>
                      <td>
                        {assignment.status === 'In Progress' && (
                          <button
                            className="complete-btn"
                            onClick={() => markAsCompleted(assignment.id)}
                          >
                            Mark Complete
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Assign Products Tab */}
      {activeTab === 'assign' && (
        <div className="tab-content">
          <div className="assign-section">
            <h2>Assign Sarees to Artisans</h2>

            <div className="assign-form">
              <h3>New Assignment</h3>
              <form onSubmit={handleAssignProduct}>
                <div className="form-row">
                  <div className="form-group">
                    <label>Select Order</label>
                    <select
                      name="orderId"
                      value={newAssignment.orderId}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Choose an order...</option>
                      {assignmentData.orders
                        .filter(order => order.pendingQuantity > 0)
                        .map(order => (
                          <option key={order.id} value={order.id}>
                            {order.id} - {order.product} (Pending: {order.pendingQuantity})
                          </option>
                        ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Select Artisan</label>
                    <select
                      name="artisanId"
                      value={newAssignment.artisanId}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Choose an artisan...</option>
                      {assignmentData.artisans
                        .filter(artisan => artisan.available)
                        .map(artisan => (
                          <option key={artisan.id} value={artisan.id}>
                            {artisan.name} - Available
                          </option>
                        ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Quantity to Assign</label>
                    <input
                      type="number"
                      name="quantity"
                      value={newAssignment.quantity}
                      onChange={handleInputChange}
                      min="1"
                      max={
                        newAssignment.orderId
                          ? assignmentData.orders.find(o => o.id === newAssignment.orderId)?.pendingQuantity || 0
                          : 0
                      }
                      required
                      placeholder="Enter quantity"
                    />
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setNewAssignment({ orderId: '', artisanId: '', quantity: '' })}
                  >
                    Clear
                  </button>
                  <button type="submit" className="assign-btn">
                    Assign Product
                  </button>
                </div>
              </form>
            </div>

            {/* Available Artisans */}
            <div className="available-artisans">
              <h3>Available Artisans</h3>
              <div className="artisans-grid">
                {assignmentData.artisans.map(artisan => (
                  <div key={artisan.id} className={`artisan-card ${artisan.available ? 'available' : 'unavailable'}`}>
                    <h4>{artisan.name}</h4>
                    <p>Skill: {artisan.skill}</p>
                    <span className={`availability-badge ${artisan.available ? 'available' : 'unavailable'}`}>
                      {artisan.available ? 'Available' : 'Busy'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ArtisanAssignment;
