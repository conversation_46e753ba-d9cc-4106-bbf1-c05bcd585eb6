import React, { useState, useEffect } from 'react';
import './TLDashboard.css';

function TLDashboard() {
  const [dashboardData, setDashboardData] = useState({
    teamLeader: {
      name: '<PERSON><PERSON>',
      village: 'Kumargram Village',
      skill: 'Handloom & Textile Crafts',
      experience: '8 years',
      clusterId: 'TL-001'
    },
    personalIncome: {
      monthly: 15000,
      clusterRevenue: 150000,
      sharePercentage: 10
    },
    monthlyBreakdown: [
      { month: 'January', income: 12000, orders: 45 },
      { month: 'February', income: 15000, orders: 52 },
      { month: 'March', income: 18000, orders: 60 },
      { month: 'April', income: 16500, orders: 55 },
      { month: 'May', income: 19000, orders: 65 },
      { month: 'June', income: 21000, orders: 70 }
    ],
    artisans: [
      { id: 1, name: '<PERSON><PERSON>', performance: 92, paymentStatus: 'Paid', orders: 25, revenue: 45000 },
      { id: 2, name: '<PERSON><PERSON>', performance: 88, paymentStatus: 'Pending', orders: 22, revenue: 38000 },
      { id: 3, name: '<PERSON><PERSON>', performance: 95, paymentStatus: 'Paid', orders: 28, revenue: 52000 },
      { id: 4, name: '<PERSON><PERSON>', performance: 85, paymentStatus: 'Paid', orders: 20, revenue: 35000 },
      { id: 5, name: 'Sunita Devi', performance: 90, paymentStatus: 'Pending', orders: 24, revenue: 42000 }
    ],
    orders: {
      current: [
        { id: 'ORD001', product: 'Handwoven Sarees', quantity: 50, status: 'In Progress', deadline: '2024-01-15' },
        { id: 'ORD002', product: 'Pottery Sets', quantity: 30, status: 'Quality Check', deadline: '2024-01-20' },
        { id: 'ORD003', product: 'Bamboo Crafts', quantity: 75, status: 'Production', deadline: '2024-01-25' }
      ],
      past: [
        { id: 'ORD004', product: 'Textile Products', quantity: 100, status: 'Delivered', completedDate: '2023-12-28' },
        { id: 'ORD005', product: 'Wooden Handicrafts', quantity: 60, status: 'Delivered', completedDate: '2023-12-20' }
      ]
    },
    deliveryStats: {
      delivered: 850,
      loss: 45,
      totalProduced: 895,
      deliveryRate: 94.97
    }
  });

  const [activeTab, setActiveTab] = useState('overview');

  const getPerformanceColor = (performance) => {
    if (performance >= 90) return '#10b981';
    if (performance >= 80) return '#f59e0b';
    return '#ef4444';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Paid': return '#10b981';
      case 'Pending': return '#f59e0b';
      case 'Delivered': return '#10b981';
      case 'In Progress': return '#3b82f6';
      case 'Quality Check': return '#f59e0b';
      case 'Production': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  return (
    <div className="tl-dashboard">
      <div className="dashboard-header">
        <div className="leader-info">
          <h1>Team Leader Dashboard</h1>
          <div className="leader-details">
            <div className="leader-name">
              <h2>{dashboardData.teamLeader.name}</h2>
              <span className="leader-id">ID: {dashboardData.teamLeader.clusterId}</span>
            </div>
            <div className="leader-meta">
              <div className="meta-item">
                <span className="meta-label">Village:</span>
                <span className="meta-value">{dashboardData.teamLeader.village}</span>
              </div>
              <div className="meta-item">
                <span className="meta-label">Specialization:</span>
                <span className="meta-value">{dashboardData.teamLeader.skill}</span>
              </div>
              <div className="meta-item">
                <span className="meta-label">Experience:</span>
                <span className="meta-value">{dashboardData.teamLeader.experience}</span>
              </div>
            </div>
          </div>
        </div>
        <p className="welcome-message">Welcome back! Here's your cluster overview</p>
      </div>

      <div className="dashboard-tabs">
        <button 
          className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button 
          className={`tab-btn ${activeTab === 'artisans' ? 'active' : ''}`}
          onClick={() => setActiveTab('artisans')}
        >
          Artisans
        </button>
        <button 
          className={`tab-btn ${activeTab === 'orders' ? 'active' : ''}`}
          onClick={() => setActiveTab('orders')}
        >
          Orders
        </button>
        <button 
          className={`tab-btn ${activeTab === 'analytics' ? 'active' : ''}`}
          onClick={() => setActiveTab('analytics')}
        >
          Analytics
        </button>
      </div>

      {activeTab === 'overview' && (
        <div className="dashboard-content">
          {/* Personal Income Section */}
          <div className="income-section">
            <h2>Personal Income (10% Share)</h2>
            <div className="income-cards">
              <div className="income-card primary">
                <h3>Monthly Income</h3>
                <p className="amount">₹{dashboardData.personalIncome.monthly.toLocaleString()}</p>
                <span className="subtitle">Current Month</span>
              </div>
              <div className="income-card">
                <h3>Cluster Revenue</h3>
                <p className="amount">₹{dashboardData.personalIncome.clusterRevenue.toLocaleString()}</p>
                <span className="subtitle">Total Revenue</span>
              </div>
              <div className="income-card">
                <h3>Your Share</h3>
                <p className="amount">{dashboardData.personalIncome.sharePercentage}%</p>
                <span className="subtitle">Commission Rate</span>
              </div>
            </div>
          </div>

          {/* Monthly Breakdown */}
          <div className="breakdown-section">
            <h2>Monthly Income Breakdown</h2>
            <div className="breakdown-chart">
              {dashboardData.monthlyBreakdown.map((month, index) => (
                <div key={index} className="month-bar">
                  <div 
                    className="bar" 
                    style={{ height: `${(month.income / 25000) * 100}%` }}
                  ></div>
                  <span className="month-label">{month.month.slice(0, 3)}</span>
                  <span className="income-label">₹{month.income.toLocaleString()}</span>
                  <span className="orders-label">{month.orders} orders</span>
                </div>
              ))}
            </div>
          </div>

          {/* Delivery Stats */}
          <div className="delivery-section">
            <h2>Delivery Performance</h2>
            <div className="delivery-stats">
              <div className="stat-card success">
                <h3>Delivered</h3>
                <p>{dashboardData.deliveryStats.delivered}</p>
              </div>
              <div className="stat-card warning">
                <h3>Loss/Damage</h3>
                <p>{dashboardData.deliveryStats.loss}</p>
              </div>
              <div className="stat-card info">
                <h3>Total Produced</h3>
                <p>{dashboardData.deliveryStats.totalProduced}</p>
              </div>
              <div className="stat-card primary">
                <h3>Success Rate</h3>
                <p>{dashboardData.deliveryStats.deliveryRate}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'artisans' && (
        <div className="dashboard-content">
          <div className="artisans-section">
            <h2>Artisan Performance</h2>
            <div className="artisans-table">
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Performance</th>
                    <th>Payment Status</th>
                    <th>Orders</th>
                    <th>Revenue</th>
                  </tr>
                </thead>
                <tbody>
                  {dashboardData.artisans.map(artisan => (
                    <tr key={artisan.id}>
                      <td>{artisan.name}</td>
                      <td>
                        <div className="performance-cell">
                          <span 
                            className="performance-score"
                            style={{ color: getPerformanceColor(artisan.performance) }}
                          >
                            {artisan.performance}%
                          </span>
                          <div className="performance-bar">
                            <div 
                              className="performance-fill"
                              style={{ 
                                width: `${artisan.performance}%`,
                                backgroundColor: getPerformanceColor(artisan.performance)
                              }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span 
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(artisan.paymentStatus) }}
                        >
                          {artisan.paymentStatus}
                        </span>
                      </td>
                      <td>{artisan.orders}</td>
                      <td>₹{artisan.revenue.toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'orders' && (
        <div className="dashboard-content">
          <div className="orders-section">
            <h2>Current Orders</h2>
            <div className="orders-grid">
              {dashboardData.orders.current.map(order => (
                <div key={order.id} className="order-card">
                  <div className="order-header">
                    <h3>{order.id}</h3>
                    <span 
                      className="order-status"
                      style={{ backgroundColor: getStatusColor(order.status) }}
                    >
                      {order.status}
                    </span>
                  </div>
                  <p className="order-product">{order.product}</p>
                  <p className="order-quantity">Quantity: {order.quantity}</p>
                  <p className="order-deadline">Deadline: {order.deadline}</p>
                </div>
              ))}
            </div>

            <h2>Past Orders</h2>
            <div className="orders-grid">
              {dashboardData.orders.past.map(order => (
                <div key={order.id} className="order-card completed">
                  <div className="order-header">
                    <h3>{order.id}</h3>
                    <span 
                      className="order-status"
                      style={{ backgroundColor: getStatusColor(order.status) }}
                    >
                      {order.status}
                    </span>
                  </div>
                  <p className="order-product">{order.product}</p>
                  <p className="order-quantity">Quantity: {order.quantity}</p>
                  <p className="order-completed">Completed: {order.completedDate}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="dashboard-content">
          <div className="analytics-section">
            <h2>Performance Analytics</h2>
            <div className="analytics-grid">
              <div className="analytics-card">
                <h3>Average Artisan Performance</h3>
                <p className="analytics-value">90%</p>
                <span className="analytics-trend positive">+5% from last month</span>
              </div>
              <div className="analytics-card">
                <h3>Order Completion Rate</h3>
                <p className="analytics-value">94.97%</p>
                <span className="analytics-trend positive">+2.3% from last month</span>
              </div>
              <div className="analytics-card">
                <h3>Revenue Growth</h3>
                <p className="analytics-value">+15%</p>
                <span className="analytics-trend positive">Monthly growth</span>
              </div>
              <div className="analytics-card">
                <h3>Active Artisans</h3>
                <p className="analytics-value">5</p>
                <span className="analytics-trend neutral">No change</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default TLDashboard;
