import React, { useState } from 'react';
import './Header.css';

function Header({ onSignIn, onSignUp }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const username = localStorage.getItem('username');

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleLogout = () => {
    localStorage.clear();
    window.location.reload();
  };

  return (
    <header className="header">
      <div className="header-container">
        <div className="header-logo">
          <img 
            src="/logo192.png" 
            alt="PANS NGO Logo" 
            className="header-logo-img"
          />
          <div className="header-logo-text">
            <h1>PANS</h1>
            <span className="header-tagline">Empowering Communities</span>
          </div>
        </div>

        <nav className={`header-nav ${isMenuOpen ? 'header-nav-open' : ''}`}>
          <ul className="header-nav-list">
            <li><a href="#home" className="header-nav-link">Home</a></li>
            <li><a href="#about" className="header-nav-link">About Us</a></li>
            <li><a href="#programs" className="header-nav-link">Programs</a></li>
            <li><a href="#get-involved" className="header-nav-link">Get Involved</a></li>
            <li><a href="#contact" className="header-nav-link">Contact</a></li>
          </ul>
        </nav>

        <div className="header-actions">
          {username ? (
            <div className="header-user-menu">
              <span className="header-welcome">
                Welcome, <strong>{username}</strong>
              </span>
              <button className="header-btn header-btn-logout" onClick={handleLogout}>
                Logout
              </button>
            </div>
          ) : (
            <div className="header-auth-buttons">
              <button className="header-btn header-btn-secondary" onClick={onSignIn}>
                Sign In
              </button>
              <button className="header-btn header-btn-primary" onClick={onSignUp}>
                Join Us
              </button>
            </div>
          )}
        </div>

        <button className="header-menu-toggle" onClick={toggleMenu}>
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </header>
  );
}

export default Header;
