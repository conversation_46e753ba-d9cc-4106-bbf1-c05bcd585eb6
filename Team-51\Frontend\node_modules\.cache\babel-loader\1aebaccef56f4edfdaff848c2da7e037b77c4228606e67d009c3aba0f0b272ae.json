{"ast": null, "code": "import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport const setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nexport const getDefaults = () => defaultOptions;", "map": {"version": 3, "names": ["unescape", "defaultOptions", "bindI18n", "bindI18nStore", "transEmptyNodeValue", "transSupportBasicHtmlNodes", "transWrapTextNodes", "transKeepBasicHtmlNodesFor", "useSuspense", "setDefaults", "options", "getDefaults"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/node_modules/react-i18next/dist/es/defaults.js"], "sourcesContent": ["import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport const setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nexport const getDefaults = () => defaultOptions;"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,IAAIC,cAAc,GAAG;EACnBC,QAAQ,EAAE,iBAAiB;EAC3BC,aAAa,EAAE,EAAE;EACjBC,mBAAmB,EAAE,EAAE;EACvBC,0BAA0B,EAAE,IAAI;EAChCC,kBAAkB,EAAE,EAAE;EACtBC,0BAA0B,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC;EACtDC,WAAW,EAAE,IAAI;EACjBR;AACF,CAAC;AACD,OAAO,MAAMS,WAAW,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;EAC3CT,cAAc,GAAG;IACf,GAAGA,cAAc;IACjB,GAAGS;EACL,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAMV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}