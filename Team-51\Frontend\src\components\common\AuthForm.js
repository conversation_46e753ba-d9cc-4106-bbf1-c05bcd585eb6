import React, { useState, useEffect } from 'react';
import { GoogleLogin } from '@react-oauth/google';
import { jwtDecode } from 'jwt-decode';
import { authAPI, userUtils } from '../../services/api';
import './AuthForm.css';

function AuthForm({ isOpen, onClose }) {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setError('');
    setSuccess('');
    setFormData({
      username: '',
      password: ''
    });
  }, [isOpen]);

  if (!isOpen) return null;

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    try {
      const payload = { username: formData.username, password: formData.password };
      const response = await authAPI.signin(payload);
      
      // Store user data using utility function
      userUtils.setUserInfo({
        token: response.data.token,
        username: formData.username,
        user_id: response.data.user_id,
        type: response.data.type,
        verified: response.data.verified
      });
      
      setSuccess('Welcome back! Redirecting...');
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      setError(err.response?.data?.msg || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSuccess = async (credentialResponse) => {
    try {
      const decoded = jwtDecode(credentialResponse.credential);
      
      const payload = {
        email: decoded.email,
        name: decoded.name,
        picture: decoded.picture
      };
      
      const res = await authAPI.googleAuth(payload);
      
      // Store user data using utility function
      userUtils.setUserInfo({
        token: res.data.token,
        username: res.data.username,
        user_id: res.data.user_id,
        type: res.data.type,
        verified: res.data.verified
      });
      
      setSuccess('Welcome to PANS NGO! Redirecting...');
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      const errorMsg = err.response?.data?.msg || 'Google authentication failed. Please try again.';
      setError(errorMsg);
    }
  };

  return (
    <div className="auth-modal-overlay" onClick={onClose}>
      <div className="auth-modal" onClick={(e) => e.stopPropagation()}>
        <div className="auth-modal-header">
          <h2 className="auth-modal-title">
            Welcome Back
          </h2>
          <p className="auth-modal-subtitle">
            Sign in to continue your journey with us
          </p>
          <button className="auth-modal-close" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="auth-modal-body">
          {error && <div className="auth-alert auth-alert-error">{error}</div>}
          {success && <div className="auth-alert auth-alert-success">{success}</div>}

          <form onSubmit={handleSubmit} className="auth-form">
            <div className="auth-form-group">
              <label className="auth-form-label">Username</label>
              <div className="auth-input-wrapper">
                <i className="fas fa-user auth-input-icon"></i>
                <input
                  type="text"
                  name="username"
                  placeholder="Enter your username"
                  className="auth-input"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  autoFocus
                />
              </div>
            </div>

            <div className="auth-form-group">
              <label className="auth-form-label">Password</label>
              <div className="auth-input-wrapper">
                <i className="fas fa-lock auth-input-icon"></i>
                <input
                  type="password"
                  name="password"
                  placeholder="Enter your password"
                  className="auth-input"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <button 
              type="submit" 
              className="auth-btn auth-btn-primary"
              disabled={loading}
            >
              {loading ? (
                <span className="auth-btn-loading">
                  <i className="fas fa-spinner fa-spin"></i>
                  Signing In...
                </span>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          <div className="auth-divider">
            <span>or continue with</span>
          </div>

          <div className="auth-google-wrapper">
            <GoogleLogin
              onSuccess={handleGoogleSuccess}
              onError={() => setError('Google authentication failed')}
              width="100%"
              text="signin_with"
              size="large"
            />
          </div>

        </div>
      </div>
    </div>
  );
}

export default AuthForm;
