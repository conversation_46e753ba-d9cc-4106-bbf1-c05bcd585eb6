import React, { useEffect } from 'react';
import './GoogleTranslate.css';

function GoogleTranslate() {
  useEffect(() => {
    // Add Google Translate script
    const addScript = () => {
      if (!window.google || !window.google.translate) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
        script.async = true;
        document.head.appendChild(script);
      }
    };

    // Initialize Google Translate
    window.googleTranslateElementInit = () => {
      if (window.google && window.google.translate) {
        new window.google.translate.TranslateElement(
          {
            pageLanguage: 'en',
            includedLanguages: 'en,hi,ta,te,bn,gu,kn,ml,mr,pa,ur,as,or',
            layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
            autoDisplay: false,
            multilanguagePage: true
          },
          'google_translate_element'
        );
      }
    };

    // Load the script
    addScript();

    // Cleanup function
    return () => {
      // Remove the script when component unmounts
      const scripts = document.querySelectorAll('script[src*="translate.google.com"]');
      scripts.forEach(script => script.remove());
      
      // Remove the translate element
      const translateElement = document.getElementById('google_translate_element');
      if (translateElement) {
        translateElement.innerHTML = '';
      }
    };
  }, []);

  return (
    <div className="google-translate-container">
      <div className="translate-header">
        <span className="translate-icon">🌐</span>
        <span className="translate-label">Translate</span>
      </div>
      <div id="google_translate_element" className="google-translate-widget"></div>
    </div>
  );
}

export default GoogleTranslate;
