// models/Order.js
const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  order_id: {
    type: String,
    required: true,
    unique: true
  },
  product_id: {
    type: String,
    required: true
  },
  product_name: {
    type: String,
    required: true
  },
  buyer_name: {
    type: String,
    required: true
  },
  buyer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Buyer',
    required: false
  },
  team_leads: {
    type: [String],
    required: true,
    validate: {
      validator: function(v) {
        return v.length > 0;
      },
      message: 'At least one team lead is required'
    }
  },
  quantities: {
    type: [Number],
    required: true,
    validate: {
      validator: function(v) {
        return v.length > 0 && v.every(qty => qty > 0);
      },
      message: 'At least one quantity is required and all quantities must be positive'
    }
  },
  total_qty: {
    type: Number,
    required: true,
    validate: {
      validator: function(v) {
        return this.quantities && v === this.quantities.reduce((sum, qty) => sum + qty, 0);
      },
      message: 'Total quantity must equal the sum of individual quantities'
    }
  },
  total_amount: {
    type: Number,
    required: true,
    default: 0
  },
  commission_percent: {
    type: Number,
    required: true,
    default: 10
  },
  date_ordered: {
    type: Date,
    required: true
  },
  deadline: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['to be done', 'in progress', 'completed', 'delayed'],
    default: 'to be done'
  },
  remark: {
    type: String
  },
  progress_updates: [{
    team_leader: {
      type: String,
      required: true
    },
    status: {
      type: String,
      required: true
    },
    fault_products: {
      type: Number,
      default: 0
    },
    remarks: {
      type: String,
      default: ''
    },
    updated_at: {
      type: Date,
      default: Date.now
    }
  }],
  artisan_assignments: [{
    team_leader_id: {
      type: String,
      required: true
    },
    artisans: [{
      artisan_id: {
        type: String,
        required: true
      },
      artisan_name: {
        type: String,
        required: true
      },
      assigned_quantity: {
        type: Number,
        required: true
      },
      completed_quantity: {
        type: Number,
        default: 0
      },
      status: {
        type: String,
        enum: ['assigned', 'in progress', 'completed'],
        default: 'assigned'
      },
      assigned_at: {
        type: Date,
        default: Date.now
      }
    }]
  }]
}, { timestamps: true });

// Pre-save middleware to calculate total_qty if not provided
orderSchema.pre('save', function(next) {
  if (!this.total_qty && this.quantities) {
    this.total_qty = this.quantities.reduce((sum, qty) => sum + qty, 0);
  }
  next();
});

module.exports = mongoose.model('Order', orderSchema);