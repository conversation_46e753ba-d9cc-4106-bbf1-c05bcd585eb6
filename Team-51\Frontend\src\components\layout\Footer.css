/* Footer Styles */
.footer {
  background: linear-gradient(135deg, #1e3a23 0%, #2c5530 100%);
  color: white;
  padding: 60px 0 20px 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #b8e6c1;
}

.footer-logo h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: white;
}

.footer-logo p {
  color: #94a3b8;
  font-size: 0.95rem;
  margin-bottom: 20px;
}

.footer-social {
  display: flex;
  gap: 15px;
}

.footer-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-social-link:hover {
  background: #3d7b47;
  transform: translateY(-2px);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #94a3b8;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #b8e6c1;
}

.footer-contact p {
  color: #94a3b8;
  font-size: 0.95rem;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-contact i {
  color: #3d7b47;
  width: 16px;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-copyright p {
  color: #94a3b8;
  font-size: 0.9rem;
  margin: 0;
}

.footer-legal {
  display: flex;
  gap: 20px;
}

.footer-legal a {
  color: #94a3b8;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-legal a:hover {
  color: #b8e6c1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
  }

  .footer-section:nth-child(4),
  .footer-section:nth-child(5) {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px 0;
  }

  .footer-container {
    padding: 0 15px;
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 30px;
  }

  .footer-section:first-child {
    grid-column: span 2;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .footer-legal {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .footer-section:first-child {
    grid-column: span 1;
  }

  .footer-logo h3 {
    font-size: 1.5rem;
  }

  .footer-social {
    justify-content: center;
  }

  .footer-legal {
    flex-direction: column;
    gap: 10px;
  }
}

/* Font Awesome Icons (if not already included) */
.fas, .fab {
  font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Brands';
  font-weight: 900;
}
