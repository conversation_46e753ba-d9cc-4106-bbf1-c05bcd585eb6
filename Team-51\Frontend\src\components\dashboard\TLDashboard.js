import React, { useState, useEffect } from 'react';
import ArtisanAssignment from './ArtisanAssignment';
import AgreementPDF from './AgreementPDF';
import './TLDashboard.css';
import api, { userUtils } from '../../services/api';
import ArtisanAssignmentModal from '../ArtisanAssignmentModal';
import ArtisanPieChart from '../ArtisanPieChart';

function TLDashboard() {
  const [dashboardData, setDashboardData] = useState({
    teamLeader: {
      name: 'Rashmi Shukla',
      village: 'Kumargram Village',
      skill: 'Handloom & Textile Crafts',
      experience: '8 years',
      clusterId: 'TL-001'
    },
    personalIncome: {
      monthly: 15000,
      clusterRevenue: 150000,
      sharePercentage: 10
    },
    monthlyBreakdown: [
      { month: 'January', income: 12000, orders: 45 },
      { month: 'February', income: 15000, orders: 52 },
      { month: 'March', income: 18000, orders: 60 },
      { month: 'April', income: 16500, orders: 55 },
      { month: 'May', income: 19000, orders: 65 },
      { month: 'June', income: 21000, orders: 70 }
    ],
    artisans: [
      { id: 1, name: '<PERSON><PERSON>', performance: 92, paymentStatus: 'Paid', orders: 25, revenue: 45000 },
      { id: 2, name: 'Rajesh Kumar', performance: 88, paymentStatus: 'Pending', orders: 22, revenue: 38000 },
      { id: 3, name: 'Meera Patel', performance: 95, paymentStatus: 'Paid', orders: 28, revenue: 52000 },
      { id: 4, name: 'Amit Singh', performance: 85, paymentStatus: 'Paid', orders: 20, revenue: 35000 },
      { id: 5, name: 'Sunita Devi', performance: 90, paymentStatus: 'Pending', orders: 24, revenue: 42000 }
    ],
    orders: {
<<<<<<< HEAD
      current: [
        { id: 'ORD001', product: 'Handwoven Sarees', quantity: 50, status: 'In Progress', deadline: '2024-01-15' },
      ],
      past: [
       { id: 'ORD001', product: 'Handwoven Sarees', quantity: 50, status: 'Done', deadline: '2024-01-15' },
      ]
=======
      current: [],
      past: []
>>>>>>> 232ca5c8332f358bdc6a5a69921328c0376a2dc6
    },
    deliveryStats: {
      delivered: 850,
      loss: 45,
      totalProduced: 895,
      deliveryRate: 94.97
    }
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [updateForm, setUpdateForm] = useState({
    status: '',
    fault_products: 0,
    remarks: ''
  });
  const [showArtisanAssignmentModal, setShowArtisanAssignmentModal] = useState(false);
  const [selectedOrderForAssignment, setSelectedOrderForAssignment] = useState(null);
  const [artisans, setArtisans] = useState([]);
  const [artisanPerformance, setArtisanPerformance] = useState({});

  const [activeTab, setActiveTab] = useState('overview');

  // Fetch orders for the team leader
  const fetchTeamLeaderOrders = async () => {
    try {
      setLoading(true);
      // Get team leader ID from user context
      const userInfo = userUtils.getUserInfo();
      const teamLeaderId = userInfo.username || 'TL001'; // Fallback to TL001 if not found
      const response = await api.get(`/orders/team-leader/${teamLeaderId}`);
      setDashboardData(prev => ({
        ...prev,
        orders: response.data
      }));
    } catch (error) {
      setError('Failed to fetch orders');
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch artisans for the team leader
  const fetchArtisans = async () => {
    try {
      const userInfo = userUtils.getUserInfo();
      const teamLeaderId = userInfo.username || 'TL001';
      const response = await api.get(`/artisans/team-leader/${teamLeaderId}`);
      setArtisans(response.data);
      
      // Calculate artisan performance based on assignments
      calculateArtisanPerformance(response.data);
    } catch (error) {
      console.error('Error fetching artisans:', error);
    }
  };

  // Calculate artisan performance based on their assignments
  const calculateArtisanPerformance = (artisansList) => {
    const performance = {};
    
    artisansList.forEach(artisan => {
      // Calculate performance based on production_number and time since joining
      const daysSinceJoining = Math.floor((new Date() - new Date(artisan.time)) / (1000 * 60 * 60 * 24));
      const experienceFactor = Math.min(1.2, Math.max(0.8, daysSinceJoining / 365)); // Experience bonus
      
      // Base performance on production capacity
      const basePerformance = Math.min(95, Math.max(70, (artisan.production_number / 200) * 100));
      const experienceAdjusted = basePerformance * experienceFactor;
      
      // Add some realistic variation
      const randomVariation = (Math.random() - 0.5) * 15; // ±7.5% variation
      const finalPerformance = Math.round(Math.max(60, Math.min(100, experienceAdjusted + randomVariation)));
      
      // Calculate realistic order count and revenue
      const avgOrdersPerMonth = Math.floor(artisan.production_number / 50) + 2;
      const totalOrders = Math.floor(avgOrdersPerMonth * (daysSinceJoining / 30));
      const avgRevenuePerOrder = 1500 + (artisan.production_number * 10);
      const totalRevenue = totalOrders * avgRevenuePerOrder;
      
      performance[artisan.customer_id] = {
        performance: finalPerformance,
        orders: Math.max(5, totalOrders),
        revenue: Math.max(10000, totalRevenue),
        paymentStatus: Math.random() > 0.25 ? 'Paid' : 'Pending', // 75% paid
        availability: Math.random() > 0.1, // 90% available
        productionNumber: artisan.production_number,
        joinDate: artisan.time
      };
    });
    
    setArtisanPerformance(performance);
  };

<<<<<<< HEAD
  // Add Artisan Form State
  const [showAddForm, setShowAddForm] = useState(false);
  const [newArtisan, setNewArtisan] = useState({
    name: '',
    skill: '',
    experience: '',
    phone: '',
    address: '',
    performance: 0,
    paymentStatus: 'Pending',
    orders: 0,
    revenue: 0
  });

  const handleToggleAvailability = (id) => {
    setArtisanAvailability(prev => ({
=======
  // Remove the old useEffect since we're now using artisanPerformance state

  // Fetch orders and artisans when component mounts
  useEffect(() => {
    fetchTeamLeaderOrders();
    fetchArtisans();
    
    // Debug: Check user info
    const userInfo = userUtils.getUserInfo();
    console.log('Current user info:', userInfo);
    console.log('Token:', localStorage.getItem('token'));
  }, []);

  const handleUpdateProgress = (order) => {
    console.log('Update progress clicked for order:', order);
    setSelectedOrder(order);
    setUpdateForm({
      status: order.status,
      fault_products: 0,
      remarks: ''
    });
    setShowUpdateModal(true);
  };

  const handleAssignArtisans = (order) => {
    console.log('Assign artisans clicked for order:', order);
    setSelectedOrderForAssignment(order);
    setShowArtisanAssignmentModal(true);
  };

  const handleArtisanAssignmentComplete = () => {
    // Refresh orders to get updated artisan assignments
    fetchTeamLeaderOrders();
  };

  // Calculate completion statistics for past orders
  const getCompletionStats = () => {
    const stats = {
      totalAssigned: 0,
      totalCompleted: 0,
      totalArtisans: 0,
      completionRate: 0
    };

    dashboardData.orders.past.forEach(order => {
      if (order.artisan_assignments) {
        order.artisan_assignments.forEach(assignment => {
          if (assignment.team_leader_id === 'TL001') {
            assignment.artisans.forEach(artisan => {
              stats.totalArtisans++;
              stats.totalAssigned += artisan.assigned_quantity;
              stats.totalCompleted += artisan.completed_quantity || 0;
            });
          }
        });
      }
    });

    if (stats.totalAssigned > 0) {
      stats.completionRate = Math.round((stats.totalCompleted / stats.totalAssigned) * 100);
    }

    return stats;
  };

  // Get artisan performance history from past orders
  const getArtisanPerformanceHistory = () => {
    const artisanHistory = {};

    dashboardData.orders.past.forEach(order => {
      if (order.artisan_assignments) {
        order.artisan_assignments.forEach(assignment => {
          if (assignment.team_leader_id === 'TL001') {
            assignment.artisans.forEach(artisan => {
              if (!artisanHistory[artisan.artisan_id]) {
                artisanHistory[artisan.artisan_id] = {
                  name: artisan.artisan_name,
                  totalAssigned: 0,
                  totalCompleted: 0,
                  orders: 0,
                  avgCompletionRate: 0
                };
              }
              
              artisanHistory[artisan.artisan_id].totalAssigned += artisan.assigned_quantity;
              artisanHistory[artisan.artisan_id].totalCompleted += artisan.completed_quantity || 0;
              artisanHistory[artisan.artisan_id].orders += 1;
            });
          }
        });
      }
    });

    // Calculate average completion rate for each artisan
    Object.values(artisanHistory).forEach(artisan => {
      if (artisan.totalAssigned > 0) {
        artisan.avgCompletionRate = Math.round((artisan.totalCompleted / artisan.totalAssigned) * 100);
      }
    });

    return artisanHistory;
  };

  const handleUpdateFormChange = (field, value) => {
    setUpdateForm(prev => ({
>>>>>>> 232ca5c8332f358bdc6a5a69921328c0376a2dc6
      ...prev,
      [field]: value
    }));
  };

  const handleSubmitUpdate = async (e) => {
    e.preventDefault();
    
    if (!updateForm.status) {
      setError('Please select a status');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      console.log('Submitting update for order:', selectedOrder._id);
      console.log('Update form data:', updateForm);
      
      const response = await api.put(`/orders/${selectedOrder._id}/progress`, updateForm);
      console.log('Update response:', response.data);
      
      setSuccess('Order progress updated successfully!');
      setShowUpdateModal(false);
      setSelectedOrder(null);
      setUpdateForm({
        status: '',
        fault_products: 0,
        remarks: ''
      });
      
      // Refresh orders list
      fetchTeamLeaderOrders();
    } catch (error) {
      console.error('Error updating order progress:', error);
      console.error('Error response:', error.response?.data);
      setError(error.response?.data?.msg || 'Failed to update order progress');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAvailability = (artisanId) => {
    setArtisanPerformance(prev => ({
      ...prev,
      [artisanId]: {
        ...prev[artisanId],
        availability: !prev[artisanId]?.availability
      }
    }));
  };

  // Form handling functions
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewArtisan(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddArtisan = (e) => {
    e.preventDefault();

    // Generate new ID
    const newId = Math.max(...dashboardData.artisans.map(a => a.id)) + 1;

    // Create new artisan object
    const artisanToAdd = {
      ...newArtisan,
      id: newId,
      performance: parseInt(newArtisan.performance) || 0,
      orders: parseInt(newArtisan.orders) || 0,
      revenue: parseInt(newArtisan.revenue) || 0
    };

    // Update dashboard data
    setDashboardData(prev => ({
      ...prev,
      artisans: [...prev.artisans, artisanToAdd]
    }));

    // Reset form
    setNewArtisan({
      name: '',
      skill: '',
      experience: '',
      phone: '',
      address: '',
      performance: 0,
      paymentStatus: 'Pending',
      orders: 0,
      revenue: 0
    });

    // Close form
    setShowAddForm(false);
  };

  const handleCancelForm = () => {
    setShowAddForm(false);
    setNewArtisan({
      name: '',
      skill: '',
      experience: '',
      phone: '',
      address: '',
      performance: 0,
      paymentStatus: 'Pending',
      orders: 0,
      revenue: 0
    });
  };

  const getPerformanceColor = (performance) => {
    if (performance >= 90) return '#10b981';
    if (performance >= 80) return '#f59e0b';
    return '#ef4444';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'to be done': return '#6b7280';
      case 'in progress': return '#3b82f6';
      case 'completed': return '#10b981';
      case 'delayed': return '#ef4444';
      case 'Paid': return '#10b981';
      case 'Pending': return '#f59e0b';
      case 'Delivered': return '#10b981';
      default: return '#6b7280';
    }
  };

  return (
    <div className="tl-dashboard">
      <div className="dashboard-header">
        <div className="leader-info">
          <h1>Team Leader Dashboard</h1>
          <div className="leader-details">
            <div className="leader-name">
              <h2>{dashboardData.teamLeader.name}</h2>
              <span className="leader-id">ID: {dashboardData.teamLeader.clusterId}</span>
            </div>
            <div className="leader-meta">
              <div className="meta-item">
                <span className="meta-label">Village:</span>
                <span className="meta-value">{dashboardData.teamLeader.village}</span>
              </div>
              <div className="meta-item">
                <span className="meta-label">Specialization:</span>
                <span className="meta-value">{dashboardData.teamLeader.skill}</span>
              </div>
              <div className="meta-item">
                <span className="meta-label">Experience:</span>
                <span className="meta-value">{dashboardData.teamLeader.experience}</span>
              </div>
            </div>
          </div>
        </div>
        <p className="welcome-message">Welcome back! Here's your cluster overview</p>
      </div>

      <div className="dashboard-tabs">
        <button 
          className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button 
          className={`tab-btn ${activeTab === 'artisans' ? 'active' : ''}`}
          onClick={() => setActiveTab('artisans')}
        >
          Artisans
        </button>
        <button 
          className={`tab-btn ${activeTab === 'orders' ? 'active' : ''}`}
          onClick={() => setActiveTab('orders')}
        >
          Orders
        </button>
        <button
          className={`tab-btn ${activeTab === 'analytics' ? 'active' : ''}`}
          onClick={() => setActiveTab('analytics')}
        >
          Analytics
        </button>
        <button
          className={`tab-btn ${activeTab === 'assignments' ? 'active' : ''}`}
          onClick={() => setActiveTab('assignments')}
        >
          Product Assignment
        </button>
        <button
          className={`tab-btn ${activeTab === 'agreements' ? 'active' : ''}`}
          onClick={() => setActiveTab('agreements')}
        >
          Agreements
        </button>
      </div>

      {activeTab === 'overview' && (
        <div className="dashboard-content">
          {/* Personal Income Section */}
          <div className="income-section">
            <h2>Personal Income (10% Share)</h2>
            <div className="income-cards">
              <div className="income-card primary">
                <h3>Monthly Income</h3>
                <p className="amount">₹{dashboardData.personalIncome.monthly.toLocaleString()}</p>
                <span className="subtitle">Current Month</span>
              </div>
              <div className="income-card">
                <h3>Cluster Revenue</h3>
                <p className="amount">₹{dashboardData.personalIncome.clusterRevenue.toLocaleString()}</p>
                <span className="subtitle">Total Revenue</span>
              </div>
              <div className="income-card">
                <h3>Your Share</h3>
                <p className="amount">{dashboardData.personalIncome.sharePercentage}%</p>
                <span className="subtitle">Commission Rate</span>
              </div>
            </div>
          </div>

          {/* Monthly Breakdown */}
          <div className="breakdown-section">
            <h2>Monthly Income Breakdown</h2>
            <div className="breakdown-chart">
              {dashboardData.monthlyBreakdown.map((month, index) => (
                <div key={index} className="month-bar">
                  <div 
                    className="bar" 
                    style={{ height: `${(month.income / 25000) * 100}%` }}
                  ></div>
                  <span className="month-label">{month.month.slice(0, 3)}</span>
                  <span className="income-label">₹{month.income.toLocaleString()}</span>
                  <span className="orders-label">{month.orders} orders</span>
                </div>
              ))}
            </div>
          </div>

          {/* Delivery Stats */}
          <div className="delivery-section">
            <h2>Delivery Performance</h2>
            <div className="delivery-stats">
              <div className="stat-card success">
                <h3>Delivered</h3>
                <p>{dashboardData.deliveryStats.delivered}</p>
              </div>
              <div className="stat-card warning">
                <h3>Loss/Damage</h3>
                <p>{dashboardData.deliveryStats.loss}</p>
              </div>
              <div className="stat-card info">
                <h3>Total Produced</h3>
                <p>{dashboardData.deliveryStats.totalProduced}</p>
              </div>
              <div className="stat-card primary">
                <h3>Success Rate</h3>
                <p>{dashboardData.deliveryStats.deliveryRate}%</p>
              </div>
            </div>
          </div>

          {/* Artisan Summary */}
          <div className="artisan-summary-section">
            <h2>Artisan Summary</h2>
            <div className="delivery-stats">
              <div className="stat-card success">
                <h3>Total Artisans</h3>
                <p>{artisans.length}</p>
              </div>
              <div className="stat-card warning">
                <h3>Available</h3>
                <p>{artisans.filter(a => artisanPerformance[a.customer_id]?.availability).length}</p>
              </div>
              <div className="stat-card info">
                <h3>Avg Performance</h3>
                <p>{artisans.length > 0 ? 
                  Math.round(artisans.reduce((sum, a) => sum + (artisanPerformance[a.customer_id]?.performance || 0), 0) / artisans.length) : 0}%</p>
              </div>
              <div className="stat-card primary">
                <h3>Total Revenue</h3>
                <p>₹{artisans.reduce((sum, a) => sum + (artisanPerformance[a.customer_id]?.revenue || 0), 0).toLocaleString()}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'artisans' && (
        <div className="dashboard-content">
          <div className="artisans-section">
            <div className="artisans-header">
              <h2>Artisan Performance</h2>
              <button
                className="add-artisan-btn"
                onClick={() => setShowAddForm(true)}
              >
                + Add New Artisan
              </button>
            </div>

            {showAddForm && (
              <div className="add-artisan-form">
                <h3>Add New Artisan</h3>
                <form onSubmit={handleAddArtisan}>
                  <div className="form-grid">
                    <div className="form-group">
                      <label>Name *</label>
                      <input
                        type="text"
                        name="name"
                        value={newArtisan.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter artisan's full name"
                      />
                    </div>

                    <div className="form-group">
                      <label>Skill/Craft *</label>
                      <input
                        type="text"
                        name="skill"
                        value={newArtisan.skill}
                        onChange={handleInputChange}
                        required
                        placeholder="e.g., Handloom, Pottery, Woodwork"
                      />
                    </div>

                    <div className="form-group">
                      <label>Experience</label>
                      <input
                        type="text"
                        name="experience"
                        value={newArtisan.experience}
                        onChange={handleInputChange}
                        placeholder="e.g., 5 years"
                      />
                    </div>

                    <div className="form-group">
                      <label>Phone Number</label>
                      <input
                        type="tel"
                        name="phone"
                        value={newArtisan.phone}
                        onChange={handleInputChange}
                        placeholder="Enter phone number"
                      />
                    </div>

                    <div className="form-group full-width">
                      <label>Address</label>
                      <textarea
                        name="address"
                        value={newArtisan.address}
                        onChange={handleInputChange}
                        placeholder="Enter full address"
                        rows="2"
                      ></textarea>
                    </div>

                    <div className="form-group">
                      <label>Initial Performance (%)</label>
                      <input
                        type="number"
                        name="performance"
                        value={newArtisan.performance}
                        onChange={handleInputChange}
                        min="0"
                        max="100"
                        placeholder="0-100"
                      />
                    </div>

                    <div className="form-group">
                      <label>Payment Status</label>
                      <select
                        name="paymentStatus"
                        value={newArtisan.paymentStatus}
                        onChange={handleInputChange}
                      >
                        <option value="Pending">Pending</option>
                        <option value="Paid">Paid</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-actions">
                    <button type="button" className="cancel-btn" onClick={handleCancelForm}>
                      Cancel
                    </button>
                    <button type="submit" className="submit-btn">
                      Add Artisan
                    </button>
                  </div>
                </form>
              </div>
            )}

            <div className="artisans-table">
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Performance</th>
                    <th>Payment Status</th>
                    <th>Orders</th>
                    <th>Revenue</th>
                    <th>Availability</th>
                  </tr>
                </thead>
                <tbody>
                  {artisans.map(artisan => {
                    const performance = artisanPerformance[artisan.customer_id] || {
                      performance: 85,
                      orders: 20,
                      revenue: 35000,
                      paymentStatus: 'Paid',
                      availability: true
                    };
                    
                    return (
                      <tr key={artisan.customer_id}>
                        <td>{artisan.name}</td>
                        <td>
                          <div className="performance-cell">
                            <span 
                              className="performance-score"
                              style={{ color: getPerformanceColor(performance.performance) }}
                            >
                              {performance.performance}%
                            </span>
                            <div className="performance-bar">
                              <div 
                                className="performance-fill"
                                style={{ 
                                  width: `${performance.performance}%`,
                                  backgroundColor: getPerformanceColor(performance.performance)
                                }}
                              ></div>
                            </div>
                          </div>
                        </td>
                        <td>
                          <span 
                            className="status-badge"
                            style={{ backgroundColor: getStatusColor(performance.paymentStatus) }}
                          >
                            {performance.paymentStatus}
                          </span>
                        </td>
                        <td>{performance.orders}</td>
                        <td>₹{performance.revenue.toLocaleString()}</td>
                        <td>
                          <label className="switch">
                            <input
                              type="checkbox"
                              checked={performance.availability}
                              onChange={() => handleToggleAvailability(artisan.customer_id)}
                            />
                            <span
                              className={`slider round ${performance.availability ? 'success' : 'danger'}`}
                              title={performance.availability ? 'Available' : 'Unavailable'}
                            ></span>
                          </label>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'orders' && (
        <div className="dashboard-content">
          <div className="orders-section">
            {error && (
              <div className="error-message">
                {error}
                <button onClick={() => setError('')}>×</button>
              </div>
            )}

            {success && (
              <div className="success-message">
                {success}
                <button onClick={() => setSuccess('')}>×</button>
              </div>
            )}

            <h2>Current Orders ({dashboardData.orders.current.length})</h2>
            {loading ? (
              <div className="loading">Loading orders...</div>
            ) : (
              <div className="orders-grid">
                {dashboardData.orders.current.length > 0 ? (
                  dashboardData.orders.current.map(order => (
                    <div key={order._id} className="order-card">
                      <div className="order-header">
                        <h3>{order.order_id}</h3>
                        <span 
                          className="order-status"
                          style={{ backgroundColor: getStatusColor(order.status) }}
                        >
                          {order.status}
                        </span>
                      </div>
                      <p className="order-product">Product: {order.product_id}</p>
                      <p className="order-quantity">Total Quantity: {order.total_qty}</p>
                      <p className="order-deadline">Deadline: {new Date(order.deadline).toLocaleDateString()}</p>
                      <p className="order-date">Ordered: {new Date(order.date_ordered).toLocaleDateString()}</p>
                      {order.remark && <p className="order-remark">Remark: {order.remark}</p>}
                      
                      <div className="quantity-breakdown">
                        <h4>Your Assignment:</h4>
                        {order.team_leads.map((teamLead, index) => {
                          { // Show only current team leader's assignment
                            return (
                              <div key={index} className="assignment-item">
                                <span>Your Quantity:</span>
                                <span>{order.quantities[index]} units</span>
                              </div>
                            );
                          }
                          return null;
                        })}
                      </div>

                      {/* Show artisan assignments if they exist */}
                      {order.artisan_assignments && order.artisan_assignments.length > 0 && (
                        <div className="artisan-assignments">
                          <h4>Artisan Assignments:</h4>
                          {order.artisan_assignments.map((assignment, index) => {
                            {
                              return (
                                <div key={index} className="artisan-assignment-list">
                                  {assignment.artisans.map((artisan, artisanIndex) => (
                                    <div key={artisanIndex} className="artisan-assignment-item">
                                      <span>{artisan.artisan_name}:</span>
                                      <span>{artisan.assigned_quantity} units</span>
                                      <span className={`status-badge ${artisan.status}`}>
                                        {artisan.status}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              );
                            }
                            return null;
                          })}
                        </div>
                      )}

                      <div className="order-actions">
                        <button 
                          className="update-progress-btn"
                          onClick={() => handleUpdateProgress(order)}
                        >
                          Update Progress
                        </button>
                        <button 
                          className="assign-artisans-btn"
                          onClick={() => handleAssignArtisans(order)}
                        >
                          Assign Artisans
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-orders">No current orders assigned</div>
                )}
              </div>
            )}

            <h2>Past Orders ({dashboardData.orders.past.length})</h2>
            
            {/* Completion Summary */}
            {dashboardData.orders.past.length > 0 && (
              <div className="completion-summary">
                <h3>Work Completion Summary</h3>
                <div className="completion-stats">
                  {(() => {
                    const stats = getCompletionStats();
                    return (
                      <>
                        <div className="completion-stat">
                          <span className="stat-label">Total Artisans Worked:</span>
                          <span className="stat-value">{stats.totalArtisans}</span>
                        </div>
                        <div className="completion-stat">
                          <span className="stat-label">Total Assigned:</span>
                          <span className="stat-value">{stats.totalAssigned} units</span>
                        </div>
                        <div className="completion-stat">
                          <span className="stat-label">Total Completed:</span>
                          <span className="stat-value">{stats.totalCompleted} units</span>
                        </div>
                        <div className="completion-stat">
                          <span className="stat-label">Completion Rate:</span>
                          <span className="stat-value">{stats.completionRate}%</span>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}

            {/* Artisan Performance History Pie Chart */}
            {dashboardData.orders.past.length > 0 && (() => {
              const artisanHistory = getArtisanPerformanceHistory();
              const pieData = Object.values(artisanHistory)
                .filter(a => a.totalCompleted > 0)
                .map(a => ({
                  name: a.name,
                  value: a.totalCompleted
                }));

              if (pieData.length > 0) {
                return (
                  <div className="artisan-performance-history">
                    <h3>Artisan Performance (Pie Chart)</h3>
                    <ArtisanPieChart data={pieData} />
                  </div>
                );
              }
              return null;
            })()}
            <div className="orders-grid">
              {dashboardData.orders.past.length > 0 ? (
                dashboardData.orders.past.map(order => (
                  <div key={order._id} className="order-card completed">
                    <div className="order-header">
                      <h3>{order.order_id}</h3>
                      <span 
                        className="order-status"
                        style={{ backgroundColor: getStatusColor(order.status) }}
                      >
                        {order.status}
                      </span>
                    </div>
                    <p className="order-product">Product: {order.product_id}</p>
                    <p className="order-quantity">Total Quantity: {order.total_qty}</p>
                    <p className="order-completed">Completed: {new Date(order.updatedAt).toLocaleDateString()}</p>
                    
                    <div className="quantity-breakdown">
                      <h4>Your Assignment:</h4>
                      {order.team_leads.map((teamLead, index) => {
                        if (teamLead === 'TL001') { // Show only current team leader's assignment
                          return (
                            <div key={index} className="assignment-item">
                              <span>Your Quantity:</span>
                              <span>{order.quantities[index]} units</span>
                            </div>
                          );
                        }
                        return null;
                      })}
                    </div>

                    {/* Show artisan assignments if they exist */}
                    {order.artisan_assignments && order.artisan_assignments.length > 0 && (
                      <div className="artisan-assignments">
                        <h4>Artisan Assignments:</h4>
                        {order.artisan_assignments.map((assignment, index) => {
                          if (assignment.team_leader_id === 'TL001') {
                            return (
                              <div key={index} className="artisan-assignment-list">
                                {assignment.artisans.map((artisan, artisanIndex) => (
                                  <div key={artisanIndex} className="artisan-assignment-item">
                                    <span>{artisan.artisan_name}:</span>
                                    <span>{artisan.assigned_quantity} units</span>
                                    <span className={`status-badge ${artisan.status}`}>
                                      {artisan.status}
                                    </span>
                                    {artisan.completed_quantity > 0 && (
                                      <span className="completed-info">
                                        Completed: {artisan.completed_quantity}
                                      </span>
                                    )}
                                  </div>
                                ))}
                              </div>
                            );
                          }
                          return null;
                        })}
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="no-orders">No past orders</div>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="dashboard-content">
          <div className="analytics-section">
            <h2>Performance Analytics</h2>
            <div className="analytics-grid">
              <div className="analytics-card">
                <h3>Average Artisan Performance</h3>
                <p className="analytics-value">90%</p>
                <span className="analytics-trend positive">+5% from last month</span>
              </div>
              <div className="analytics-card">
                <h3>Order Completion Rate</h3>
                <p className="analytics-value">94.97%</p>
                <span className="analytics-trend positive">+2.3% from last month</span>
              </div>
              <div className="analytics-card">
                <h3>Revenue Growth</h3>
                <p className="analytics-value">+15%</p>
                <span className="analytics-trend positive">Monthly growth</span>
              </div>
              <div className="analytics-card">
                <h3>Active Artisans</h3>
                <p className="analytics-value">5</p>
                <span className="analytics-trend neutral">No change</span>
              </div>
            </div>
          </div>
        </div>
      )}

<<<<<<< HEAD
      {activeTab === 'assignments' && (
        <ArtisanAssignment />
      )}

      {activeTab === 'agreements' && (
        <AgreementPDF />
=======
      {/* Update Progress Modal */}
      {showUpdateModal && selectedOrder && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Update Order Progress</h2>
              <button 
                className="close-btn"
                onClick={() => setShowUpdateModal(false)}
              >
                ×
              </button>
            </div>

            <form onSubmit={handleSubmitUpdate} className="update-form">
              <div className="form-group">
                <label>Order ID</label>
                <input
                  type="text"
                  value={selectedOrder.order_id}
                  disabled
                  className="disabled-input"
                />
              </div>

              <div className="form-group">
                <label>Current Status</label>
                <select
                  value={updateForm.status}
                  onChange={(e) => handleUpdateFormChange('status', e.target.value)}
                  required
                >
                  <option value="">Select Status</option>
                  <option value="to be done">To Be Done</option>
                  <option value="in progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="delayed">Delayed</option>
                </select>
              </div>

              <div className="form-group">
                <label>Fault Products</label>
                <input
                  type="number"
                  value={updateForm.fault_products}
                  onChange={(e) => handleUpdateFormChange('fault_products', parseInt(e.target.value) || 0)}
                  min="0"
                  placeholder="Number of faulty products"
                />
              </div>

              <div className="form-group">
                <label>Remarks</label>
                <textarea
                  value={updateForm.remarks}
                  onChange={(e) => handleUpdateFormChange('remarks', e.target.value)}
                  placeholder="Add any remarks about the progress..."
                  rows="3"
                />
              </div>

              <div className="form-actions">
                <button 
                  type="button" 
                  className="cancel-btn"
                  onClick={() => setShowUpdateModal(false)}
                >
                  Cancel
                </button>
                <button 
                  type="submit" 
                  className="submit-btn"
                  disabled={loading}
                >
                  {loading ? 'Updating...' : 'Update Progress'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Artisan Assignment Modal */}
      {showArtisanAssignmentModal && selectedOrderForAssignment && (
        <ArtisanAssignmentModal
          isOpen={showArtisanAssignmentModal}
          onClose={() => {
            setShowArtisanAssignmentModal(false);
            setSelectedOrderForAssignment(null);
          }}
          order={selectedOrderForAssignment}
          teamLeaderId={userUtils.getUserInfo().username}
          onAssignmentComplete={handleArtisanAssignmentComplete}
        />
>>>>>>> 232ca5c8332f358bdc6a5a69921328c0376a2dc6
      )}
    </div>
  );
}

export default TLDashboard;
