# Team Leader Authentication & ID Mapping Guide

## 🔐 How Authentication Works

### 1. Login Flow
```
User Login → Backend Verification → JWT Token + user_id → Frontend Dashboard
```

### 2. Current Authentication Response
When you login as a Team Leader, the backend returns:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user_id": "teamlead-tl001",
  "type": "team leader",
  "verified": true
}
```

---

## 📋 Team Leader Credentials & IDs

### Primary Team Leader (For Dashboard Testing)
- **Username:** `<EMAIL>`
- **Password:** `password123`
- **User ID:** `teamlead-tl001`
- **MongoDB _id:** `507f1f77bcf86cd799439013`
- **Dashboard Cluster ID:** `TL-001`

### Additional Team Leaders
- **<PERSON><PERSON> Patel:** `teamlead-tl002`
- **Me<PERSON> Singh:** `teamlead-tl003`

---

## 🔧 Setting Up Proper ID Mapping

### Step 1: Create MongoDB Documents

Add these documents to your `users` collection in MongoDB Compass:

```json
{
  "_id": ObjectId("507f1f77bcf86cd799439013"),
  "username": "<EMAIL>",
  "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
  "email": "<EMAIL>",
  "phone_number": "+91-9876543212",
  "type": "team leader",
  "user_id": "teamlead-tl001",
  "verified": true
}
```

### Step 2: Update TLDashboard Component

Modify the TLDashboard to use the authenticated user's ID:

```javascript
// Add this to TLDashboard.js
useEffect(() => {
  // Get team leader ID from localStorage (set during login)
  const teamLeaderId = localStorage.getItem('user_id'); // "teamlead-tl001"
  const token = localStorage.getItem('token');
  
  if (teamLeaderId && token) {
    // Update dashboard data with actual team leader info
    setDashboardData(prev => ({
      ...prev,
      teamLeader: {
        ...prev.teamLeader,
        clusterId: teamLeaderId
      }
    }));
  }
}, []);
```

---

## 🚀 Complete Authentication Setup

### Step 1: Login API Call
```javascript
// Frontend login function
const handleLogin = async (username, password) => {
  try {
    const response = await fetch('http://localhost:5000/api/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      // Store authentication data
      localStorage.setItem('token', data.token);
      localStorage.setItem('user_id', data.user_id);
      localStorage.setItem('user_type', data.type);
      
      // Redirect to dashboard
      if (data.type === 'team leader') {
        window.location.href = '/dashboard';
      }
    }
  } catch (error) {
    console.error('Login failed:', error);
  }
};
```

### Step 2: Protected Dashboard Route
```javascript
// Add authentication check to TLDashboard
function TLDashboard() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [teamLeaderId, setTeamLeaderId] = useState(null);
  
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('user_id');
    const userType = localStorage.getItem('user_type');
    
    if (token && userId && userType === 'team leader') {
      setIsAuthenticated(true);
      setTeamLeaderId(userId);
    } else {
      // Redirect to login
      window.location.href = '/login';
    }
  }, []);
  
  if (!isAuthenticated) {
    return <div>Loading...</div>;
  }
  
  // Rest of your dashboard component...
}
```

---

## 🗄️ Database Setup for Testing

### MongoDB Collection: `users`

```javascript
// Insert this document in MongoDB Compass
{
  "_id": ObjectId("507f1f77bcf86cd799439013"),
  "username": "<EMAIL>",
  "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
  "email": "<EMAIL>",
  "phone_number": "+91-9876543212",
  "type": "team leader",
  "user_id": "teamlead-tl001",
  "verified": true,
  "__v": 0
}
```

### Additional Collections for Dashboard Data

#### Collection: `team_leaders`
```javascript
{
  "_id": ObjectId("507f1f77bcf86cd799439023"),
  "user_id": "teamlead-tl001",
  "name": "Rajesh Kumar Sharma",
  "village": "Kumargram Village",
  "skill": "Handloom & Textile Crafts",
  "experience": "8 years",
  "cluster_id": "TL-001",
  "created_at": new Date()
}
```

#### Collection: `artisans`
```javascript
{
  "_id": ObjectId("507f1f77bcf86cd799439024"),
  "team_leader_id": "teamlead-tl001",
  "name": "Priya Sharma",
  "skill": "Handloom & Textile Crafts",
  "performance": 92,
  "payment_status": "Paid",
  "orders": 25,
  "revenue": 45000,
  "available": true,
  "phone": "+91-9876543215",
  "created_at": new Date()
}
```

---

## 🧪 Testing the Complete Flow

### Step 1: Start Backend
```bash
cd backend
npm start
```

### Step 2: Test Login API
```bash
curl -X POST http://localhost:5000/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123"
  }'
```

**Expected Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user_id": "teamlead-tl001",
  "type": "team leader",
  "verified": true
}
```

### Step 3: Access Dashboard
1. Login with credentials
2. Token and user_id stored in localStorage
3. Dashboard loads with team leader data
4. All API calls use the team leader's ID

---

## 🔗 API Endpoints for Dashboard Data

### Get Team Leader Profile
```
GET /api/team-leader/profile/teamlead-tl001
Authorization: Bearer <token>
```

### Get Artisans
```
GET /api/team-leader/teamlead-tl001/artisans
Authorization: Bearer <token>
```

### Get Orders
```
GET /api/team-leader/teamlead-tl001/orders
Authorization: Bearer <token>
```

---

## 📝 Quick Setup Checklist

- [ ] Add team leader user to MongoDB `users` collection
- [ ] Verify password hash matches `password123`
- [ ] Set `verified: true` and `type: "team leader"`
- [ ] Use `user_id: "teamlead-tl001"`
- [ ] Test login API endpoint
- [ ] Verify token generation
- [ ] Update frontend to use authenticated user ID
- [ ] Test dashboard access with proper authentication

---

## 🎯 Key Points

1. **User ID Format:** `teamlead-tl001` (from database)
2. **Dashboard Cluster ID:** `TL-001` (for display)
3. **Authentication:** JWT token + user_id
4. **Password:** `password123` (hashed in database)
5. **Database:** MongoDB with `users` collection

This setup ensures proper authentication flow and ID mapping for the Team Leader Dashboard!
