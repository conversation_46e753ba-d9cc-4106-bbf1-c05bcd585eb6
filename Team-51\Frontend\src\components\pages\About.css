/* About Section Styles */
.about {
  padding: 80px 0;
  background: var(--light-blue-bg);
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.about-header {
  text-align: center;
  margin-bottom: 60px;
}

.about-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-blue);
  margin-bottom: 16px;
  position: relative;
}

.about-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-orange), var(--accent-orange-light));
  border-radius: 2px;
}

.about-subtitle {
  font-size: 1.2rem;
  color: var(--gray-700);
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  margin-bottom: 80px;
}

.about-text {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.about-story h3,
.about-mission h3,
.about-values h3 {
  font-size: 1.6rem;
  font-weight: 600;
  color: var(--light-green);
  margin-bottom: 16px;
  word-wrap: break-word;
}

.about-story p,
.about-mission p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--gray-600);
  margin: 0;
  word-wrap: break-word;
  hyphens: auto;
}

.about-values-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.about-values-list li {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--gray-600);
  margin-bottom: 12px;
  padding-left: 20px;
  position: relative;
}

.about-values-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--accent-orange);
  font-weight: bold;
}

.about-values-list li strong {
  color: var(--primary-blue);
}

.about-image {
  position: relative;
}

.about-img-link {
  display: block;
  position: relative;
  transition: transform 0.3s ease;
}

.about-img-link:hover {
  transform: scale(1.02);
}

.about-img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
  transition: filter 0.3s ease;
}

.about-img-link:hover .about-img {
  filter: brightness(1.1);
}

.about-impact {
  text-align: center;
}

.about-impact h3 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c5530;
  margin-bottom: 50px;
}

.impact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.impact-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  word-wrap: break-word;
  hyphens: auto;
}

.impact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.impact-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
}

.impact-card h4 {
  font-size: 1.6rem;
  font-weight: 600;
  color: #2c5530;
  margin-bottom: 15px;
  word-wrap: break-word;
}

.impact-card p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0;
  word-wrap: break-word;
  hyphens: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .about {
    padding: 60px 0;
  }

  .about-container {
    padding: 0 15px;
  }

  .about-header {
    margin-bottom: 40px;
  }

  .about-title {
    font-size: 2.5rem;
  }

  .about-subtitle {
    font-size: 1.1rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 60px;
  }

  .about-text {
    gap: 30px;
  }

  .about-story h3,
  .about-mission h3,
  .about-values h3 {
    font-size: 1.3rem;
  }

  .about-story p,
  .about-mission p {
    font-size: 1rem;
  }

  .about-values-list li {
    font-size: 1rem;
  }

  .about-img {
    height: 300px;
  }

  .about-impact h3 {
    font-size: 2rem;
    margin-bottom: 40px;
  }

  .impact-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .impact-card {
    padding: 30px 25px;
  }

  .impact-icon {
    font-size: 2.5rem;
  }

  .impact-card h4 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .about-title {
    font-size: 2rem;
  }

  .about-subtitle {
    font-size: 1rem;
  }

  .impact-card {
    padding: 25px 20px;
  }

  .impact-icon {
    font-size: 2rem;
  }
}
