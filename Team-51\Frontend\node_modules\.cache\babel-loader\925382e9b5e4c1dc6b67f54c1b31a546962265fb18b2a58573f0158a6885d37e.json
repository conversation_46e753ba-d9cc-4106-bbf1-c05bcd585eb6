{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\components\\\\dashboard\\\\AgreementPDF.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport jsPDF from 'jspdf';\nimport './AgreementPDF.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AgreementPDF() {\n  _s();\n  const [agreementData, setAgreementData] = useState({\n    teamLeader: {\n      name: '<PERSON><PERSON>',\n      village: 'Kumargram Village',\n      clusterId: 'TL-001',\n      phone: '+91-9876543212',\n      email: '<EMAIL>'\n    },\n    artisan: {\n      name: '',\n      phone: '',\n      address: '',\n      experience: ''\n    },\n    order: {\n      orderId: '',\n      product: 'Handwoven Sarees',\n      quantity: '',\n      pricePerUnit: '',\n      totalAmount: '',\n      deadline: '',\n      qualityStandards: t('qualityStandards')\n    },\n    terms: {\n      advancePayment: '',\n      finalPayment: '',\n      deliveryTerms: t('deliveryTerms'),\n      qualityCheck: t('qualityCheck'),\n      penaltyClause: t('penaltyClause')\n    }\n  });\n  const [showForm, setShowForm] = useState(false);\n  const handleInputChange = (section, field, value) => {\n    setAgreementData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n  const calculateTotalAmount = () => {\n    const quantity = parseFloat(agreementData.order.quantity) || 0;\n    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;\n    const total = quantity * pricePerUnit;\n    setAgreementData(prev => ({\n      ...prev,\n      order: {\n        ...prev.order,\n        totalAmount: total.toFixed(2)\n      }\n    }));\n  };\n  const generatePDF = () => {\n    const doc = new jsPDF();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    let yPosition = 30;\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('pdfTitle'), pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(t('pdfSubtitle'), pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.text(`${t('agreementDate')}: ${new Date().toLocaleDateString()}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('teamLeaderDetails'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t('name')}: ${agreementData.teamLeader.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('village')}: ${agreementData.teamLeader.village}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('clusterId')}: ${agreementData.teamLeader.clusterId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('phone')}: ${agreementData.teamLeader.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('email')}: ${agreementData.teamLeader.email}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('artisanDetails'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t('name')}: ${agreementData.artisan.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('phone')}: ${agreementData.artisan.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('address')}: ${agreementData.artisan.address}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('experience')}: ${agreementData.artisan.experience}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('orderDetails'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t('orderId')}: ${agreementData.order.orderId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('product')}: ${agreementData.order.product}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('quantity')}: ${agreementData.order.quantity}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('pricePerUnit')}: ₹${agreementData.order.pricePerUnit}`, margin, yPosition);\n    yPosition += 6;\n    doc.setFont('helvetica', 'bold');\n    doc.text(`${t('totalAmount')}: ₹${agreementData.order.totalAmount}`, margin, yPosition);\n    doc.setFont('helvetica', 'normal');\n    yPosition += 6;\n    doc.text(`${t('deadline')}: ${agreementData.order.deadline}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('qualityStandards')}: ${agreementData.order.qualityStandards}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('paymentTerms'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const advance = parseFloat(agreementData.terms.advancePayment);\n    const total = parseFloat(agreementData.order.totalAmount);\n    const percent = total ? (advance / total * 100).toFixed(1) : '0';\n    doc.text(`${t('advancePayment')}: ₹${agreementData.terms.advancePayment} (${percent}%)`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('finalPayment')}: ₹${agreementData.terms.finalPayment}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('termsAndConditions'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const terms = [`1. ${t('deliveryTerms')}: ${agreementData.terms.deliveryTerms}`, `2. ${t('qualityCheck')}: ${agreementData.terms.qualityCheck}`, `3. ${t('penaltyClause')}: ${agreementData.terms.penaltyClause}`, `4. ${t('term4')}`, `5. ${t('term5')}`, `6. ${t('term6')}`, `7. ${t('term7')}`];\n    terms.forEach(term => {\n      doc.text(term, margin, yPosition, {\n        maxWidth: pageWidth - 2 * margin\n      });\n      yPosition += 8;\n    });\n    yPosition += 20;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('signatures'), margin, yPosition);\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t('teamLeader')}:`, margin, yPosition);\n    doc.text(`${t('artisan')}:`, pageWidth - margin - 60, yPosition);\n    yPosition += 15;\n    doc.text('_____________________', margin, yPosition);\n    doc.text('_____________________', pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);\n    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${t('date')}: _______________`, margin, yPosition);\n    doc.text(`${t('date')}: _______________`, pageWidth - margin - 60, yPosition);\n    yPosition += 20;\n    doc.setFontSize(8);\n    doc.setFont('helvetica', 'italic');\n    doc.text(t('footerNote'), pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 5\n  }, this);\n}\n_s(AgreementPDF, \"8/j/NmV3SUgam2xzzTBML+XnS6o=\");\n_c = AgreementPDF;\nexport default AgreementPDF;\nvar _c;\n$RefreshReg$(_c, \"AgreementPDF\");", "map": {"version": 3, "names": ["React", "useState", "jsPDF", "jsxDEV", "_jsxDEV", "AgreementPDF", "_s", "agreementData", "setAgreementData", "<PERSON><PERSON><PERSON><PERSON>", "name", "village", "clusterId", "phone", "email", "artisan", "address", "experience", "order", "orderId", "product", "quantity", "pricePerUnit", "totalAmount", "deadline", "qualityStandards", "t", "terms", "advancePayment", "finalPayment", "deliveryTerms", "qualityCheck", "penalty<PERSON><PERSON><PERSON>", "showForm", "setShowForm", "handleInputChange", "section", "field", "value", "prev", "calculateTotalAmount", "parseFloat", "total", "toFixed", "generatePDF", "doc", "pageWidth", "internal", "pageSize", "width", "margin", "yPosition", "setFontSize", "setFont", "text", "align", "Date", "toLocaleDateString", "advance", "percent", "for<PERSON>ach", "term", "max<PERSON><PERSON><PERSON>", "fileName", "replace", "toISOString", "split", "save", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/components/dashboard/AgreementPDF.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport jsPDF from 'jspdf';\nimport './AgreementPDF.css';\n\nfunction AgreementPDF() {\n\n  const [agreementData, setAgreementData] = useState({\n    teamLeader: {\n      name: '<PERSON><PERSON>',\n      village: 'Kumargram Village',\n      clusterId: 'TL-001',\n      phone: '+91-9876543212',\n      email: '<EMAIL>'\n    },\n    artisan: {\n      name: '',\n      phone: '',\n      address: '',\n      experience: ''\n    },\n    order: {\n      orderId: '',\n      product: 'Handwoven Sarees',\n      quantity: '',\n      pricePerUnit: '',\n      totalAmount: '',\n      deadline: '',\n      qualityStandards: t('qualityStandards')\n    },\n    terms: {\n      advancePayment: '',\n      finalPayment: '',\n      deliveryTerms: t('deliveryTerms'),\n      qualityCheck: t('qualityCheck'),\n      penaltyClause: t('penaltyClause')\n    }\n  });\n\n  const [showForm, setShowForm] = useState(false);\n\n  const handleInputChange = (section, field, value) => {\n    setAgreementData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n\n  const calculateTotalAmount = () => {\n    const quantity = parseFloat(agreementData.order.quantity) || 0;\n    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;\n    const total = quantity * pricePerUnit;\n    setAgreementData(prev => ({\n      ...prev,\n      order: {\n        ...prev.order,\n        totalAmount: total.toFixed(2)\n      }\n    }));\n  };\n\n  const generatePDF = () => {\n    const doc = new jsPDF();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    let yPosition = 30;\n\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('pdfTitle'), pageWidth / 2, yPosition, { align: 'center' });\n\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(t('pdfSubtitle'), pageWidth / 2, yPosition, { align: 'center' });\n\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.text(`${t('agreementDate')}: ${new Date().toLocaleDateString()}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('teamLeaderDetails'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t('name')}: ${agreementData.teamLeader.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('village')}: ${agreementData.teamLeader.village}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('clusterId')}: ${agreementData.teamLeader.clusterId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('phone')}: ${agreementData.teamLeader.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('email')}: ${agreementData.teamLeader.email}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('artisanDetails'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t('name')}: ${agreementData.artisan.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('phone')}: ${agreementData.artisan.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('address')}: ${agreementData.artisan.address}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('experience')}: ${agreementData.artisan.experience}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('orderDetails'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t('orderId')}: ${agreementData.order.orderId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('product')}: ${agreementData.order.product}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('quantity')}: ${agreementData.order.quantity}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('pricePerUnit')}: ₹${agreementData.order.pricePerUnit}`, margin, yPosition);\n    yPosition += 6;\n    doc.setFont('helvetica', 'bold');\n    doc.text(`${t('totalAmount')}: ₹${agreementData.order.totalAmount}`, margin, yPosition);\n    doc.setFont('helvetica', 'normal');\n    yPosition += 6;\n    doc.text(`${t('deadline')}: ${agreementData.order.deadline}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('qualityStandards')}: ${agreementData.order.qualityStandards}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('paymentTerms'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const advance = parseFloat(agreementData.terms.advancePayment);\n    const total = parseFloat(agreementData.order.totalAmount);\n    const percent = total ? ((advance / total) * 100).toFixed(1) : '0';\n    doc.text(`${t('advancePayment')}: ₹${agreementData.terms.advancePayment} (${percent}%)`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t('finalPayment')}: ₹${agreementData.terms.finalPayment}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('termsAndConditions'), margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const terms = [\n      `1. ${t('deliveryTerms')}: ${agreementData.terms.deliveryTerms}`,\n      `2. ${t('qualityCheck')}: ${agreementData.terms.qualityCheck}`,\n      `3. ${t('penaltyClause')}: ${agreementData.terms.penaltyClause}`,\n      `4. ${t('term4')}`,\n      `5. ${t('term5')}`,\n      `6. ${t('term6')}`,\n      `7. ${t('term7')}`\n    ];\n    terms.forEach(term => {\n      doc.text(term, margin, yPosition, { maxWidth: pageWidth - 2 * margin });\n      yPosition += 8;\n    });\n    yPosition += 20;\n\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t('signatures'), margin, yPosition);\n    yPosition += 20;\n\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t('teamLeader')}:`, margin, yPosition);\n    doc.text(`${t('artisan')}:`, pageWidth - margin - 60, yPosition);\n    yPosition += 15;\n\n    doc.text('_____________________', margin, yPosition);\n    doc.text('_____________________', pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);\n    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${t('date')}: _______________`, margin, yPosition);\n    doc.text(`${t('date')}: _______________`, pageWidth - margin - 60, yPosition);\n\n    yPosition += 20;\n    doc.setFontSize(8);\n    doc.setFont('helvetica', 'italic');\n    doc.text(t('footerNote'), pageWidth / 2, yPosition, { align: 'center' });\n\n    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  };\n\n  return (\n    <div>{/* UI rendering omitted for brevity */}</div>\n  );\n}\n\nexport default AgreementPDF;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EAEtB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGP,QAAQ,CAAC;IACjDQ,UAAU,EAAE;MACVC,IAAI,EAAE,qBAAqB;MAC3BC,OAAO,EAAE,mBAAmB;MAC5BC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPL,IAAI,EAAE,EAAE;MACRG,KAAK,EAAE,EAAE;MACTG,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC;IACDC,KAAK,EAAE;MACLC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,gBAAgB,EAAEC,CAAC,CAAC,kBAAkB;IACxC,CAAC;IACDC,KAAK,EAAE;MACLC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAEJ,CAAC,CAAC,eAAe,CAAC;MACjCK,YAAY,EAAEL,CAAC,CAAC,cAAc,CAAC;MAC/BM,aAAa,EAAEN,CAAC,CAAC,eAAe;IAClC;EACF,CAAC,CAAC;EAEF,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMkC,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACnD9B,gBAAgB,CAAC+B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACH,OAAO,GAAG;QACT,GAAGG,IAAI,CAACH,OAAO,CAAC;QAChB,CAACC,KAAK,GAAGC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMnB,QAAQ,GAAGoB,UAAU,CAAClC,aAAa,CAACW,KAAK,CAACG,QAAQ,CAAC,IAAI,CAAC;IAC9D,MAAMC,YAAY,GAAGmB,UAAU,CAAClC,aAAa,CAACW,KAAK,CAACI,YAAY,CAAC,IAAI,CAAC;IACtE,MAAMoB,KAAK,GAAGrB,QAAQ,GAAGC,YAAY;IACrCd,gBAAgB,CAAC+B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPrB,KAAK,EAAE;QACL,GAAGqB,IAAI,CAACrB,KAAK;QACbK,WAAW,EAAEmB,KAAK,CAACC,OAAO,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,GAAG,GAAG,IAAI3C,KAAK,CAAC,CAAC;IACvB,MAAM4C,SAAS,GAAGD,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACC,KAAK;IAC7C,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,EAAE;IAElBN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,UAAU,CAAC,EAAEoB,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAEtEJ,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,aAAa,CAAC,EAAEoB,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAEzEJ,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,eAAe,CAAC,KAAK,IAAI8B,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAEP,MAAM,EAAEC,SAAS,CAAC;IACxFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,mBAAmB,CAAC,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IACnDA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,MAAM,CAAC,KAAKnB,aAAa,CAACE,UAAU,CAACC,IAAI,EAAE,EAAEwC,MAAM,EAAEC,SAAS,CAAC;IAC7EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,SAAS,CAAC,KAAKnB,aAAa,CAACE,UAAU,CAACE,OAAO,EAAE,EAAEuC,MAAM,EAAEC,SAAS,CAAC;IACnFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,WAAW,CAAC,KAAKnB,aAAa,CAACE,UAAU,CAACG,SAAS,EAAE,EAAEsC,MAAM,EAAEC,SAAS,CAAC;IACvFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,OAAO,CAAC,KAAKnB,aAAa,CAACE,UAAU,CAACI,KAAK,EAAE,EAAEqC,MAAM,EAAEC,SAAS,CAAC;IAC/EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,OAAO,CAAC,KAAKnB,aAAa,CAACE,UAAU,CAACK,KAAK,EAAE,EAAEoC,MAAM,EAAEC,SAAS,CAAC;IAC/EA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,gBAAgB,CAAC,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IAChDA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,MAAM,CAAC,KAAKnB,aAAa,CAACQ,OAAO,CAACL,IAAI,EAAE,EAAEwC,MAAM,EAAEC,SAAS,CAAC;IAC1EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,OAAO,CAAC,KAAKnB,aAAa,CAACQ,OAAO,CAACF,KAAK,EAAE,EAAEqC,MAAM,EAAEC,SAAS,CAAC;IAC5EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,SAAS,CAAC,KAAKnB,aAAa,CAACQ,OAAO,CAACC,OAAO,EAAE,EAAEkC,MAAM,EAAEC,SAAS,CAAC;IAChFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,YAAY,CAAC,KAAKnB,aAAa,CAACQ,OAAO,CAACE,UAAU,EAAE,EAAEiC,MAAM,EAAEC,SAAS,CAAC;IACtFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,cAAc,CAAC,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IAC9CA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,SAAS,CAAC,KAAKnB,aAAa,CAACW,KAAK,CAACC,OAAO,EAAE,EAAE+B,MAAM,EAAEC,SAAS,CAAC;IAC9EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,SAAS,CAAC,KAAKnB,aAAa,CAACW,KAAK,CAACE,OAAO,EAAE,EAAE8B,MAAM,EAAEC,SAAS,CAAC;IAC9EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,UAAU,CAAC,KAAKnB,aAAa,CAACW,KAAK,CAACG,QAAQ,EAAE,EAAE6B,MAAM,EAAEC,SAAS,CAAC;IAChFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,cAAc,CAAC,MAAMnB,aAAa,CAACW,KAAK,CAACI,YAAY,EAAE,EAAE4B,MAAM,EAAEC,SAAS,CAAC;IACzFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,aAAa,CAAC,MAAMnB,aAAa,CAACW,KAAK,CAACK,WAAW,EAAE,EAAE2B,MAAM,EAAEC,SAAS,CAAC;IACvFN,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCF,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,UAAU,CAAC,KAAKnB,aAAa,CAACW,KAAK,CAACM,QAAQ,EAAE,EAAE0B,MAAM,EAAEC,SAAS,CAAC;IAChFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,kBAAkB,CAAC,KAAKnB,aAAa,CAACW,KAAK,CAACO,gBAAgB,EAAE,EAAEyB,MAAM,EAAEC,SAAS,CAAC;IAChGA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,cAAc,CAAC,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IAC9CA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClC,MAAMK,OAAO,GAAGjB,UAAU,CAAClC,aAAa,CAACoB,KAAK,CAACC,cAAc,CAAC;IAC9D,MAAMc,KAAK,GAAGD,UAAU,CAAClC,aAAa,CAACW,KAAK,CAACK,WAAW,CAAC;IACzD,MAAMoC,OAAO,GAAGjB,KAAK,GAAG,CAAEgB,OAAO,GAAGhB,KAAK,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAClEE,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,gBAAgB,CAAC,MAAMnB,aAAa,CAACoB,KAAK,CAACC,cAAc,KAAK+B,OAAO,IAAI,EAAET,MAAM,EAAEC,SAAS,CAAC;IAC3GA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,cAAc,CAAC,MAAMnB,aAAa,CAACoB,KAAK,CAACE,YAAY,EAAE,EAAEqB,MAAM,EAAEC,SAAS,CAAC;IACzFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,oBAAoB,CAAC,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IACpDA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClC,MAAM1B,KAAK,GAAG,CACZ,MAAMD,CAAC,CAAC,eAAe,CAAC,KAAKnB,aAAa,CAACoB,KAAK,CAACG,aAAa,EAAE,EAChE,MAAMJ,CAAC,CAAC,cAAc,CAAC,KAAKnB,aAAa,CAACoB,KAAK,CAACI,YAAY,EAAE,EAC9D,MAAML,CAAC,CAAC,eAAe,CAAC,KAAKnB,aAAa,CAACoB,KAAK,CAACK,aAAa,EAAE,EAChE,MAAMN,CAAC,CAAC,OAAO,CAAC,EAAE,EAClB,MAAMA,CAAC,CAAC,OAAO,CAAC,EAAE,EAClB,MAAMA,CAAC,CAAC,OAAO,CAAC,EAAE,EAClB,MAAMA,CAAC,CAAC,OAAO,CAAC,EAAE,CACnB;IACDC,KAAK,CAACiC,OAAO,CAACC,IAAI,IAAI;MACpBhB,GAAG,CAACS,IAAI,CAACO,IAAI,EAAEX,MAAM,EAAEC,SAAS,EAAE;QAAEW,QAAQ,EAAEhB,SAAS,GAAG,CAAC,GAAGI;MAAO,CAAC,CAAC;MACvEC,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,YAAY,CAAC,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IAC5CA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,YAAY,CAAC,GAAG,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IAClDN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,SAAS,CAAC,GAAG,EAAEoB,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAChEA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACpDN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAER,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IACrEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG/C,aAAa,CAACE,UAAU,CAACC,IAAI,EAAE,EAAEwC,MAAM,EAAEC,SAAS,CAAC;IAC/DN,GAAG,CAACS,IAAI,CAAC,GAAG/C,aAAa,CAACQ,OAAO,CAACL,IAAI,EAAE,EAAEoC,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAC7EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,MAAM,CAAC,mBAAmB,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IAC5DN,GAAG,CAACS,IAAI,CAAC,GAAG5B,CAAC,CAAC,MAAM,CAAC,mBAAmB,EAAEoB,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAE7EA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,CAAC,CAAC;IAClBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC5B,CAAC,CAAC,YAAY,CAAC,EAAEoB,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAExE,MAAMQ,QAAQ,GAAG,aAAaxD,aAAa,CAACW,KAAK,CAACC,OAAO,IAAIZ,aAAa,CAACQ,OAAO,CAACL,IAAI,CAACsD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC5JrB,GAAG,CAACsB,IAAI,CAACJ,QAAQ,CAAC;EACpB,CAAC;EAED,oBACE3D,OAAA;IAAA2D,QAAA,EAAAK,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAkD,CAAC;AAEvD;AAAChE,EAAA,CAzMQD,YAAY;AAAAkE,EAAA,GAAZlE,YAAY;AA2MrB,eAAeA,YAAY;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}