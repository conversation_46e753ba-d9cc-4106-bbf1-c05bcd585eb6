const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const router = express.Router();

// Helper to generate user_id
function generateUserId(type) {
  const unique = Math.random().toString(36).substring(2, 10);
  if (type === 'admin') return `admin-${unique}`;
  if (type === 'admin-pan') return `admin-pan-${unique}`;
  if (type === 'team leader') return `teamlead-${unique}`;
  if (type === 'artisan') return `artisan-${unique}`;
  return `user-${unique}`;
}

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ msg: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, 's0meSup3r$tr0ng!Key2025');
    const user = await User.findById(decoded.id);
    if (!user || !user.verified) {
      return res.status(403).json({ msg: 'User not verified' });
    }
    req.user = user;
    next();
  } catch (err) {
    return res.status(403).json({ msg: 'Invalid token' });
  }
};

// Enrollment endpoint
router.post('/enroll', authenticateToken, async (req, res) => {
  try {
    const { username, password, email, phone_number, type } = req.body;
    
    // Validate required fields
    if (!username || !password || !phone_number || !type) {
      return res.status(400).json({ msg: 'Missing required fields' });
    }
    
    // Validate user type
    if (!['admin', 'admin-pan', 'team leader', 'artisan'].includes(type)) {
      return res.status(400).json({ msg: 'Invalid user type' });
    }
    
    // Check permissions
    const currentUserType = req.user.type;
    if (currentUserType === 'admin' && !['admin', 'admin-pan'].includes(type)) {
      return res.status(403).json({ msg: 'Admin can only enroll admin or admin-pan users' });
    }
    if (currentUserType === 'admin-pan' && type !== 'team leader') {
      return res.status(403).json({ msg: 'Admin-pan can only enroll team leaders' });
    }
    if (currentUserType === 'team leader' && type !== 'team leader') {
      return res.status(403).json({ msg: 'Team leaders can only enroll other team leaders' });
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({ username });
    if (existingUser) {
      return res.status(400).json({ msg: 'User already exists' });
    }
    
    // Create new user
    const user_id = generateUserId(type);
    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = new User({
      username,
      password: hashedPassword,
      email,
      phone_number,
      type,
      user_id,
      verified: false
    });
    
    await newUser.save();
    res.status(201).json({ 
      msg: 'User enrolled successfully', 
      user_id, 
      type,
      enrolledBy: req.user.username 
    });
  } catch (err) {
    res.status(500).json({ msg: 'Server error', error: err.message });
  }
});

// Register
router.post('/signup', async (req, res) => {
  try {
    const { username, password, email, phone_number, type } = req.body;
    if (!username || !password || !phone_number || !type) {
      return res.status(400).json({ msg: 'Missing required fields' });
    }
    if (!['admin', 'admin-pan', 'team leader', 'artisan'].includes(type)) {
      return res.status(400).json({ msg: 'Invalid user type' });
    }
    const existingUser = await User.findOne({ username });
    if (existingUser) return res.status(400).json({ msg: 'User already exists' });

    const user_id = generateUserId(type);
    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = new User({
      username,
      password: hashedPassword,
      email,
      phone_number,
      type,
      user_id,
      verified: false
    });
    await newUser.save();
    res.status(201).json({ msg: 'User created successfully', user_id, type });
  } catch (err) {
    res.status(500).json({ msg: 'Server error', error: err.message });
  }
});

// Login
router.post('/signin', async (req, res) => {
  try {
    const { username, password } = req.body;
    const user = await User.findOne({ username });
    if (!user) return res.status(400).json({ msg: 'User not found' });
    if (user.password === 'google_oauth_user') {
      return res.status(400).json({ msg: 'Please sign in with Google' });
    }
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) return res.status(400).json({ msg: 'Invalid credentials' });
    
    // Check if user is verified
    if (!user.verified) {
      return res.status(403).json({ msg: 'Account not verified. Please contact administrator.' });
    }
    
    const token = jwt.sign({ id: user._id }, 's0meSup3r$tr0ng!Key2025');
    res.json({ token, user_id: user.user_id, type: user.type, verified: user.verified });
  } catch (err) {
    res.status(500).json({ msg: 'Server error', error: err.message });
  }
});

// Google Auth
router.post('/google-auth', async (req, res) => {
  try {
    const { email, name, type, phone_number } = req.body;
    if (!email) return res.status(400).json({ msg: 'Email is required for Google Auth' });
    let user = await User.findOne({ username: email });
    if (!user) {
      // For Google Auth, require phone_number and type if first time
      if (!phone_number || !type) {
        return res.status(400).json({ msg: 'phone_number and type required for first-time Google sign-in' });
      }
      if (!['admin', 'admin-pan', 'team leader', 'artisan'].includes(type)) {
        return res.status(400).json({ msg: 'Invalid user type' });
      }
      const user_id = generateUserId(type);
      user = new User({
        username: email,
        password: 'google_oauth_user',
        email,
        phone_number,
        type,
        user_id,
        verified: false
      });
      await user.save();
    }
    
    // Check if user is verified (for existing users)
    if (!user.verified) {
      return res.status(403).json({ msg: 'Account not verified. Please contact administrator.' });
    }
    
    const token = jwt.sign({ id: user._id, username: user.username }, 's0meSup3r$tr0ng!Key2025');
    res.json({ token, username: user.username, user_id: user.user_id, type: user.type, verified: user.verified });
  } catch (err) {
    res.status(500).json({ msg: 'Server error', error: err.message });
  }
});

module.exports = router;
