/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { TestResult } from '@jest/test-result';
import type { Config } from '@jest/types';
export default function getResultHeader(result: TestResult, globalConfig: Config.GlobalConfig, projectConfig?: Config.ProjectConfig): string;
