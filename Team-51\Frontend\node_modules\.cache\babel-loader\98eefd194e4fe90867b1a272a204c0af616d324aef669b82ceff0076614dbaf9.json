{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\components\\\\dashboard\\\\AgreementPDF.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport jsPDF from 'jspdf';\nimport './AgreementPDF.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AgreementPDF() {\n  _s();\n  const [agreementData, setAgreementData] = useState({\n    teamLeader: {\n      name: '<PERSON><PERSON>',\n      village: 'Kumargram Village',\n      clusterId: 'TL-001',\n      phone: '+91-9876543212',\n      email: '<EMAIL>'\n    },\n    artisan: {\n      name: '',\n      phone: '',\n      address: '',\n      experience: ''\n    },\n    order: {\n      orderId: '',\n      product: 'Handwoven Sarees',\n      quantity: '',\n      pricePerUnit: '',\n      totalAmount: '',\n      deadline: '',\n      qualityStandards: 'Premium handloom quality with traditional patterns'\n    },\n    terms: {\n      advancePayment: '',\n      finalPayment: '',\n      deliveryTerms: 'Delivery to be made at the designated collection center',\n      qualityCheck: 'All products subject to quality inspection before final payment',\n      penaltyClause: '2% deduction for each day of delay beyond agreed deadline'\n    }\n  });\n  const [showForm, setShowForm] = useState(false);\n  const handleInputChange = (section, field, value) => {\n    setAgreementData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n  const calculateTotalAmount = () => {\n    const quantity = parseFloat(agreementData.order.quantity) || 0;\n    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;\n    const total = quantity * pricePerUnit;\n    setAgreementData(prev => ({\n      ...prev,\n      order: {\n        ...prev.order,\n        totalAmount: total.toFixed(2)\n      }\n    }));\n  };\n  const generatePDF = () => {\n    const doc = new jsPDF();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    let yPosition = 30;\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ARTISAN PRODUCTION AGREEMENT', pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Between Team Leader and Artisan', pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.text(`Agreement Date: ${new Date().toLocaleDateString()}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('TEAM LEADER DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Name: ${agreementData.teamLeader.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Village: ${agreementData.teamLeader.village}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Cluster ID: ${agreementData.teamLeader.clusterId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Phone: ${agreementData.teamLeader.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Email: ${agreementData.teamLeader.email}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ARTISAN DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Name: ${agreementData.artisan.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Phone: ${agreementData.artisan.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Address: ${agreementData.artisan.address}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Experience: ${agreementData.artisan.experience}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ORDER DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Order ID: ${agreementData.order.orderId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Product: ${agreementData.order.product}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Quantity: ${agreementData.order.quantity} pieces`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Price per Unit: ₹${agreementData.order.pricePerUnit}`, margin, yPosition);\n    yPosition += 6;\n    doc.setFont('helvetica', 'bold');\n    doc.text(`Total Amount: ₹${agreementData.order.totalAmount}`, margin, yPosition);\n    doc.setFont('helvetica', 'normal');\n    yPosition += 6;\n    doc.text(`Delivery Deadline: ${agreementData.order.deadline}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Quality Standards: ${agreementData.order.qualityStandards}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('PAYMENT TERMS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const advance = parseFloat(agreementData.terms.advancePayment);\n    const total = parseFloat(agreementData.order.totalAmount);\n    const percent = total ? (advance / total * 100).toFixed(1) : '0';\n    doc.text(`Advance Payment: ₹${agreementData.terms.advancePayment} (${percent}%)`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Final Payment: ₹${agreementData.terms.finalPayment} (Upon delivery and quality approval)`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('TERMS AND CONDITIONS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const terms = [`1. Delivery: ${agreementData.terms.deliveryTerms}`, `2. Quality: ${agreementData.terms.qualityCheck}`, `3. Penalty: ${agreementData.terms.penaltyClause}`, `4. The artisan agrees to maintain the specified quality standards throughout production.`, `5. Any defective products will be replaced at the artisan's cost.`, `6. This agreement is valid for the specified order only.`, `7. Both parties agree to resolve disputes through mutual discussion.`];\n    terms.forEach(term => {\n      doc.text(term, margin, yPosition, {\n        maxWidth: pageWidth - 2 * margin\n      });\n      yPosition += 8;\n    });\n    yPosition += 20;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text('SIGNATURES:', margin, yPosition);\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Team Leader:', margin, yPosition);\n    doc.text('Artisan:', pageWidth - margin - 60, yPosition);\n    yPosition += 15;\n    doc.text('_____________________', margin, yPosition);\n    doc.text('_____________________', pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);\n    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text('Date: _______________', margin, yPosition);\n    doc.text('Date: _______________', pageWidth - margin - 60, yPosition);\n    yPosition += 20;\n    doc.setFontSize(8);\n    doc.setFont('helvetica', 'italic');\n    doc.text('This agreement is generated electronically and is valid without physical signatures for record purposes.', pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"agreement-pdf-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"agreement-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Generate Artisan Agreement\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Create a formal agreement between Team Leader and Artisan with pricing details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"toggle-form-btn\",\n        onClick: () => setShowForm(!showForm),\n        children: showForm ? 'Hide Form' : 'Create New Agreement'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"agreement-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Artisan Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Artisan Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.artisan.name,\n              onChange: e => handleInputChange('artisan', 'name', e.target.value),\n              placeholder: \"Enter artisan's full name\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Phone Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              value: agreementData.artisan.phone,\n              onChange: e => handleInputChange('artisan', 'phone', e.target.value),\n              placeholder: \"+91-XXXXXXXXXX\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group full-width\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: agreementData.artisan.address,\n              onChange: e => handleInputChange('artisan', 'address', e.target.value),\n              placeholder: \"Enter complete address\",\n              rows: \"2\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Experience\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.artisan.experience,\n              onChange: e => handleInputChange('artisan', 'experience', e.target.value),\n              placeholder: \"e.g., 5 years in handloom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Order Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Order ID *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.order.orderId,\n              onChange: e => handleInputChange('order', 'orderId', e.target.value),\n              placeholder: \"e.g., ORD001\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Quantity *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: agreementData.order.quantity,\n              onChange: e => {\n                handleInputChange('order', 'quantity', e.target.value);\n                setTimeout(calculateTotalAmount, 100);\n              },\n              placeholder: \"Number of pieces\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Price per Unit (\\u20B9) *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              step: \"0.01\",\n              value: agreementData.order.pricePerUnit,\n              onChange: e => {\n                handleInputChange('order', 'pricePerUnit', e.target.value);\n                setTimeout(calculateTotalAmount, 100);\n              },\n              placeholder: \"Price per saree\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Total Amount (\\u20B9)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.order.totalAmount,\n              readOnly: true,\n              className: \"readonly-field\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Delivery Deadline *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: agreementData.order.deadline,\n              onChange: e => handleInputChange('order', 'deadline', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Payment Terms\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Advance Payment (\\u20B9) *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              step: \"0.01\",\n              value: agreementData.terms.advancePayment,\n              onChange: e => {\n                handleInputChange('terms', 'advancePayment', e.target.value);\n                const remaining = parseFloat(agreementData.order.totalAmount) - parseFloat(e.target.value);\n                handleInputChange('terms', 'finalPayment', remaining.toFixed(2));\n              },\n              placeholder: \"Advance amount\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Final Payment (\\u20B9)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.terms.finalPayment,\n              readOnly: true,\n              className: \"readonly-field\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"cancel-btn\",\n          onClick: () => setShowForm(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"generate-btn\",\n          onClick: generatePDF,\n          disabled: !agreementData.artisan.name || !agreementData.order.orderId || !agreementData.order.quantity,\n          children: \"Generate PDF Agreement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sample-agreements\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Recent Agreements\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"agreements-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"agreement-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"agreement-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Agreement with Priya Sharma\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Order: ORD001 | 40 Handwoven Sarees | \\u20B932,000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"agreement-date\",\n              children: \"Created: 2024-01-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"download-btn\",\n            children: \"Download PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"agreement-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"agreement-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Agreement with Meera Patel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Order: ORD002 | 45 Handwoven Sarees | \\u20B936,000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"agreement-date\",\n              children: \"Created: 2024-01-08\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"download-btn\",\n            children: \"Download PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 5\n  }, this);\n}\n_s(AgreementPDF, \"+Z4/CRoYVtZMggtIxpqlgE+G4Dg=\");\n_c = AgreementPDF;\nexport default AgreementPDF;\nvar _c;\n$RefreshReg$(_c, \"AgreementPDF\");", "map": {"version": 3, "names": ["React", "useState", "jsPDF", "jsxDEV", "_jsxDEV", "AgreementPDF", "_s", "agreementData", "setAgreementData", "<PERSON><PERSON><PERSON><PERSON>", "name", "village", "clusterId", "phone", "email", "artisan", "address", "experience", "order", "orderId", "product", "quantity", "pricePerUnit", "totalAmount", "deadline", "qualityStandards", "terms", "advancePayment", "finalPayment", "deliveryTerms", "qualityCheck", "penalty<PERSON><PERSON><PERSON>", "showForm", "setShowForm", "handleInputChange", "section", "field", "value", "prev", "calculateTotalAmount", "parseFloat", "total", "toFixed", "generatePDF", "doc", "pageWidth", "internal", "pageSize", "width", "margin", "yPosition", "setFontSize", "setFont", "text", "align", "Date", "toLocaleDateString", "advance", "percent", "for<PERSON>ach", "term", "max<PERSON><PERSON><PERSON>", "fileName", "replace", "toISOString", "split", "save", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "onChange", "e", "target", "placeholder", "required", "rows", "setTimeout", "step", "readOnly", "remaining", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/components/dashboard/AgreementPDF.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport jsPDF from 'jspdf';\nimport './AgreementPDF.css';\n\nfunction AgreementPDF() {\n\n  const [agreementData, setAgreementData] = useState({\n    teamLeader: {\n      name: '<PERSON><PERSON>',\n      village: 'Kumargram Village',\n      clusterId: 'TL-001',\n      phone: '+91-9876543212',\n      email: '<EMAIL>'\n    },\n    artisan: {\n      name: '',\n      phone: '',\n      address: '',\n      experience: ''\n    },\n    order: {\n      orderId: '',\n      product: 'Handwoven Sarees',\n      quantity: '',\n      pricePerUnit: '',\n      totalAmount: '',\n      deadline: '',\n      qualityStandards: 'Premium handloom quality with traditional patterns'\n    },\n    terms: {\n      advancePayment: '',\n      finalPayment: '',\n      deliveryTerms: 'Delivery to be made at the designated collection center',\n      qualityCheck: 'All products subject to quality inspection before final payment',\n      penaltyClause: '2% deduction for each day of delay beyond agreed deadline'\n    }\n  });\n\n  const [showForm, setShowForm] = useState(false);\n\n  const handleInputChange = (section, field, value) => {\n    setAgreementData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n\n  const calculateTotalAmount = () => {\n    const quantity = parseFloat(agreementData.order.quantity) || 0;\n    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;\n    const total = quantity * pricePerUnit;\n    setAgreementData(prev => ({\n      ...prev,\n      order: {\n        ...prev.order,\n        totalAmount: total.toFixed(2)\n      }\n    }));\n  };\n\n  const generatePDF = () => {\n    const doc = new jsPDF();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    let yPosition = 30;\n\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ARTISAN PRODUCTION AGREEMENT', pageWidth / 2, yPosition, { align: 'center' });\n\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Between Team Leader and Artisan', pageWidth / 2, yPosition, { align: 'center' });\n\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.text(`Agreement Date: ${new Date().toLocaleDateString()}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('TEAM LEADER DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Name: ${agreementData.teamLeader.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Village: ${agreementData.teamLeader.village}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Cluster ID: ${agreementData.teamLeader.clusterId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Phone: ${agreementData.teamLeader.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Email: ${agreementData.teamLeader.email}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ARTISAN DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Name: ${agreementData.artisan.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Phone: ${agreementData.artisan.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Address: ${agreementData.artisan.address}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Experience: ${agreementData.artisan.experience}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ORDER DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Order ID: ${agreementData.order.orderId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Product: ${agreementData.order.product}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Quantity: ${agreementData.order.quantity} pieces`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Price per Unit: ₹${agreementData.order.pricePerUnit}`, margin, yPosition);\n    yPosition += 6;\n    doc.setFont('helvetica', 'bold');\n    doc.text(`Total Amount: ₹${agreementData.order.totalAmount}`, margin, yPosition);\n    doc.setFont('helvetica', 'normal');\n    yPosition += 6;\n    doc.text(`Delivery Deadline: ${agreementData.order.deadline}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Quality Standards: ${agreementData.order.qualityStandards}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('PAYMENT TERMS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const advance = parseFloat(agreementData.terms.advancePayment);\n    const total = parseFloat(agreementData.order.totalAmount);\n    const percent = total ? ((advance / total) * 100).toFixed(1) : '0';\n    doc.text(`Advance Payment: ₹${agreementData.terms.advancePayment} (${percent}%)`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Final Payment: ₹${agreementData.terms.finalPayment} (Upon delivery and quality approval)`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('TERMS AND CONDITIONS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const terms = [\n      `1. Delivery: ${agreementData.terms.deliveryTerms}`,\n      `2. Quality: ${agreementData.terms.qualityCheck}`,\n      `3. Penalty: ${agreementData.terms.penaltyClause}`,\n      `4. The artisan agrees to maintain the specified quality standards throughout production.`,\n      `5. Any defective products will be replaced at the artisan's cost.`,\n      `6. This agreement is valid for the specified order only.`,\n      `7. Both parties agree to resolve disputes through mutual discussion.`\n    ];\n    terms.forEach(term => {\n      doc.text(term, margin, yPosition, { maxWidth: pageWidth - 2 * margin });\n      yPosition += 8;\n    });\n    yPosition += 20;\n\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text('SIGNATURES:', margin, yPosition);\n    yPosition += 20;\n\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Team Leader:', margin, yPosition);\n    doc.text('Artisan:', pageWidth - margin - 60, yPosition);\n    yPosition += 15;\n\n    doc.text('_____________________', margin, yPosition);\n    doc.text('_____________________', pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);\n    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text('Date: _______________', margin, yPosition);\n    doc.text('Date: _______________', pageWidth - margin - 60, yPosition);\n\n    yPosition += 20;\n    doc.setFontSize(8);\n    doc.setFont('helvetica', 'italic');\n    doc.text('This agreement is generated electronically and is valid without physical signatures for record purposes.', pageWidth / 2, yPosition, { align: 'center' });\n\n    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  };\n\n  return (\n    <div className=\"agreement-pdf-container\">\n      <div className=\"agreement-header\">\n        <h2>Generate Artisan Agreement</h2>\n        <p>Create a formal agreement between Team Leader and Artisan with pricing details</p>\n        <button\n          className=\"toggle-form-btn\"\n          onClick={() => setShowForm(!showForm)}\n        >\n          {showForm ? 'Hide Form' : 'Create New Agreement'}\n        </button>\n      </div>\n\n      {showForm && (\n        <div className=\"agreement-form\">\n          <div className=\"form-section\">\n            <h3>Artisan Information</h3>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Artisan Name *</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.artisan.name}\n                  onChange={(e) => handleInputChange('artisan', 'name', e.target.value)}\n                  placeholder=\"Enter artisan's full name\"\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Phone Number *</label>\n                <input\n                  type=\"tel\"\n                  value={agreementData.artisan.phone}\n                  onChange={(e) => handleInputChange('artisan', 'phone', e.target.value)}\n                  placeholder=\"+91-XXXXXXXXXX\"\n                  required\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group full-width\">\n                <label>Address *</label>\n                <textarea\n                  value={agreementData.artisan.address}\n                  onChange={(e) => handleInputChange('artisan', 'address', e.target.value)}\n                  placeholder=\"Enter complete address\"\n                  rows=\"2\"\n                  required\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Experience</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.artisan.experience}\n                  onChange={(e) => handleInputChange('artisan', 'experience', e.target.value)}\n                  placeholder=\"e.g., 5 years in handloom\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h3>Order Details</h3>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Order ID *</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.order.orderId}\n                  onChange={(e) => handleInputChange('order', 'orderId', e.target.value)}\n                  placeholder=\"e.g., ORD001\"\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Quantity *</label>\n                <input\n                  type=\"number\"\n                  value={agreementData.order.quantity}\n                  onChange={(e) => {\n                    handleInputChange('order', 'quantity', e.target.value);\n                    setTimeout(calculateTotalAmount, 100);\n                  }}\n                  placeholder=\"Number of pieces\"\n                  required\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Price per Unit (₹) *</label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={agreementData.order.pricePerUnit}\n                  onChange={(e) => {\n                    handleInputChange('order', 'pricePerUnit', e.target.value);\n                    setTimeout(calculateTotalAmount, 100);\n                  }}\n                  placeholder=\"Price per saree\"\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Total Amount (₹)</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.order.totalAmount}\n                  readOnly\n                  className=\"readonly-field\"\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Delivery Deadline *</label>\n                <input\n                  type=\"date\"\n                  value={agreementData.order.deadline}\n                  onChange={(e) => handleInputChange('order', 'deadline', e.target.value)}\n                  required\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h3>Payment Terms</h3>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Advance Payment (₹) *</label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={agreementData.terms.advancePayment}\n                  onChange={(e) => {\n                    handleInputChange('terms', 'advancePayment', e.target.value);\n                    const remaining = parseFloat(agreementData.order.totalAmount) - parseFloat(e.target.value);\n                    handleInputChange('terms', 'finalPayment', remaining.toFixed(2));\n                  }}\n                  placeholder=\"Advance amount\"\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Final Payment (₹)</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.terms.finalPayment}\n                  readOnly\n                  className=\"readonly-field\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-actions\">\n            <button\n              type=\"button\"\n              className=\"cancel-btn\"\n              onClick={() => setShowForm(false)}\n            >\n              Cancel\n            </button>\n            <button\n              type=\"button\"\n              className=\"generate-btn\"\n              onClick={generatePDF}\n              disabled={!agreementData.artisan.name || !agreementData.order.orderId || !agreementData.order.quantity}\n            >\n              Generate PDF Agreement\n            </button>\n          </div>\n        </div>\n      )}\n\n      <div className=\"sample-agreements\">\n        <h3>Recent Agreements</h3>\n        <div className=\"agreements-list\">\n          <div className=\"agreement-item\">\n            <div className=\"agreement-info\">\n              <h4>Agreement with Priya Sharma</h4>\n              <p>Order: ORD001 | 40 Handwoven Sarees | ₹32,000</p>\n              <span className=\"agreement-date\">Created: 2024-01-10</span>\n            </div>\n            <button className=\"download-btn\">Download PDF</button>\n          </div>\n          <div className=\"agreement-item\">\n            <div className=\"agreement-info\">\n              <h4>Agreement with Meera Patel</h4>\n              <p>Order: ORD002 | 45 Handwoven Sarees | ₹36,000</p>\n              <span className=\"agreement-date\">Created: 2024-01-08</span>\n            </div>\n            <button className=\"download-btn\">Download PDF</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default AgreementPDF;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EAEtB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGP,QAAQ,CAAC;IACjDQ,UAAU,EAAE;MACVC,IAAI,EAAE,qBAAqB;MAC3BC,OAAO,EAAE,mBAAmB;MAC5BC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPL,IAAI,EAAE,EAAE;MACRG,KAAK,EAAE,EAAE;MACTG,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC;IACDC,KAAK,EAAE;MACLC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,gBAAgB,EAAE;IACpB,CAAC;IACDC,KAAK,EAAE;MACLC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,yDAAyD;MACxEC,YAAY,EAAE,iEAAiE;MAC/EC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMiC,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACnD7B,gBAAgB,CAAC8B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACH,OAAO,GAAG;QACT,GAAGG,IAAI,CAACH,OAAO,CAAC;QAChB,CAACC,KAAK,GAAGC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMlB,QAAQ,GAAGmB,UAAU,CAACjC,aAAa,CAACW,KAAK,CAACG,QAAQ,CAAC,IAAI,CAAC;IAC9D,MAAMC,YAAY,GAAGkB,UAAU,CAACjC,aAAa,CAACW,KAAK,CAACI,YAAY,CAAC,IAAI,CAAC;IACtE,MAAMmB,KAAK,GAAGpB,QAAQ,GAAGC,YAAY;IACrCd,gBAAgB,CAAC8B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPpB,KAAK,EAAE;QACL,GAAGoB,IAAI,CAACpB,KAAK;QACbK,WAAW,EAAEkB,KAAK,CAACC,OAAO,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,GAAG,GAAG,IAAI1C,KAAK,CAAC,CAAC;IACvB,MAAM2C,SAAS,GAAGD,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACC,KAAK;IAC7C,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,EAAE;IAElBN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,8BAA8B,EAAER,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAEvFJ,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,iCAAiC,EAAER,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAE1FJ,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACS,IAAI,CAAC,mBAAmB,IAAIE,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAEP,MAAM,EAAEC,SAAS,CAAC;IACjFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,sBAAsB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACnDA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,SAAS9C,aAAa,CAACE,UAAU,CAACC,IAAI,EAAE,EAAEuC,MAAM,EAAEC,SAAS,CAAC;IACrEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,YAAY9C,aAAa,CAACE,UAAU,CAACE,OAAO,EAAE,EAAEsC,MAAM,EAAEC,SAAS,CAAC;IAC3EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,eAAe9C,aAAa,CAACE,UAAU,CAACG,SAAS,EAAE,EAAEqC,MAAM,EAAEC,SAAS,CAAC;IAChFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,UAAU9C,aAAa,CAACE,UAAU,CAACI,KAAK,EAAE,EAAEoC,MAAM,EAAEC,SAAS,CAAC;IACvEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,UAAU9C,aAAa,CAACE,UAAU,CAACK,KAAK,EAAE,EAAEmC,MAAM,EAAEC,SAAS,CAAC;IACvEA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,kBAAkB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC/CA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,SAAS9C,aAAa,CAACQ,OAAO,CAACL,IAAI,EAAE,EAAEuC,MAAM,EAAEC,SAAS,CAAC;IAClEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,UAAU9C,aAAa,CAACQ,OAAO,CAACF,KAAK,EAAE,EAAEoC,MAAM,EAAEC,SAAS,CAAC;IACpEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,YAAY9C,aAAa,CAACQ,OAAO,CAACC,OAAO,EAAE,EAAEiC,MAAM,EAAEC,SAAS,CAAC;IACxEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,eAAe9C,aAAa,CAACQ,OAAO,CAACE,UAAU,EAAE,EAAEgC,MAAM,EAAEC,SAAS,CAAC;IAC9EA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,gBAAgB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC7CA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,aAAa9C,aAAa,CAACW,KAAK,CAACC,OAAO,EAAE,EAAE8B,MAAM,EAAEC,SAAS,CAAC;IACvEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,YAAY9C,aAAa,CAACW,KAAK,CAACE,OAAO,EAAE,EAAE6B,MAAM,EAAEC,SAAS,CAAC;IACtEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,aAAa9C,aAAa,CAACW,KAAK,CAACG,QAAQ,SAAS,EAAE4B,MAAM,EAAEC,SAAS,CAAC;IAC/EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,oBAAoB9C,aAAa,CAACW,KAAK,CAACI,YAAY,EAAE,EAAE2B,MAAM,EAAEC,SAAS,CAAC;IACnFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,kBAAkB9C,aAAa,CAACW,KAAK,CAACK,WAAW,EAAE,EAAE0B,MAAM,EAAEC,SAAS,CAAC;IAChFN,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCF,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,sBAAsB9C,aAAa,CAACW,KAAK,CAACM,QAAQ,EAAE,EAAEyB,MAAM,EAAEC,SAAS,CAAC;IACjFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,sBAAsB9C,aAAa,CAACW,KAAK,CAACO,gBAAgB,EAAE,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IACzFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,gBAAgB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC7CA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClC,MAAMK,OAAO,GAAGjB,UAAU,CAACjC,aAAa,CAACmB,KAAK,CAACC,cAAc,CAAC;IAC9D,MAAMc,KAAK,GAAGD,UAAU,CAACjC,aAAa,CAACW,KAAK,CAACK,WAAW,CAAC;IACzD,MAAMmC,OAAO,GAAGjB,KAAK,GAAG,CAAEgB,OAAO,GAAGhB,KAAK,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAClEE,GAAG,CAACS,IAAI,CAAC,qBAAqB9C,aAAa,CAACmB,KAAK,CAACC,cAAc,KAAK+B,OAAO,IAAI,EAAET,MAAM,EAAEC,SAAS,CAAC;IACpGA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,mBAAmB9C,aAAa,CAACmB,KAAK,CAACE,YAAY,uCAAuC,EAAEqB,MAAM,EAAEC,SAAS,CAAC;IACvHA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACpDA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClC,MAAM1B,KAAK,GAAG,CACZ,gBAAgBnB,aAAa,CAACmB,KAAK,CAACG,aAAa,EAAE,EACnD,eAAetB,aAAa,CAACmB,KAAK,CAACI,YAAY,EAAE,EACjD,eAAevB,aAAa,CAACmB,KAAK,CAACK,aAAa,EAAE,EAClD,0FAA0F,EAC1F,mEAAmE,EACnE,0DAA0D,EAC1D,sEAAsE,CACvE;IACDL,KAAK,CAACiC,OAAO,CAACC,IAAI,IAAI;MACpBhB,GAAG,CAACS,IAAI,CAACO,IAAI,EAAEX,MAAM,EAAEC,SAAS,EAAE;QAAEW,QAAQ,EAAEhB,SAAS,GAAG,CAAC,GAAGI;MAAO,CAAC,CAAC;MACvEC,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,aAAa,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC1CA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,cAAc,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC3CN,GAAG,CAACS,IAAI,CAAC,UAAU,EAAER,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IACxDA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACpDN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAER,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IACrEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG9C,aAAa,CAACE,UAAU,CAACC,IAAI,EAAE,EAAEuC,MAAM,EAAEC,SAAS,CAAC;IAC/DN,GAAG,CAACS,IAAI,CAAC,GAAG9C,aAAa,CAACQ,OAAO,CAACL,IAAI,EAAE,EAAEmC,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAC7EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACpDN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAER,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAErEA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,CAAC,CAAC;IAClBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,0GAA0G,EAAER,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAEnK,MAAMQ,QAAQ,GAAG,aAAavD,aAAa,CAACW,KAAK,CAACC,OAAO,IAAIZ,aAAa,CAACQ,OAAO,CAACL,IAAI,CAACqD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC5JrB,GAAG,CAACsB,IAAI,CAACJ,QAAQ,CAAC;EACpB,CAAC;EAED,oBACE1D,OAAA;IAAK+D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtChE,OAAA;MAAK+D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BhE,OAAA;QAAAgE,QAAA,EAAI;MAA0B;QAAAN,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnE,OAAA;QAAAgE,QAAA,EAAG;MAA8E;QAAAN,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrFnE,OAAA;QACE+D,SAAS,EAAC,iBAAiB;QAC3BK,OAAO,EAAEA,CAAA,KAAMvC,WAAW,CAAC,CAACD,QAAQ,CAAE;QAAAoC,QAAA,EAErCpC,QAAQ,GAAG,WAAW,GAAG;MAAsB;QAAA8B,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELvC,QAAQ,iBACP5B,OAAA;MAAK+D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BhE,OAAA;QAAK+D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhE,OAAA;UAAAgE,QAAA,EAAI;QAAmB;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BnE,OAAA;UAAK+D,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAc;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BnE,OAAA;cACEqE,IAAI,EAAC,MAAM;cACXpC,KAAK,EAAE9B,aAAa,CAACQ,OAAO,CAACL,IAAK;cAClCgE,QAAQ,EAAGC,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,MAAM,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cACtEwC,WAAW,EAAC,2BAA2B;cACvCC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAc;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BnE,OAAA;cACEqE,IAAI,EAAC,KAAK;cACVpC,KAAK,EAAE9B,aAAa,CAACQ,OAAO,CAACF,KAAM;cACnC6D,QAAQ,EAAGC,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cACvEwC,WAAW,EAAC,gBAAgB;cAC5BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnE,OAAA;UAAK+D,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBhE,OAAA;YAAK+D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpChE,OAAA;cAAAgE,QAAA,EAAO;YAAS;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBnE,OAAA;cACEiC,KAAK,EAAE9B,aAAa,CAACQ,OAAO,CAACC,OAAQ;cACrC0D,QAAQ,EAAGC,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,SAAS,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cACzEwC,WAAW,EAAC,wBAAwB;cACpCE,IAAI,EAAC,GAAG;cACRD,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnE,OAAA;UAAK+D,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBhE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAU;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBnE,OAAA;cACEqE,IAAI,EAAC,MAAM;cACXpC,KAAK,EAAE9B,aAAa,CAACQ,OAAO,CAACE,UAAW;cACxCyD,QAAQ,EAAGC,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,YAAY,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cAC5EwC,WAAW,EAAC;YAA2B;cAAAf,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnE,OAAA;QAAK+D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhE,OAAA;UAAAgE,QAAA,EAAI;QAAa;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBnE,OAAA;UAAK+D,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAU;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBnE,OAAA;cACEqE,IAAI,EAAC,MAAM;cACXpC,KAAK,EAAE9B,aAAa,CAACW,KAAK,CAACC,OAAQ;cACnCuD,QAAQ,EAAGC,CAAC,IAAKzC,iBAAiB,CAAC,OAAO,EAAE,SAAS,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cACvEwC,WAAW,EAAC,cAAc;cAC1BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAU;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBnE,OAAA;cACEqE,IAAI,EAAC,QAAQ;cACbpC,KAAK,EAAE9B,aAAa,CAACW,KAAK,CAACG,QAAS;cACpCqD,QAAQ,EAAGC,CAAC,IAAK;gBACfzC,iBAAiB,CAAC,OAAO,EAAE,UAAU,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC;gBACtD2C,UAAU,CAACzC,oBAAoB,EAAE,GAAG,CAAC;cACvC,CAAE;cACFsC,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnE,OAAA;UAAK+D,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAoB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnCnE,OAAA;cACEqE,IAAI,EAAC,QAAQ;cACbQ,IAAI,EAAC,MAAM;cACX5C,KAAK,EAAE9B,aAAa,CAACW,KAAK,CAACI,YAAa;cACxCoD,QAAQ,EAAGC,CAAC,IAAK;gBACfzC,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC;gBAC1D2C,UAAU,CAACzC,oBAAoB,EAAE,GAAG,CAAC;cACvC,CAAE;cACFsC,WAAW,EAAC,iBAAiB;cAC7BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAgB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/BnE,OAAA;cACEqE,IAAI,EAAC,MAAM;cACXpC,KAAK,EAAE9B,aAAa,CAACW,KAAK,CAACK,WAAY;cACvC2D,QAAQ;cACRf,SAAS,EAAC;YAAgB;cAAAL,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnE,OAAA;UAAK+D,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBhE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAmB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClCnE,OAAA;cACEqE,IAAI,EAAC,MAAM;cACXpC,KAAK,EAAE9B,aAAa,CAACW,KAAK,CAACM,QAAS;cACpCkD,QAAQ,EAAGC,CAAC,IAAKzC,iBAAiB,CAAC,OAAO,EAAE,UAAU,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cACxEyC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnE,OAAA;QAAK+D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhE,OAAA;UAAAgE,QAAA,EAAI;QAAa;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBnE,OAAA;UAAK+D,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAqB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCnE,OAAA;cACEqE,IAAI,EAAC,QAAQ;cACbQ,IAAI,EAAC,MAAM;cACX5C,KAAK,EAAE9B,aAAa,CAACmB,KAAK,CAACC,cAAe;cAC1C+C,QAAQ,EAAGC,CAAC,IAAK;gBACfzC,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,EAAEyC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC;gBAC5D,MAAM8C,SAAS,GAAG3C,UAAU,CAACjC,aAAa,CAACW,KAAK,CAACK,WAAW,CAAC,GAAGiB,UAAU,CAACmC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC;gBAC1FH,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAEiD,SAAS,CAACzC,OAAO,CAAC,CAAC,CAAC,CAAC;cAClE,CAAE;cACFmC,WAAW,EAAC,gBAAgB;cAC5BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA;cAAAgE,QAAA,EAAO;YAAiB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChCnE,OAAA;cACEqE,IAAI,EAAC,MAAM;cACXpC,KAAK,EAAE9B,aAAa,CAACmB,KAAK,CAACE,YAAa;cACxCsD,QAAQ;cACRf,SAAS,EAAC;YAAgB;cAAAL,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnE,OAAA;QAAK+D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhE,OAAA;UACEqE,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,YAAY;UACtBK,OAAO,EAAEA,CAAA,KAAMvC,WAAW,CAAC,KAAK,CAAE;UAAAmC,QAAA,EACnC;QAED;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnE,OAAA;UACEqE,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,cAAc;UACxBK,OAAO,EAAE7B,WAAY;UACrByC,QAAQ,EAAE,CAAC7E,aAAa,CAACQ,OAAO,CAACL,IAAI,IAAI,CAACH,aAAa,CAACW,KAAK,CAACC,OAAO,IAAI,CAACZ,aAAa,CAACW,KAAK,CAACG,QAAS;UAAA+C,QAAA,EACxG;QAED;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnE,OAAA;MAAK+D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChE,OAAA;QAAAgE,QAAA,EAAI;MAAiB;QAAAN,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BnE,OAAA;QAAK+D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BhE,OAAA;UAAK+D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhE,OAAA;YAAK+D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhE,OAAA;cAAAgE,QAAA,EAAI;YAA2B;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpCnE,OAAA;cAAAgE,QAAA,EAAG;YAA6C;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpDnE,OAAA;cAAM+D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAmB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNnE,OAAA;YAAQ+D,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAY;YAAAN,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNnE,OAAA;UAAK+D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhE,OAAA;YAAK+D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhE,OAAA;cAAAgE,QAAA,EAAI;YAA0B;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnCnE,OAAA;cAAAgE,QAAA,EAAG;YAA6C;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpDnE,OAAA;cAAM+D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAmB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNnE,OAAA;YAAQ+D,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAY;YAAAN,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAT,QAAA,EAAAO,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjE,EAAA,CAhZQD,YAAY;AAAAgF,EAAA,GAAZhF,YAAY;AAkZrB,eAAeA,YAAY;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}