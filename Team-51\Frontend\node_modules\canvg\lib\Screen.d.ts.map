{"version": 3, "file": "Screen.d.ts", "sourceRoot": "", "sources": ["../src/Screen.ts"], "names": [], "mappings": "AACA,OAAO,EACN,kBAAkB,EAClB,MAAM,SAAS,CAAC;AAMjB,OAAO,QAAQ,MAAM,YAAY,CAAC;AAClC,OAAO,KAAK,MAAM,SAAS,CAAC;AAC5B,OAAO,QAAQ,EAAE,EAChB,OAAO,EACP,cAAc,EACd,MAAM,YAAY,CAAC;AAEpB,MAAM,WAAW,cAAc;IAC9B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC;CACrB;AAED,MAAM,WAAW,mBAAmB;IACnC;;OAEG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;OAEG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B;;OAEG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,WAAW,CAAC,IAAI,OAAO,CAAC;CACxB;AAED,MAAM,WAAW,oBAAoB;IACpC,QAAQ,EAAE,QAAQ,CAAC;IACnB,GAAG,EAAE,kBAAkB,CAAC;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,KAAK,EAAE,MAAM,CAAC;IACd,YAAY,EAAE,MAAM,CAAC;IACrB,MAAM,EAAE,MAAM,CAAC;IACf,aAAa,EAAE,MAAM,CAAC;IACtB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAKD,QAAA,MAAM,YAAY,cAEX,CAAC;AAER,MAAM,CAAC,OAAO,OAAO,MAAM;IAsBzB,QAAQ,CAAC,GAAG,EAAE,kBAAkB;IArBjC,MAAM,CAAC,QAAQ,CAAC,aAAa,6BAAiB;IAC9C,MAAM,CAAC,QAAQ,CAAC,YAAY,eAAgB;IAE5C,SAAS,SAAM;IACf,kBAAkB,SAAS;IAC3B,YAAY,SAAO;IACnB,aAAa,SAAO;IACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB,QAAQ,CAAC,KAAK,EAAE,OAAO,YAAY,CAAC;IACpC,QAAQ,CAAC,QAAQ,WAAkB;IACnC,QAAQ,CAAC,KAAK,QAAmB;IACjC,QAAQ,CAAC,UAAU,EAAE,cAAc,EAAE,CAAM;IAC3C,OAAO,CAAC,YAAY,CAAgB;IACpC,OAAO,CAAC,YAAY,CAAa;IACjC,OAAO,CAAC,KAAK,CAAyB;IACtC,OAAO,CAAC,aAAa,CAAK;IAC1B,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,aAAa,CAAQ;IAC7B,OAAO,CAAC,UAAU,CAAgB;gBAGxB,GAAG,EAAE,kBAAkB,EAChC,EACC,KAAoB,EACpB,MAAsB,EACtB,GAAE,cAAmB;IAMvB,IAAI,CAAC,OAAO,EAAE,MAAM,OAAO;IAI3B,KAAK;IASL,OAAO;IAoBP,WAAW,CAAC,GAAG,EAAE,kBAAkB;IAQnC,UAAU,CAAC,EACV,QAAQ,EACR,GAAG,EACH,WAAW,EACX,KAAK,EACL,YAAY,EACZ,MAAM,EACN,aAAa,EACb,IAAQ,EACR,IAAQ,EACR,IAAI,EACJ,IAAI,EACJ,IAAY,EACZ,KAAS,EACT,KAAS,EACT,EAAE,oBAAoB;IAuGvB,KAAK,CACJ,OAAO,EAAE,OAAO,EAChB,EACC,YAAoB,EACpB,WAAmB,EACnB,eAAuB,EACvB,gBAAwB,EACxB,WAAmB,EACnB,WAAW,EACX,UAAU,EACV,WAAW,EACX,OAAO,EACP,OAAO,EACP,GAAE,mBAAwB;IAkE5B,IAAI;IASJ,OAAO,CAAC,YAAY;IAoCpB,OAAO,CAAC,MAAM;CA6Hd"}