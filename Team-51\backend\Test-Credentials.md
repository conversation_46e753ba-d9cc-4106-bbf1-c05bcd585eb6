# Test Credentials for NGO Dashboard

## 🔐 Login Credentials

**All users have the same password: `password123`**

---

## 👑 Admin Users

### 1. Super Admin
- **Username:** `<EMAIL>`
- **Password:** `password123`
- **Type:** `admin`
- **User ID:** `admin-12345678`
- **Phone:** `+91-9876543210`
- **Status:** ✅ Verified

### 2. PAN Admin
- **Username:** `<EMAIL>`
- **Password:** `password123`
- **Type:** `admin-pan`
- **User ID:** `admin-pan-87654321`
- **Phone:** `+91-9876543211`
- **Status:** ✅ Verified

---

## 👨‍💼 Team Leaders

### 1. <PERSON><PERSON> (Primary TL for Dashboard)
- **Username:** `<EMAIL>`
- **Password:** `password123`
- **Type:** `team leader`
- **User ID:** `teamlead-tl001`
- **Phone:** `+91-9876543212`
- **Village:** Kumargram Village
- **Skill:** Handloom & Textile Crafts
- **Status:** ✅ Verified

### 2. Priya Patel
- **Username:** `<EMAIL>`
- **Password:** `password123`
- **Type:** `team leader`
- **User ID:** `teamlead-tl002`
- **Phone:** `+91-9876543213`
- **Status:** ✅ Verified

### 3. Meera Singh
- **Username:** `<EMAIL>`
- **Password:** `password123`
- **Type:** `team leader`
- **User ID:** `teamlead-tl003`
- **Phone:** `+91-9876543214`
- **Status:** ✅ Verified

---

## 🎨 Artisans

### 1. Priya Sharma
- **Username:** `priya.sharma`
- **Password:** `password123`
- **Type:** `artisan`
- **User ID:** `artisan-art001`
- **Phone:** `+91-9876543215`
- **Skill:** Handloom & Textile Crafts
- **Status:** ✅ Verified

### 2. Rajesh Kumar
- **Username:** `rajesh.kumar`
- **Password:** `password123`
- **Type:** `artisan`
- **User ID:** `artisan-art002`
- **Phone:** `+91-9876543216`
- **Skill:** Handloom & Textile Crafts
- **Status:** ✅ Verified

### 3. Meera Patel
- **Username:** `meera.patel`
- **Password:** `password123`
- **Type:** `artisan`
- **User ID:** `artisan-art003`
- **Phone:** `+91-9876543217`
- **Skill:** Handloom & Textile Crafts
- **Status:** ✅ Verified

### 4. Amit Singh
- **Username:** `amit.singh`
- **Password:** `password123`
- **Type:** `artisan`
- **User ID:** `artisan-art004`
- **Phone:** `+91-9876543218`
- **Skill:** Handloom & Textile Crafts
- **Status:** ✅ Verified

### 5. Sunita Devi
- **Username:** `sunita.devi`
- **Password:** `password123`
- **Type:** `artisan`
- **User ID:** `artisan-art005`
- **Phone:** `+91-9876543219`
- **Skill:** Handloom & Textile Crafts
- **Status:** ✅ Verified

### 6. Kavita Devi
- **Username:** `kavita.devi`
- **Password:** `password123`
- **Type:** `artisan`
- **User ID:** `artisan-art006`
- **Phone:** `+91-9876543220`
- **Skill:** Handloom & Textile Crafts
- **Status:** ✅ Verified

---

## 📊 MongoDB Compass Setup

### 1. Database Connection
- **Connection String:** `mongodb://127.0.0.1:27017/ngo_users`
- **Database Name:** `ngo_users`
- **Collection Name:** `users`

### 2. Import Data Steps

#### Option A: Import JSON File
1. Open MongoDB Compass
2. Connect to `mongodb://127.0.0.1:27017/ngo_users`
3. Navigate to `ngo_users` database
4. Click on `users` collection (create if doesn't exist)
5. Click "ADD DATA" → "Import JSON or CSV file"
6. Select the `dummy-credentials.json` file
7. Click "Import"

#### Option B: Manual Insert
1. Open MongoDB Compass
2. Connect to database
3. Go to `users` collection
4. Click "INSERT DOCUMENT"
5. Copy and paste individual user objects from the JSON file

### 3. Verify Data
After importing, you should see 11 users:
- 1 Super Admin
- 1 PAN Admin  
- 3 Team Leaders
- 6 Artisans

---

## 🧪 Testing Login

### Frontend Login Test
Use these credentials in your React login form:

**For Team Leader Dashboard:**
- Username: `<EMAIL>`
- Password: `password123`

**For Admin Panel:**
- Username: `<EMAIL>`
- Password: `password123`

### API Testing (Postman/curl)
```bash
# Login API Test
curl -X POST http://localhost:5000/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123"
  }'
```

**Expected Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user_id": "teamlead-tl001",
  "type": "team leader",
  "verified": true
}
```

---

## 🔑 Password Hash Information

The password hash `$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi` corresponds to the plain text password `password123`.

This was generated using bcryptjs with salt rounds = 10:
```javascript
const bcrypt = require('bcryptjs');
const hash = await bcrypt.hash('password123', 10);
```

---

## 🚀 Quick Start Commands

### 1. Start MongoDB
```bash
mongod
```

### 2. Start Backend Server
```bash
cd backend
npm start
```

### 3. Start Frontend
```bash
cd Frontend
npm start
```

### 4. Test Login
Navigate to `http://localhost:3001` and use any of the credentials above.

---

## 📝 Notes

- All users are pre-verified (`verified: true`)
- Phone numbers follow Indian format
- User IDs follow the pattern: `{type}-{identifier}`
- All artisans specialize in "Handloom & Textile Crafts"
- Team Leader "Rajesh Kumar Sharma" matches the dashboard profile data

These credentials will allow you to test all aspects of the Team Leader Dashboard and Artisan Assignment system!
