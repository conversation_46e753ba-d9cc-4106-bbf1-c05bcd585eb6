[{"C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js": "3", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js": "4", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js": "5", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js": "7", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js": "9", "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\services\\api.js": "10"}, {"size": 394, "mtime": 1752339457265, "results": "11", "hashOfConfig": "12"}, {"size": 3206, "mtime": 1752357059125, "results": "13", "hashOfConfig": "12"}, {"size": 3376, "mtime": 1752339457260, "results": "14", "hashOfConfig": "12"}, {"size": 2467, "mtime": 1752339457264, "results": "15", "hashOfConfig": "12"}, {"size": 3568, "mtime": 1752339457263, "results": "16", "hashOfConfig": "12"}, {"size": 2870, "mtime": 1752349920510, "results": "17", "hashOfConfig": "12"}, {"size": 5668, "mtime": 1752349920507, "results": "18", "hashOfConfig": "12"}, {"size": 20517, "mtime": 1752347705929, "results": "19", "hashOfConfig": "12"}, {"size": 17456, "mtime": 1752347599041, "results": "20", "hashOfConfig": "12"}, {"size": 2770, "mtime": 1752349920512, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yx484d", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Footer.js", ["52", "53", "54", "55"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\Hero.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\common\\AuthForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\TLDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\components\\dashboard\\ArtisanAssignment.js", ["56", "57"], [], "C:\\Users\\<USER>\\Desktop\\Team 51\\Team-51\\Frontend\\src\\services\\api.js", [], [], {"ruleId": "58", "severity": 1, "message": "59", "line": 15, "column": 15, "nodeType": "60", "endLine": 15, "endColumn": 58}, {"ruleId": "58", "severity": 1, "message": "59", "line": 18, "column": 15, "nodeType": "60", "endLine": 18, "endColumn": 58}, {"ruleId": "58", "severity": 1, "message": "59", "line": 21, "column": 15, "nodeType": "60", "endLine": 21, "endColumn": 58}, {"ruleId": "58", "severity": 1, "message": "59", "line": 24, "column": 15, "nodeType": "60", "endLine": 24, "endColumn": 58}, {"ruleId": "61", "severity": 1, "message": "62", "line": 1, "column": 27, "nodeType": "63", "messageId": "64", "endLine": 1, "endColumn": 36}, {"ruleId": "61", "severity": 1, "message": "65", "line": 150, "column": 10, "nodeType": "63", "messageId": "64", "endLine": 150, "endColumn": 24}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'showAssignForm' is assigned a value but never used."]