define(["exports","react"],(function(e,n){"use strict";function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var s=t({area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),r=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function a(e){var n={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},t=e.match(/<\/?([^\s]+?)[/\s>]/);if(t&&(n.name=t[1],(s[t[1]]||"/"===e.charAt(e.length-2))&&(n.voidElement=!0),n.name.startsWith("!--"))){var a=e.indexOf("--\x3e");return{type:"comment",comment:-1!==a?e.slice(4,a):""}}for(var i=new RegExp(r),o=null;null!==(o=i.exec(e));)if(o[0].trim())if(o[1]){var l=o[1].trim(),c=[l,""];l.indexOf("=")>-1&&(c=l.split("=")),n.attrs[c[0]]=c[1],i.lastIndex--}else o[2]&&(n.attrs[o[2]]=o[3].trim().substring(1,o[3].length-1));return n}var i=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,o=/^\s*$/,l=Object.create(null);var c=function(e,n){n||(n={}),n.components||(n.components=l);var t,s=[],r=[],c=-1,u=!1;if(0!==e.indexOf("<")){var p=e.indexOf("<");s.push({type:"text",content:-1===p?e:e.substring(0,p)})}return e.replace(i,(function(i,l){if(u){if(i!=="</"+t.name+">")return;u=!1}var p,d="/"!==i.charAt(1),f=i.startsWith("\x3c!--"),m=l+i.length,g=e.charAt(m);if(f){var h=a(i);return c<0?(s.push(h),s):((p=r[c]).children.push(h),s)}if(d&&(c++,"tag"===(t=a(i)).type&&n.components[t.name]&&(t.type="component",u=!0),t.voidElement||u||!g||"<"===g||t.children.push({type:"text",content:e.slice(m,e.indexOf("<",m))}),0===c&&s.push(t),(p=r[c-1])&&p.children.push(t),r[c]=t),(!d||t.voidElement)&&(c>-1&&(t.voidElement||t.name===i.slice(2,-1))&&(c--,t=-1===c?s:r[c]),!u&&"<"!==g&&g)){p=-1===c?s:r[c].children;var y=e.indexOf("<",m),N=e.slice(m,-1===y?void 0:y);o.test(N)&&(N=" "),(y>-1&&c+p.length>=0||" "!==N)&&p.push({type:"text",content:N})}})),s};const u=(e,n,t,s)=>{const r=[t,{code:n,...s||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(r,"warn","react-i18next::",!0);y(r[0])&&(r[0]=`react-i18next:: ${r[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...r):console?.warn&&console.warn(...r)},p={},d=(e,n,t,s)=>{y(t)&&p[t]||(y(t)&&(p[t]=new Date),u(e,n,t,s))},f=(e,n)=>()=>{if(e.isInitialized)n();else{const t=()=>{setTimeout((()=>{e.off("initialized",t)}),0),n()};e.on("initialized",t)}},m=(e,n,t)=>{e.loadNamespaces(n,f(e,t))},g=(e,n,t,s)=>{if(y(t)&&(t=[t]),e.options.preload&&e.options.preload.indexOf(n)>-1)return m(e,t,s);t.forEach((n=>{e.options.ns.indexOf(n)<0&&e.options.ns.push(n)})),e.loadLanguages(n,f(e,s))},h=e=>e.displayName||e.name||(y(e)&&e.length>0?e:"Unknown"),y=e=>"string"==typeof e,N=e=>"object"==typeof e&&null!==e,x=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,b={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},v=e=>b[e];let E={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(x,v)};const O=(e={})=>{E={...E,...e}},I=()=>E;let S;const w=e=>{S=e},$=()=>S,k=(e,n)=>{if(!e)return!1;const t=e.props?.children??e.children;return n?t.length>0:!!t},T=e=>{if(!e)return[];const n=e.props?.children??e.children;return e.props?.i18nIsDynamicList?A(n):n},A=e=>Array.isArray(e)?e:[e],R=(e,t,s,r)=>{if(!e)return"";let a="";const i=A(e),o=t?.transSupportBasicHtmlNodes?t.transKeepBasicHtmlNodesFor??[]:[];return i.forEach(((e,i)=>{if(y(e))a+=`${e}`;else if(n.isValidElement(e)){const{props:n,type:l}=e,c=Object.keys(n).length,u=o.indexOf(l)>-1,p=n.children;if(!p&&u&&!c)return void(a+=`<${l}/>`);if(!p&&(!u||c)||n.i18nIsDynamicList)return void(a+=`<${i}></${i}>`);if(u&&1===c&&y(p))return void(a+=`<${l}>${p}</${l}>`);const d=R(p,t,s,r);a+=`<${i}>${d}</${i}>`}else if(null!==e)if(N(e)){const{format:n,...t}=e,i=Object.keys(t);if(1===i.length){const e=n?`${i[0]}, ${n}`:i[0];return void(a+=`{{${e}}}`)}u(s,"TRANS_INVALID_OBJ","Invalid child - Object should only have keys {{ value, format }} (format is optional).",{i18nKey:r,child:e})}else u(s,"TRANS_INVALID_VAR","Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.",{i18nKey:r,child:e});else u(s,"TRANS_NULL_VALUE","Passed in a null value as child",{i18nKey:r})})),a},j=(e,t,s,r,a,i,o)=>{if(""===s)return[];const l=a.transKeepBasicHtmlNodesFor||[],u=s&&new RegExp(l.map((e=>`<${e}`)).join("|")).test(s);if(!(e||t||u||o))return[s];const p=t??{},d=e=>{A(e).forEach((e=>{y(e)||(k(e)?d(T(e)):N(e)&&!n.isValidElement(e)&&Object.assign(p,e))}))};d(e);const f=c(`<0>${s}</0>`),m={...p,...i},g=(e,t,s)=>{const r=T(e),a=x(r,t.children,s);return(e=>Array.isArray(e)&&e.every(n.isValidElement))(r)&&0===a.length||e.props?.i18nIsDynamicList?r:a},h=(e,t,s,r,a)=>{e.dummy?(e.children=t,s.push(n.cloneElement(e,{key:r},a?void 0:t))):s.push(...n.Children.map([e],(e=>{const s={...e.props};return delete s.i18nIsDynamicList,n.createElement(e.type,{...s,key:r,ref:e.props.ref??e.ref},a?null:t)})))},x=(e,s,i)=>{const c=A(e);return A(s).reduce(((e,s,p)=>{const d=s.children?.[0]?.content&&r.services.interpolator.interpolate(s.children[0].content,m,r.language);if("tag"===s.type){let o=c[parseInt(s.name,10)];!o&&t&&(o=t[s.name]),1!==i.length||o||(o=i[0][s.name]),o||(o={});const f=0!==Object.keys(s.attrs).length?((e,n)=>{const t={...n};return t.props=Object.assign(e.props,n.props),t})({props:s.attrs},o):o,b=n.isValidElement(f),v=b&&k(s,!0)&&!s.voidElement,E=u&&N(f)&&f.dummy&&!b,O=N(t)&&Object.hasOwnProperty.call(t,s.name);if(y(f)){const n=r.services.interpolator.interpolate(f,m,r.language);e.push(n)}else if(k(f)||v){const n=g(f,s,i);h(f,n,e,p)}else if(E){const n=x(c,s.children,i);h(f,n,e,p)}else if(Number.isNaN(parseFloat(s.name)))if(O){const n=g(f,s,i);h(f,n,e,p,s.voidElement)}else if(a.transSupportBasicHtmlNodes&&l.indexOf(s.name)>-1)if(s.voidElement)e.push(n.createElement(s.name,{key:`${s.name}-${p}`}));else{const t=x(c,s.children,i);e.push(n.createElement(s.name,{key:`${s.name}-${p}`},t))}else if(s.voidElement)e.push(`<${s.name} />`);else{const n=x(c,s.children,i);e.push(`<${s.name}>${n}</${s.name}>`)}else if(N(f)&&!b){const n=s.children[0]?d:null;n&&e.push(n)}else h(f,d,e,p,1!==s.children.length||!d)}else if("text"===s.type){const t=a.transWrapTextNodes,i=o?a.unescape(r.services.interpolator.interpolate(s.content,m,r.language)):r.services.interpolator.interpolate(s.content,m,r.language);t?e.push(n.createElement(t,{key:`${s.name}-${p}`},i)):e.push(i)}return e}),[])},b=x([{dummy:!0,children:e||[]}],f,A(e||[]));return T(b[0])},C=(e,t,s)=>{const r=e.key||t,a=n.cloneElement(e,{key:r});if(!a.props||!a.props.children||s.indexOf(`${t}/>`)<0&&s.indexOf(`${t} />`)<0)return a;return n.createElement((function(){return n.createElement(n.Fragment,null,a)}),{key:r})},L=(e,n,t,s)=>e?Array.isArray(e)?((e,n)=>e.map(((e,t)=>C(e,t,n))))(e,n):N(e)?((e,n)=>{const t={};return Object.keys(e).forEach((s=>{Object.assign(t,{[s]:C(e[s],s,n)})})),t})(e,n):(d(t,"TRANS_INVALID_COMPONENTS",'<Trans /> "components" prop expects an object or array',{i18nKey:s}),null):null,P=e=>!!N(e)&&(!Array.isArray(e)&&Object.keys(e).reduce(((e,n)=>e&&Number.isNaN(Number.parseFloat(n))),!0));function V({children:e,count:t,parent:s,i18nKey:r,context:a,tOptions:i={},values:o,defaults:l,components:c,ns:u,i18n:p,t:f,shouldUnescape:m,...g}){const h=p||$();if(!h)return d(h,"NO_I18NEXT_INSTANCE","Trans: You need to pass in an i18next instance using i18nextReactModule",{i18nKey:r}),e;const N=f||h.t.bind(h)||(e=>e),x={...I(),...h.options?.react};let b=u||N.ns||h.options?.defaultNS;b=y(b)?[b]:b||["translation"];const v=R(e,x,h,r),E=l||v||x.transEmptyNodeValue||r,{hashTransKey:O}=x,S=r||(O?O(v||E):v||E);h.options?.interpolation?.defaultVariables&&(o=o&&Object.keys(o).length>0?{...o,...h.options.interpolation.defaultVariables}:{...h.options.interpolation.defaultVariables});const w=o||void 0!==t&&!h.options?.interpolation?.alwaysFormat||!e?i.interpolation:{interpolation:{...i.interpolation,prefix:"#$?",suffix:"?$#"}},k={...i,context:a||i.context,count:t,...o,...w,defaultValue:E,ns:b},T=S?N(S,k):E,A=L(c,T,h,r);let C=A||e,V=null;P(A)&&(V=A,C=e);const _=j(C,V,T,h,x,k,m),D=s??x.defaultTransParent;return D?n.createElement(D,g,_):_}const _={type:"3rdParty",init(e){O(e.options.react),w(e)}},D=n.createContext();class K{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const z=e=>async n=>({...await(e.getInitialProps?.(n))??{},...U()}),U=()=>{const e=$(),n=e.reportNamespaces?.getUsedNamespaces()??[],t={},s={};return e.languages.forEach((t=>{s[t]={},n.forEach((n=>{s[t][n]=e.getResourceBundle(t,n)||{}}))})),t.initialI18nStore=s,t.initialLanguage=e.language,t};const F=(e,n,t,s)=>e.getFixedT(n,t,s),B=(e,t={})=>{const{i18n:s}=t,{i18n:r,defaultNS:a}=n.useContext(D)||{},i=s||r||$();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new K),!i){d(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const e=(e,n)=>y(n)?n:N(n)&&y(n.defaultValue)?n.defaultValue:Array.isArray(e)?e[e.length-1]:e,n=[e,{},!1];return n.t=e,n.i18n={},n.ready=!1,n}i.options.react?.wait&&d(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={...I(),...i.options.react,...t},{useSuspense:l,keyPrefix:c}=o;let u=e||a||i.options?.defaultNS;u=y(u)?[u]:u||["translation"],i.reportNamespaces.addUsedNamespaces?.(u);const p=(i.isInitialized||i.initializedStoreOnce)&&u.every((e=>((e,n,t={})=>n.languages&&n.languages.length?n.hasLoadedNamespace(e,{lng:t.lng,precheck:(n,s)=>{if(t.bindI18n?.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!s(n.isLanguageChangingTo,e))return!1}}):(d(n,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:n.languages}),!0))(e,i,o))),f=((e,t,s,r)=>n.useCallback(F(e,t,s,r),[e,t,s,r]))(i,t.lng||null,"fallback"===o.nsMode?u:u[0],c),h=()=>f,x=()=>F(i,t.lng||null,"fallback"===o.nsMode?u:u[0],c),[b,v]=n.useState(h);let E=u.join();t.lng&&(E=`${t.lng}${E}`);const O=((e,t)=>{const s=n.useRef();return n.useEffect((()=>{s.current=e}),[e,t]),s.current})(E),S=n.useRef(!0);n.useEffect((()=>{const{bindI18n:e,bindI18nStore:n}=o;S.current=!0,p||l||(t.lng?g(i,t.lng,u,(()=>{S.current&&v(x)})):m(i,u,(()=>{S.current&&v(x)}))),p&&O&&O!==E&&S.current&&v(x);const s=()=>{S.current&&v(x)};return e&&i?.on(e,s),n&&i?.store.on(n,s),()=>{S.current=!1,i&&e?.split(" ").forEach((e=>i.off(e,s))),n&&i&&n.split(" ").forEach((e=>i.store.off(e,s)))}}),[i,E]),n.useEffect((()=>{S.current&&p&&v(h)}),[i,c,p]);const w=[b,i,p];if(w.t=b,w.i18n=i,w.ready=p,p)return w;if(!p&&!l)return w;throw new Promise((e=>{t.lng?g(i,t.lng,u,(()=>e())):m(i,u,(()=>e()))}))};const W=(e,t,s={})=>{const{i18n:r}=s,{i18n:a}=n.useContext(D)||{},i=r||a||$();i.options?.isClone||(e&&!i.initializedStoreOnce&&(i.services.resourceStore.data=e,i.options.ns=Object.values(e).reduce(((e,n)=>(Object.keys(n).forEach((n=>{e.indexOf(n)<0&&e.push(n)})),e)),i.options.ns),i.initializedStoreOnce=!0,i.isInitialized=!0),t&&!i.initializedLanguageOnce&&(i.changeLanguage(t),i.initializedLanguageOnce=!0))};e.I18nContext=D,e.I18nextProvider=function({i18n:e,defaultNS:t,children:s}){const r=n.useMemo((()=>({i18n:e,defaultNS:t})),[e,t]);return n.createElement(D.Provider,{value:r},s)},e.Trans=function({children:e,count:t,parent:s,i18nKey:r,context:a,tOptions:i={},values:o,defaults:l,components:c,ns:u,i18n:p,t:d,shouldUnescape:f,...m}){const{i18n:g,defaultNS:h}=n.useContext(D)||{},y=p||g||$(),N=d||y?.t.bind(y);return V({children:e,count:t,parent:s,i18nKey:r,context:a,tOptions:i,values:o,defaults:l,components:c,ns:u||N?.ns||h||y?.options?.defaultNS,i18n:y,t:d,shouldUnescape:f,...m})},e.TransWithoutContext=V,e.Translation=({ns:e,children:n,...t})=>{const[s,r,a]=B(e,t);return n(s,{i18n:r,lng:r.language},a)},e.composeInitialProps=z,e.date=()=>"",e.getDefaults=I,e.getI18n=$,e.getInitialProps=U,e.initReactI18next=_,e.number=()=>"",e.plural=()=>"",e.select=()=>"",e.selectOrdinal=()=>"",e.setDefaults=O,e.setI18n=w,e.time=()=>"",e.useSSR=W,e.useTranslation=B,e.withSSR=()=>function(e){function t({initialI18nStore:t,initialLanguage:s,...r}){return W(t,s),n.createElement(e,{...r})}return t.getInitialProps=z(e),t.displayName=`withI18nextSSR(${h(e)})`,t.WrappedComponent=e,t},e.withTranslation=(e,t={})=>function(s){function r({forwardedRef:r,...a}){const[i,o,l]=B(e,{...a,keyPrefix:t.keyPrefix}),c={...a,t:i,i18n:o,tReady:l};return t.withRef&&r?c.ref=r:!t.withRef&&r&&(c.forwardedRef=r),n.createElement(s,c)}r.displayName=`withI18nextTranslation(${h(s)})`,r.WrappedComponent=s;return t.withRef?n.forwardRef(((e,t)=>n.createElement(r,Object.assign({},e,{forwardedRef:t})))):r}}));
