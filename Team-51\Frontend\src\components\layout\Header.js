import React, { useState } from 'react';
import { userUtils } from '../../services/api';
import './Header.css';

function Header({ onSignIn }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const userInfo = userUtils.getUserInfo();
  const { username, userType, verified } = userInfo;

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleLogout = () => {
    userUtils.clearUserInfo();
    window.location.reload();
  };

  return (
    <header className="header">
      <div className="header-container">
        <div className="header-logo">
          <img 
            src="/logo192.png" 
            alt="PANS NGO Logo" 
            className="header-logo-img"
          />
          <div className="header-logo-text">
            <h1>PANS</h1>
            <span className="header-tagline">Empowering Communities</span>
          </div>
        </div>

        <nav className={`header-nav ${isMenuOpen ? 'header-nav-open' : ''}`}>
          <ul className="header-nav-list">
            <li><a href="#home" className="header-nav-link">Home</a></li>
            <li><a href="#about" className="header-nav-link">About Us</a></li>
            <li><a href="#programs" className="header-nav-link">Programs</a></li>
            <li><a href="#get-involved" className="header-nav-link">Get Involved</a></li>
            <li><a href="#contact" className="header-nav-link">Contact</a></li>
          </ul>
        </nav>

        <div className="header-actions">
          {username ? (
            <div className="header-user-menu">
              <div className="header-user-info">
                <span className="header-welcome">
                  Welcome, <strong>{username}</strong>
                </span>
                {userType && (
                  <span className="header-user-type">
                    {userType.charAt(0).toUpperCase() + userType.slice(1)}
                  </span>
                )}
                {!verified && (
                  <span className="header-verification-status">
                    ⚠️ Account Pending Verification
                  </span>
                )}
              </div>
              <button className="header-btn header-btn-logout" onClick={handleLogout}>
                Logout
              </button>
            </div>
          ) : (
            <div className="header-auth-buttons">
              <button className="header-btn header-btn-primary" onClick={onSignIn}>
                Sign In
              </button>
            </div>
          )}
        </div>

        <button className="header-menu-toggle" onClick={toggleMenu}>
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </header>
  );
}

export default Header;
