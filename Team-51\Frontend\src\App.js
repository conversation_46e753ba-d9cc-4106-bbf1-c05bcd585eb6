import React, { useState } from 'react';
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import Hero from './components/pages/Hero';
import About from './components/pages/About';
import AuthForm from './components/common/AuthForm';
import { GoogleOAuthProvider } from '@react-oauth/google';
import './styles/globals.css';

function App() {
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState('signin'); // 'signin' or 'signup'

  // Pass these handlers to Header
  const handleSignIn = () => {
    setAuthMode('signin');
    setAuthModalOpen(true);
  };
  
  const handleSignUp = () => {
    setAuthMode('signup');
    setAuthModalOpen(true);
  };

  const handleCloseModal = () => setAuthModalOpen(false);

  return (
    <GoogleOAuthProvider clientId="###">
      <div className="App">
        <Header onSignIn={handleSignIn} onSignUp={handleSignUp} />
        <main>
          <Hero />
          <About />
        </main>
        <Footer />
        <AuthForm isOpen={authModalOpen} onClose={handleCloseModal} initialMode={authMode} />
      </div>
    </GoogleOAuthProvider>
  );
}

export default App;
