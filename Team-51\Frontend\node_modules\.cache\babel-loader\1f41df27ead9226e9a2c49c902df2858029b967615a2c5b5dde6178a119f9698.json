{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\components\\\\dashboard\\\\AgreementPDF.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport jsPDF from 'jspdf';\nimport './AgreementPDF.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AgreementPDF() {\n  _s();\n  const [agreementData, setAgreementData] = useState({\n    teamLeader: {\n      name: '<PERSON><PERSON>',\n      village: 'Kumargram Village',\n      clusterId: 'TL-001',\n      phone: '+91-9876543212',\n      email: '<EMAIL>'\n    },\n    artisan: {\n      name: '',\n      phone: '',\n      address: '',\n      experience: ''\n    },\n    order: {\n      orderId: '',\n      product: 'Handwoven Sarees',\n      quantity: '',\n      pricePerUnit: '',\n      totalAmount: '',\n      deadline: '',\n      qualityStandards: 'Premium handloom quality with traditional patterns'\n    },\n    terms: {\n      advancePayment: '',\n      finalPayment: '',\n      deliveryTerms: 'Delivery to be made at the designated collection center',\n      qualityCheck: 'All products subject to quality inspection before final payment',\n      penaltyClause: '2% deduction for each day of delay beyond agreed deadline'\n    }\n  });\n  const [showForm, setShowForm] = useState(false);\n  const handleInputChange = (section, field, value) => {\n    setAgreementData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n  const calculateTotalAmount = () => {\n    const quantity = parseFloat(agreementData.order.quantity) || 0;\n    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;\n    const total = quantity * pricePerUnit;\n    setAgreementData(prev => ({\n      ...prev,\n      order: {\n        ...prev.order,\n        totalAmount: total.toFixed(2)\n      }\n    }));\n  };\n  const generatePDF = () => {\n    const doc = new jsPDF();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    let yPosition = 30;\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ARTISAN PRODUCTION AGREEMENT', pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Between Team Leader and Artisan', pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.text(`Agreement Date: ${new Date().toLocaleDateString()}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('TEAM LEADER DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Name: ${agreementData.teamLeader.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Village: ${agreementData.teamLeader.village}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Cluster ID: ${agreementData.teamLeader.clusterId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Phone: ${agreementData.teamLeader.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Email: ${agreementData.teamLeader.email}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ARTISAN DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Name: ${agreementData.artisan.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Phone: ${agreementData.artisan.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Address: ${agreementData.artisan.address}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Experience: ${agreementData.artisan.experience}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ORDER DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Order ID: ${agreementData.order.orderId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Product: ${agreementData.order.product}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Quantity: ${agreementData.order.quantity} pieces`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Price per Unit: ₹${agreementData.order.pricePerUnit}`, margin, yPosition);\n    yPosition += 6;\n    doc.setFont('helvetica', 'bold');\n    doc.text(`Total Amount: ₹${agreementData.order.totalAmount}`, margin, yPosition);\n    doc.setFont('helvetica', 'normal');\n    yPosition += 6;\n    doc.text(`Delivery Deadline: ${agreementData.order.deadline}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Quality Standards: ${agreementData.order.qualityStandards}`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('PAYMENT TERMS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const advance = parseFloat(agreementData.terms.advancePayment);\n    const total = parseFloat(agreementData.order.totalAmount);\n    const percent = total ? (advance / total * 100).toFixed(1) : '0';\n    doc.text(`Advance Payment: ₹${agreementData.terms.advancePayment} (${percent}%)`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Final Payment: ₹${agreementData.terms.finalPayment} (Upon delivery and quality approval)`, margin, yPosition);\n    yPosition += 15;\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('TERMS AND CONDITIONS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const terms = [`1. Delivery: ${agreementData.terms.deliveryTerms}`, `2. Quality: ${agreementData.terms.qualityCheck}`, `3. Penalty: ${agreementData.terms.penaltyClause}`, `4. The artisan agrees to maintain the specified quality standards throughout production.`, `5. Any defective products will be replaced at the artisan's cost.`, `6. This agreement is valid for the specified order only.`, `7. Both parties agree to resolve disputes through mutual discussion.`];\n    terms.forEach(term => {\n      doc.text(term, margin, yPosition, {\n        maxWidth: pageWidth - 2 * margin\n      });\n      yPosition += 8;\n    });\n    yPosition += 20;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text('SIGNATURES:', margin, yPosition);\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Team Leader:', margin, yPosition);\n    doc.text('Artisan:', pageWidth - margin - 60, yPosition);\n    yPosition += 15;\n    doc.text('_____________________', margin, yPosition);\n    doc.text('_____________________', pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);\n    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text('Date: _______________', margin, yPosition);\n    doc.text('Date: _______________', pageWidth - margin - 60, yPosition);\n    yPosition += 20;\n    doc.setFontSize(8);\n    doc.setFont('helvetica', 'italic');\n    doc.text('This agreement is generated electronically and is valid without physical signatures for record purposes.', pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 5\n  }, this);\n}\n_s(AgreementPDF, \"+Z4/CRoYVtZMggtIxpqlgE+G4Dg=\");\n_c = AgreementPDF;\nexport default AgreementPDF;\nvar _c;\n$RefreshReg$(_c, \"AgreementPDF\");", "map": {"version": 3, "names": ["React", "useState", "jsPDF", "jsxDEV", "_jsxDEV", "AgreementPDF", "_s", "agreementData", "setAgreementData", "<PERSON><PERSON><PERSON><PERSON>", "name", "village", "clusterId", "phone", "email", "artisan", "address", "experience", "order", "orderId", "product", "quantity", "pricePerUnit", "totalAmount", "deadline", "qualityStandards", "terms", "advancePayment", "finalPayment", "deliveryTerms", "qualityCheck", "penalty<PERSON><PERSON><PERSON>", "showForm", "setShowForm", "handleInputChange", "section", "field", "value", "prev", "calculateTotalAmount", "parseFloat", "total", "toFixed", "generatePDF", "doc", "pageWidth", "internal", "pageSize", "width", "margin", "yPosition", "setFontSize", "setFont", "text", "align", "Date", "toLocaleDateString", "advance", "percent", "for<PERSON>ach", "term", "max<PERSON><PERSON><PERSON>", "fileName", "replace", "toISOString", "split", "save", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/components/dashboard/AgreementPDF.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport jsPDF from 'jspdf';\nimport './AgreementPDF.css';\n\nfunction AgreementPDF() {\n\n  const [agreementData, setAgreementData] = useState({\n    teamLeader: {\n      name: '<PERSON><PERSON>',\n      village: 'Kumargram Village',\n      clusterId: 'TL-001',\n      phone: '+91-9876543212',\n      email: '<EMAIL>'\n    },\n    artisan: {\n      name: '',\n      phone: '',\n      address: '',\n      experience: ''\n    },\n    order: {\n      orderId: '',\n      product: 'Handwoven Sarees',\n      quantity: '',\n      pricePerUnit: '',\n      totalAmount: '',\n      deadline: '',\n      qualityStandards: 'Premium handloom quality with traditional patterns'\n    },\n    terms: {\n      advancePayment: '',\n      finalPayment: '',\n      deliveryTerms: 'Delivery to be made at the designated collection center',\n      qualityCheck: 'All products subject to quality inspection before final payment',\n      penaltyClause: '2% deduction for each day of delay beyond agreed deadline'\n    }\n  });\n\n  const [showForm, setShowForm] = useState(false);\n\n  const handleInputChange = (section, field, value) => {\n    setAgreementData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n\n  const calculateTotalAmount = () => {\n    const quantity = parseFloat(agreementData.order.quantity) || 0;\n    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;\n    const total = quantity * pricePerUnit;\n    setAgreementData(prev => ({\n      ...prev,\n      order: {\n        ...prev.order,\n        totalAmount: total.toFixed(2)\n      }\n    }));\n  };\n\n  const generatePDF = () => {\n    const doc = new jsPDF();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    let yPosition = 30;\n\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ARTISAN PRODUCTION AGREEMENT', pageWidth / 2, yPosition, { align: 'center' });\n\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Between Team Leader and Artisan', pageWidth / 2, yPosition, { align: 'center' });\n\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.text(`Agreement Date: ${new Date().toLocaleDateString()}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('TEAM LEADER DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Name: ${agreementData.teamLeader.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Village: ${agreementData.teamLeader.village}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Cluster ID: ${agreementData.teamLeader.clusterId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Phone: ${agreementData.teamLeader.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Email: ${agreementData.teamLeader.email}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ARTISAN DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Name: ${agreementData.artisan.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Phone: ${agreementData.artisan.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Address: ${agreementData.artisan.address}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Experience: ${agreementData.artisan.experience}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('ORDER DETAILS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Order ID: ${agreementData.order.orderId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Product: ${agreementData.order.product}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Quantity: ${agreementData.order.quantity} pieces`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Price per Unit: ₹${agreementData.order.pricePerUnit}`, margin, yPosition);\n    yPosition += 6;\n    doc.setFont('helvetica', 'bold');\n    doc.text(`Total Amount: ₹${agreementData.order.totalAmount}`, margin, yPosition);\n    doc.setFont('helvetica', 'normal');\n    yPosition += 6;\n    doc.text(`Delivery Deadline: ${agreementData.order.deadline}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Quality Standards: ${agreementData.order.qualityStandards}`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('PAYMENT TERMS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const advance = parseFloat(agreementData.terms.advancePayment);\n    const total = parseFloat(agreementData.order.totalAmount);\n    const percent = total ? ((advance / total) * 100).toFixed(1) : '0';\n    doc.text(`Advance Payment: ₹${agreementData.terms.advancePayment} (${percent}%)`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`Final Payment: ₹${agreementData.terms.finalPayment} (Upon delivery and quality approval)`, margin, yPosition);\n    yPosition += 15;\n\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('TERMS AND CONDITIONS:', margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const terms = [\n      `1. Delivery: ${agreementData.terms.deliveryTerms}`,\n      `2. Quality: ${agreementData.terms.qualityCheck}`,\n      `3. Penalty: ${agreementData.terms.penaltyClause}`,\n      `4. The artisan agrees to maintain the specified quality standards throughout production.`,\n      `5. Any defective products will be replaced at the artisan's cost.`,\n      `6. This agreement is valid for the specified order only.`,\n      `7. Both parties agree to resolve disputes through mutual discussion.`\n    ];\n    terms.forEach(term => {\n      doc.text(term, margin, yPosition, { maxWidth: pageWidth - 2 * margin });\n      yPosition += 8;\n    });\n    yPosition += 20;\n\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text('SIGNATURES:', margin, yPosition);\n    yPosition += 20;\n\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Team Leader:', margin, yPosition);\n    doc.text('Artisan:', pageWidth - margin - 60, yPosition);\n    yPosition += 15;\n\n    doc.text('_____________________', margin, yPosition);\n    doc.text('_____________________', pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);\n    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text('Date: _______________', margin, yPosition);\n    doc.text('Date: _______________', pageWidth - margin - 60, yPosition);\n\n    yPosition += 20;\n    doc.setFontSize(8);\n    doc.setFont('helvetica', 'italic');\n    doc.text('This agreement is generated electronically and is valid without physical signatures for record purposes.', pageWidth / 2, yPosition, { align: 'center' });\n\n    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  };\n\n  return (\n    <div>{/* UI rendering omitted for brevity */}</div>\n  );\n}\n\nexport default AgreementPDF;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EAEtB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGP,QAAQ,CAAC;IACjDQ,UAAU,EAAE;MACVC,IAAI,EAAE,qBAAqB;MAC3BC,OAAO,EAAE,mBAAmB;MAC5BC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPL,IAAI,EAAE,EAAE;MACRG,KAAK,EAAE,EAAE;MACTG,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC;IACDC,KAAK,EAAE;MACLC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,gBAAgB,EAAE;IACpB,CAAC;IACDC,KAAK,EAAE;MACLC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,yDAAyD;MACxEC,YAAY,EAAE,iEAAiE;MAC/EC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMiC,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACnD7B,gBAAgB,CAAC8B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACH,OAAO,GAAG;QACT,GAAGG,IAAI,CAACH,OAAO,CAAC;QAChB,CAACC,KAAK,GAAGC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMlB,QAAQ,GAAGmB,UAAU,CAACjC,aAAa,CAACW,KAAK,CAACG,QAAQ,CAAC,IAAI,CAAC;IAC9D,MAAMC,YAAY,GAAGkB,UAAU,CAACjC,aAAa,CAACW,KAAK,CAACI,YAAY,CAAC,IAAI,CAAC;IACtE,MAAMmB,KAAK,GAAGpB,QAAQ,GAAGC,YAAY;IACrCd,gBAAgB,CAAC8B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPpB,KAAK,EAAE;QACL,GAAGoB,IAAI,CAACpB,KAAK;QACbK,WAAW,EAAEkB,KAAK,CAACC,OAAO,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,GAAG,GAAG,IAAI1C,KAAK,CAAC,CAAC;IACvB,MAAM2C,SAAS,GAAGD,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACC,KAAK;IAC7C,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,EAAE;IAElBN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,8BAA8B,EAAER,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAEvFJ,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,iCAAiC,EAAER,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAE1FJ,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACS,IAAI,CAAC,mBAAmB,IAAIE,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAEP,MAAM,EAAEC,SAAS,CAAC;IACjFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,sBAAsB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACnDA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,SAAS9C,aAAa,CAACE,UAAU,CAACC,IAAI,EAAE,EAAEuC,MAAM,EAAEC,SAAS,CAAC;IACrEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,YAAY9C,aAAa,CAACE,UAAU,CAACE,OAAO,EAAE,EAAEsC,MAAM,EAAEC,SAAS,CAAC;IAC3EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,eAAe9C,aAAa,CAACE,UAAU,CAACG,SAAS,EAAE,EAAEqC,MAAM,EAAEC,SAAS,CAAC;IAChFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,UAAU9C,aAAa,CAACE,UAAU,CAACI,KAAK,EAAE,EAAEoC,MAAM,EAAEC,SAAS,CAAC;IACvEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,UAAU9C,aAAa,CAACE,UAAU,CAACK,KAAK,EAAE,EAAEmC,MAAM,EAAEC,SAAS,CAAC;IACvEA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,kBAAkB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC/CA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,SAAS9C,aAAa,CAACQ,OAAO,CAACL,IAAI,EAAE,EAAEuC,MAAM,EAAEC,SAAS,CAAC;IAClEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,UAAU9C,aAAa,CAACQ,OAAO,CAACF,KAAK,EAAE,EAAEoC,MAAM,EAAEC,SAAS,CAAC;IACpEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,YAAY9C,aAAa,CAACQ,OAAO,CAACC,OAAO,EAAE,EAAEiC,MAAM,EAAEC,SAAS,CAAC;IACxEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,eAAe9C,aAAa,CAACQ,OAAO,CAACE,UAAU,EAAE,EAAEgC,MAAM,EAAEC,SAAS,CAAC;IAC9EA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,gBAAgB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC7CA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,aAAa9C,aAAa,CAACW,KAAK,CAACC,OAAO,EAAE,EAAE8B,MAAM,EAAEC,SAAS,CAAC;IACvEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,YAAY9C,aAAa,CAACW,KAAK,CAACE,OAAO,EAAE,EAAE6B,MAAM,EAAEC,SAAS,CAAC;IACtEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,aAAa9C,aAAa,CAACW,KAAK,CAACG,QAAQ,SAAS,EAAE4B,MAAM,EAAEC,SAAS,CAAC;IAC/EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,oBAAoB9C,aAAa,CAACW,KAAK,CAACI,YAAY,EAAE,EAAE2B,MAAM,EAAEC,SAAS,CAAC;IACnFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,kBAAkB9C,aAAa,CAACW,KAAK,CAACK,WAAW,EAAE,EAAE0B,MAAM,EAAEC,SAAS,CAAC;IAChFN,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCF,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,sBAAsB9C,aAAa,CAACW,KAAK,CAACM,QAAQ,EAAE,EAAEyB,MAAM,EAAEC,SAAS,CAAC;IACjFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,sBAAsB9C,aAAa,CAACW,KAAK,CAACO,gBAAgB,EAAE,EAAEwB,MAAM,EAAEC,SAAS,CAAC;IACzFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,gBAAgB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC7CA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClC,MAAMK,OAAO,GAAGjB,UAAU,CAACjC,aAAa,CAACmB,KAAK,CAACC,cAAc,CAAC;IAC9D,MAAMc,KAAK,GAAGD,UAAU,CAACjC,aAAa,CAACW,KAAK,CAACK,WAAW,CAAC;IACzD,MAAMmC,OAAO,GAAGjB,KAAK,GAAG,CAAEgB,OAAO,GAAGhB,KAAK,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAClEE,GAAG,CAACS,IAAI,CAAC,qBAAqB9C,aAAa,CAACmB,KAAK,CAACC,cAAc,KAAK+B,OAAO,IAAI,EAAET,MAAM,EAAEC,SAAS,CAAC;IACpGA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,mBAAmB9C,aAAa,CAACmB,KAAK,CAACE,YAAY,uCAAuC,EAAEqB,MAAM,EAAEC,SAAS,CAAC;IACvHA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACpDA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClC,MAAM1B,KAAK,GAAG,CACZ,gBAAgBnB,aAAa,CAACmB,KAAK,CAACG,aAAa,EAAE,EACnD,eAAetB,aAAa,CAACmB,KAAK,CAACI,YAAY,EAAE,EACjD,eAAevB,aAAa,CAACmB,KAAK,CAACK,aAAa,EAAE,EAClD,0FAA0F,EAC1F,mEAAmE,EACnE,0DAA0D,EAC1D,sEAAsE,CACvE;IACDL,KAAK,CAACiC,OAAO,CAACC,IAAI,IAAI;MACpBhB,GAAG,CAACS,IAAI,CAACO,IAAI,EAAEX,MAAM,EAAEC,SAAS,EAAE;QAAEW,QAAQ,EAAEhB,SAAS,GAAG,CAAC,GAAGI;MAAO,CAAC,CAAC;MACvEC,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACS,IAAI,CAAC,aAAa,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC1CA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACO,WAAW,CAAC,EAAE,CAAC;IACnBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,cAAc,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IAC3CN,GAAG,CAACS,IAAI,CAAC,UAAU,EAAER,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IACxDA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACpDN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAER,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IACrEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,GAAG9C,aAAa,CAACE,UAAU,CAACC,IAAI,EAAE,EAAEuC,MAAM,EAAEC,SAAS,CAAC;IAC/DN,GAAG,CAACS,IAAI,CAAC,GAAG9C,aAAa,CAACQ,OAAO,CAACL,IAAI,EAAE,EAAEmC,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAC7EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAEJ,MAAM,EAAEC,SAAS,CAAC;IACpDN,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAER,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAErEA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACO,WAAW,CAAC,CAAC,CAAC;IAClBP,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACS,IAAI,CAAC,0GAA0G,EAAER,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAEnK,MAAMQ,QAAQ,GAAG,aAAavD,aAAa,CAACW,KAAK,CAACC,OAAO,IAAIZ,aAAa,CAACQ,OAAO,CAACL,IAAI,CAACqD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC5JrB,GAAG,CAACsB,IAAI,CAACJ,QAAQ,CAAC;EACpB,CAAC;EAED,oBACE1D,OAAA;IAAA0D,QAAA,EAAAK,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAkD,CAAC;AAEvD;AAAC/D,EAAA,CAzMQD,YAAY;AAAAiE,EAAA,GAAZjE,YAAY;AA2MrB,eAAeA,YAAY;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}