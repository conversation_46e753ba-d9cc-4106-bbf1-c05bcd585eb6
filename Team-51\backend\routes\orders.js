const express = require('express');
const jwt = require('jsonwebtoken');
const Order = require('../models/Order');
const TeamLeader = require('../models/TeamLeader');
const User = require('../models/User');
const router = express.Router();

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ msg: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, 's0meSup3r$tr0ng!Key2025');
    const user = await User.findById(decoded.id);
    // Remove verified check
    if (!user) {
      return res.status(403).json({ msg: 'User not found' });
    }
    // Add username to req.user for team leader identification
    req.user = {
      ...user.toObject(),
      username: decoded.username || user.username
    };
    next();
  } catch (err) {
    return res.status(403).json({ msg: 'Invalid token' });
  }
};

// Test endpoint
router.get('/test', (req, res) => {
  res.json({ msg: 'Orders API is working' });
});

// Get all orders
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('Fetching orders...');
    const orders = await Order.find().populate('buyer').sort({ createdAt: -1 });
    console.log('Orders found:', orders.length);
    
    // Populate team leader information for each order
    const ordersWithTeamLeaders = await Promise.all(
      orders.map(async (order) => {
        const teamLeaders = await TeamLeader.find({ 
          tl_id: { $in: order.team_leads } 
        }).select('tl_id name village_name cluster_id');
        
        // Create a map of tl_id to team leader info
        const teamLeaderMap = {};
        teamLeaders.forEach(tl => {
          teamLeaderMap[tl.tl_id] = {
            name: tl.name,
            village_name: tl.village_name,
            cluster_id: tl.cluster_id
          };
        });
        
        // Add team leader details to the order
        const orderWithTeamLeaders = order.toObject();
        orderWithTeamLeaders.team_leaders_details = order.team_leads.map(tl_id => ({
          tl_id,
          ...teamLeaderMap[tl_id]
        }));
        
        return orderWithTeamLeaders;
      })
    );
    
    res.json(ordersWithTeamLeaders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get available team leaders (not assigned to any order) with order counts
router.get('/available-team-leaders', authenticateToken, async (req, res) => {
  try {
    const availableTeamLeaders = await TeamLeader.find({
      $or: [
        { current_orderid: null },
        { current_orderid: { $exists: false } }
      ]
    }).select('tl_id name village_name cluster_id');
    
    // Get order counts for each available team leader
    const teamLeadersWithCounts = await Promise.all(
      availableTeamLeaders.map(async (tl) => {
        // Count current orders (to be done, in progress)
        const currentOrderCount = await Order.countDocuments({
          team_leads: tl.tl_id,
          status: { $in: ['to be done', 'in progress'] }
        });
        
        // Count total orders assigned to this team leader
        const totalOrderCount = await Order.countDocuments({
          team_leads: tl.tl_id
        });
        
        return {
          ...tl.toObject(),
          current_order_count: currentOrderCount,
          total_order_count: totalOrderCount
        };
      })
    );
    
    res.json(teamLeadersWithCounts);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Test endpoint for team leaders (no auth)
router.get('/team-leaders-test', async (req, res) => {
  try {
    const teamLeaders = await TeamLeader.find().select('tl_id name village_name cluster_id current_orderid');
    res.json(teamLeaders);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get all team leaders with their current order status and order counts
router.get('/team-leaders', authenticateToken, async (req, res) => {
  try {
    const teamLeaders = await TeamLeader.find().select('tl_id name village_name cluster_id current_orderid');
    
    // Get order counts for each team leader
    const teamLeadersWithCounts = await Promise.all(
      teamLeaders.map(async (tl) => {
        // Count current orders (to be done, in progress)
        const currentOrderCount = await Order.countDocuments({
          team_leads: tl.tl_id,
          status: { $in: ['to be done', 'in progress'] }
        });
        
        // Count total orders assigned to this team leader
        const totalOrderCount = await Order.countDocuments({
          team_leads: tl.tl_id
        });
        
        return {
          ...tl.toObject(),
          current_order_count: currentOrderCount,
          total_order_count: totalOrderCount
        };
      })
    );
    
    res.json(teamLeadersWithCounts);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get orders for a specific team leader
router.get('/team-leader/:tl_id', authenticateToken, async (req, res) => {
  try {
    const { tl_id } = req.params;
    
    // Find orders where this team leader is assigned
    const orders = await Order.find({ team_leads: tl_id }).sort({ createdAt: -1 });
    
    // Separate current and past orders based on status
    const currentOrders = orders.filter(order => 
      ['to be done', 'in progress'].includes(order.status)
    );
    
    const pastOrders = orders.filter(order => 
      ['completed', 'delayed'].includes(order.status)
    );
    
    res.json({
      current: currentOrders,
      past: pastOrders,
      total: orders.length
    });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Create new order
router.post('/', authenticateToken, async (req, res) => {
  try {
    console.log('Create order request received:', req.body);
    console.log('User making request:', req.user.username);
    
    const {
      order_id,
      product_id,
      product_name,
      buyer_name,
      team_leads,
      quantities,
      date_ordered,
      deadline,
      status,
      remark
    } = req.body;

    // Validate required fields
    console.log('Validating required fields...');
    if (!order_id || !product_id || !product_name || !buyer_name || !team_leads || !quantities || !date_ordered || !deadline) {
      console.log('Missing fields:', { order_id, product_id, product_name, buyer_name, team_leads, quantities, date_ordered, deadline });
      return res.status(400).json({ msg: 'Missing required fields' });
    }

    // Validate arrays have same length
    console.log('Validating arrays:', { team_leads, quantities });
    if (team_leads.length !== quantities.length) {
      console.log('Array length mismatch:', { team_leads_length: team_leads.length, quantities_length: quantities.length });
      return res.status(400).json({ msg: 'Team leads and quantities arrays must have the same length' });
    }

    // Validate quantities are positive
    console.log('Validating quantities:', quantities);
    if (!quantities.every(qty => qty > 0)) {
      console.log('Invalid quantities found:', quantities);
      return res.status(400).json({ msg: 'All quantities must be positive' });
    }

    // Check if order_id already exists
    console.log('Checking if order_id exists:', order_id);
    const existingOrder = await Order.findOne({ order_id });
    if (existingOrder) {
      console.log('Order ID already exists:', order_id);
      return res.status(400).json({ msg: 'Order ID already exists' });
    }

    // Validate that all team leaders exist in the database
    console.log('Validating team leaders:', team_leads);
    const teamLeaders = await TeamLeader.find({ tl_id: { $in: team_leads } });
    console.log('Found team leaders:', teamLeaders.map(tl => tl.tl_id));
    if (teamLeaders.length !== team_leads.length) {
      const foundIds = teamLeaders.map(tl => tl.tl_id);
      const missingIds = team_leads.filter(id => !foundIds.includes(id));
      console.log('Missing team leaders:', missingIds);
      return res.status(400).json({ 
        msg: `Team leaders not found: ${missingIds.join(', ')}` 
      });
    }

    // Check if any team leader is already assigned to another order
    console.log('Checking if team leaders are busy...');
    const busyTeamLeaders = teamLeaders.filter(tl => tl.current_orderid && tl.current_orderid !== order_id);
    if (busyTeamLeaders.length > 0) {
      const busyIds = busyTeamLeaders.map(tl => tl.tl_id);
      console.log('Busy team leaders:', busyIds);
      return res.status(400).json({ 
        msg: `Team leaders already assigned to other orders: ${busyIds.join(', ')}` 
      });
    }

    // Calculate total quantity
    const total_qty = quantities.reduce((sum, qty) => sum + qty, 0);
    console.log('Calculated total quantity:', total_qty);

    // Create new order
    console.log('Creating new order with data:', {
      order_id,
      product_id,
      product_name,
      buyer_name,
      team_leads,
      quantities,
      total_qty,
      date_ordered: new Date(date_ordered),
      deadline: new Date(deadline),
      status: status || 'InProgress',
      remark
    });
    
    // Normalize status to match enum values
    let normalizedStatus = status;
    if (status === 'InProgress') normalizedStatus = 'in progress';
    if (status === 'ToBeDone') normalizedStatus = 'to be done';
    
    const newOrder = new Order({
      order_id,
      product_id,
      product_name,
      buyer_name,
      team_leads,
      quantities,
      total_qty,
      date_ordered: new Date(date_ordered),
      deadline: new Date(deadline),
      status: normalizedStatus || 'to be done',
      remark
    });

    console.log('Saving order to database...');
    await newOrder.save();
    console.log('Order saved successfully:', newOrder._id);

    // Update team leaders with current_orderid
    console.log('Updating team leaders with current_orderid:', order_id);
    await TeamLeader.updateMany(
      { tl_id: { $in: team_leads } },
      { current_orderid: order_id }
    );
    console.log('Team leaders updated successfully');

    console.log('Order creation completed successfully');
    res.status(201).json({ 
      msg: 'Order created successfully', 
      order: newOrder 
    });
  } catch (error) {
    console.error('Error creating order:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get order by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id).populate('buyer');
    if (!order) {
      return res.status(404).json({ msg: 'Order not found' });
    }
    res.json(order);
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Update order
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const {
      product_id,
      team_leads,
      quantities,
      deadline,
      status,
      remark
    } = req.body;

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({ msg: 'Order not found' });
    }

    // Update fields if provided
    if (product_id) order.product_id = product_id;
    if (team_leads) order.team_leads = team_leads;
    if (quantities) order.quantities = quantities;
    if (deadline) order.deadline = new Date(deadline);
    if (status) order.status = status;
    if (remark !== undefined) order.remark = remark;

    // Recalculate total_qty if quantities changed
    if (quantities) {
      order.total_qty = quantities.reduce((sum, qty) => sum + qty, 0);
    }

    await order.save();
    res.json({ msg: 'Order updated successfully', order });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Update order progress by team leader
router.put('/:id/progress', authenticateToken, async (req, res) => {
  try {
    const { status, fault_products, remarks } = req.body;
    const { id } = req.params;
    const teamLeaderId = req.user.username; // Get team leader ID from username in token

    console.log('Progress update request:', {
      orderId: id,
      teamLeaderId: teamLeaderId,
      status: status,
      fault_products: fault_products,
      remarks: remarks
    });

    const order = await Order.findById(id);
    if (!order) {
      console.log('Order not found:', id);
      return res.status(404).json({ msg: 'Order not found' });
    }

    console.log('Found order:', {
      orderId: order.order_id,
      teamLeads: order.team_leads,
      currentStatus: order.status
    });

    // Check if the team leader is assigned to this order
    if (!order.team_leads.includes(teamLeaderId)) {
      console.log('Team leader not assigned to order:', {
        teamLeaderId: teamLeaderId,
        orderTeamLeads: order.team_leads
      });
      return res.status(403).json({ msg: 'You are not assigned to this order' });
    }

    // Update order status
    order.status = status;
    
    // Add progress update to order
    if (!order.progress_updates) {
      order.progress_updates = [];
    }
    
    order.progress_updates.push({
      team_leader: teamLeaderId,
      status: status,
      fault_products: fault_products || 0,
      remarks: remarks || '',
      updated_at: new Date()
    });

    await order.save();
    console.log('Order progress updated successfully');
    res.json({ msg: 'Order progress updated successfully', order });
  } catch (error) {
    console.error('Error updating order progress:', error);
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Delete order
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const order = await Order.findByIdAndDelete(req.params.id);
    if (!order) {
      return res.status(404).json({ msg: 'Order not found' });
    }
    res.json({ msg: 'Order deleted successfully' });
  } catch (error) {
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Assign artisans to order
router.post('/:id/assign-artisans', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { team_leader_id, artisan_assignments } = req.body;
    const requestingUser = req.user.username;

    console.log('Artisan assignment request:', {
      orderId: id,
      teamLeaderId: team_leader_id,
      requestingUser: requestingUser,
      assignments: artisan_assignments
    });

    // Check if the requesting user is the team leader or has admin privileges
    if (req.user.type !== 'admin' && req.user.type !== 'admin-pan' && requestingUser !== team_leader_id) {
      return res.status(403).json({ msg: 'Access denied' });
    }

    const order = await Order.findById(id);
    if (!order) {
      return res.status(404).json({ msg: 'Order not found' });
    }

    // Check if the team leader is assigned to this order
    if (!order.team_leads.includes(team_leader_id)) {
      return res.status(403).json({ msg: 'Team leader is not assigned to this order' });
    }

    // Validate artisan assignments
    if (!artisan_assignments || !Array.isArray(artisan_assignments)) {
      return res.status(400).json({ msg: 'Invalid artisan assignments' });
    }

    // Calculate total assigned quantity
    const totalAssignedQuantity = artisan_assignments.reduce((sum, assignment) => {
      return sum + assignment.assigned_quantity;
    }, 0);

    // Find the team leader's assigned quantity for this order
    const teamLeadIndex = order.team_leads.indexOf(team_leader_id);
    const teamLeadQuantity = order.quantities[teamLeadIndex];

    if (totalAssignedQuantity > teamLeadQuantity) {
      return res.status(400).json({ 
        msg: `Total assigned quantity (${totalAssignedQuantity}) exceeds team leader's quantity (${teamLeadQuantity})` 
      });
    }

    // Initialize artisan_assignments array if it doesn't exist
    if (!order.artisan_assignments) {
      order.artisan_assignments = [];
    }

    // Find existing assignment for this team leader or create new one
    let teamLeaderAssignment = order.artisan_assignments.find(
      assignment => assignment.team_leader_id === team_leader_id
    );

    if (!teamLeaderAssignment) {
      teamLeaderAssignment = {
        team_leader_id: team_leader_id,
        artisans: []
      };
      order.artisan_assignments.push(teamLeaderAssignment);
    }

    // Update artisan assignments
    teamLeaderAssignment.artisans = artisan_assignments.map(assignment => ({
      artisan_id: assignment.artisan_id,
      artisan_name: assignment.artisan_name,
      assigned_quantity: assignment.assigned_quantity,
      completed_quantity: 0,
      status: 'assigned',
      assigned_at: new Date()
    }));

    await order.save();
    console.log('Artisan assignments updated successfully');
    res.json({ 
      msg: 'Artisan assignments updated successfully', 
      order: order 
    });
  } catch (error) {
    console.error('Error assigning artisans:', error);
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Get artisan assignments for an order
router.get('/:id/artisan-assignments/:team_leader_id', authenticateToken, async (req, res) => {
  try {
    const { id, team_leader_id } = req.params;
    const requestingUser = req.user.username;

    // Check if the requesting user is the team leader or has admin privileges
    if (req.user.type !== 'admin' && req.user.type !== 'admin-pan' && requestingUser !== team_leader_id) {
      return res.status(403).json({ msg: 'Access denied' });
    }

    const order = await Order.findById(id);
    if (!order) {
      return res.status(404).json({ msg: 'Order not found' });
    }

    // Check if the team leader is assigned to this order
    if (!order.team_leads.includes(team_leader_id)) {
      return res.status(403).json({ msg: 'Team leader is not assigned to this order' });
    }

    // Find artisan assignments for this team leader
    const teamLeaderAssignment = order.artisan_assignments?.find(
      assignment => assignment.team_leader_id === team_leader_id
    );

    res.json({ 
      team_leader_id: team_leader_id,
      assignments: teamLeaderAssignment ? teamLeaderAssignment.artisans : []
    });
  } catch (error) {
    console.error('Error fetching artisan assignments:', error);
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

// Update artisan progress
router.put('/:id/artisan-progress', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { team_leader_id, artisan_id, completed_quantity, status } = req.body;
    const requestingUser = req.user.username;

    // Check if the requesting user is the team leader or has admin privileges
    if (req.user.type !== 'admin' && req.user.type !== 'admin-pan' && requestingUser !== team_leader_id) {
      return res.status(403).json({ msg: 'Access denied' });
    }

    const order = await Order.findById(id);
    if (!order) {
      return res.status(404).json({ msg: 'Order not found' });
    }

    // Check if the team leader is assigned to this order
    if (!order.team_leads.includes(team_leader_id)) {
      return res.status(403).json({ msg: 'Team leader is not assigned to this order' });
    }

    // Find artisan assignment
    const teamLeaderAssignment = order.artisan_assignments?.find(
      assignment => assignment.team_leader_id === team_leader_id
    );

    if (!teamLeaderAssignment) {
      return res.status(404).json({ msg: 'No artisan assignments found for this team leader' });
    }

    const artisanAssignment = teamLeaderAssignment.artisans.find(
      artisan => artisan.artisan_id === artisan_id
    );

    if (!artisanAssignment) {
      return res.status(404).json({ msg: 'Artisan assignment not found' });
    }

    // Update artisan progress
    if (completed_quantity !== undefined) {
      artisanAssignment.completed_quantity = completed_quantity;
    }
    if (status) {
      artisanAssignment.status = status;
    }

    await order.save();
    console.log('Artisan progress updated successfully');
    res.json({ 
      msg: 'Artisan progress updated successfully', 
      artisan_assignment: artisanAssignment 
    });
  } catch (error) {
    console.error('Error updating artisan progress:', error);
    res.status(500).json({ msg: 'Server error', error: error.message });
  }
});

module.exports = router; 