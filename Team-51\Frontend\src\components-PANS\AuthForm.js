import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { GoogleLogin } from '@react-oauth/google';
import { jwtDecode } from 'jwt-decode';
import './AuthForm.css';

function AuthForm({ isOpen, onClose, initialMode }) {
  const [isSignup, setIsSignup] = useState(initialMode === 'signup');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    setIsSignup(initialMode === 'signup');
    setError('');
    setSuccess('');
    setUsername('');
    setPassword('');
  }, [isOpen, initialMode]);

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    const url = isSignup
      ? 'http://localhost:5000/api/auth/signup'
      : 'http://localhost:5000/api/auth/signin';
    try {
      const response = await axios.post(url, { username, password });
      if (isSignup) {
        setSuccess('Signup successful! Now sign in.');
        setIsSignup(false);
      } else {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('username', username);
        window.location.reload();
      }
      setUsername('');
      setPassword('');
    } catch (err) {
      setError(err.response?.data?.msg || 'Something went wrong');
    }
  };

  return (
    <div className="auth-modal-overlay">
      <div className="auth-modal-card">
        <button className="auth-modal-close" onClick={onClose}>&times;</button>
        <h2 className="auth-title">{isSignup ? 'Create Account' : 'Sign In'}</h2>
        {error && <div className="auth-alert error">{error}</div>}
        {success && <div className="auth-alert success">{success}</div>}
        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-input-group">
            <span className="auth-input-icon">👤</span>
            <input
              type="text"
              placeholder="Username"
              className="auth-input"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              autoFocus
            />
          </div>
          <div className="auth-input-group">
            <span className="auth-input-icon">🔒</span>
            <input
              type="password"
              placeholder="Password"
              className="auth-input"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          <button className="auth-btn primary" type="submit">
            {isSignup ? 'Sign Up' : 'Sign In'}
          </button>
        </form>
        <button
          className="auth-switch"
          onClick={() => { setIsSignup(!isSignup); setError(''); setSuccess(''); }}
        >
          {isSignup ? 'Already have an account? Sign In' : "Don’t have an account? Sign Up"}
        </button>
        <div className="auth-divider">
          <span>or</span>
        </div>
        <div className="auth-google">
          <GoogleLogin
            onSuccess={async (credentialResponse) => {
              const decoded = jwtDecode(credentialResponse.credential);
              try {
                const res = await axios.post('http://localhost:5000/api/auth/google-auth', {
                  email: decoded.email,
                  name: decoded.name,
                });
                localStorage.setItem('token', res.data.token);
                localStorage.setItem('username', res.data.username);
                window.location.reload();
              } catch (err) {
                setError('Google login failed');
              }
            }}
            onError={() => {
              setError('Google login failed');
            }}
            width="250"
          />
        </div>
      </div>
    </div>
  );
}

export default AuthForm;
