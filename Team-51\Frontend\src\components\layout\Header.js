import React, { useState, useEffect, useRef } from 'react';
import { userUtils } from '../../services/api';
import GoogleTranslate from '../common/GoogleTranslate';
import './Header.css';

function Header({ onSignIn }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const userInfo = userUtils.getUserInfo();
  const { username, userType, verified } = userInfo;
  const dropdownRef = useRef(null);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleUserDropdown = () => {
    setIsUserDropdownOpen(!isUserDropdownOpen);
  };

  const handleLogout = () => {
    userUtils.clearUserInfo();
    window.location.reload();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsUserDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="header">
      <div className="header-container">
        <div className="header-logo">
          <img 
            src="/logo192.jpg" 
            alt="PANS NGO Logo" 
            className="header-logo-img"
          />
          <div className="header-logo-text">
            <h1>PANS</h1>
            <span className="header-tagline">People's Awareness Network Society</span>
          </div>
        </div>

        <nav className={`header-nav ${isMenuOpen ? 'header-nav-open' : ''}`}>
          <ul className="header-nav-list">
            <li><a href="#home" className="header-nav-link">Home</a></li>
            <li><a href="#about" className="header-nav-link">About Us</a></li>
            <li><a href="#projects" className="header-nav-link">Projects</a></li>
            <li><a href="#impact" className="header-nav-link">Impact</a></li>
            <li><a href="#get-involved" className="header-nav-link">Get Involved</a></li>
            <li><a href="#contact" className="header-nav-link">Contact</a></li>
          </ul>
        </nav>

        <div className="header-actions">
          {/* Google Translate Widget */}
          <GoogleTranslate />

          {username ? (
            <div className="header-user-menu">
              <div className={`header-user-dropdown ${isUserDropdownOpen ? 'open' : ''}`} ref={dropdownRef}>
                <button 
                  className="header-user-trigger" 
                  onClick={toggleUserDropdown}
                >
                  <div className="header-user-info">
                    <span className="header-welcome">
                      Welcome, <strong>{username}</strong>
                    </span>
                    {userType && (
                      <span className="header-user-type">
                        {userType.charAt(0).toUpperCase() + userType.slice(1)}
                      </span>
                    )}
                    {!verified && (
                      <span className="header-verification-status">
                        ⚠️ Account Pending Verification
                      </span>
                    )}
                  </div>
                  <span className="header-dropdown-arrow">▼</span>
                </button>
                
                {isUserDropdownOpen && (
                  <div className="header-dropdown-menu">
                    <button className="header-dropdown-item" onClick={handleLogout}>
                      Logout
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="header-auth-buttons">
              <button className="header-btn header-btn-primary" onClick={onSignIn}>
                Sign In
              </button>
            </div>
          )}
        </div>

        <button className="header-menu-toggle" onClick={toggleMenu}>
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </header>
  );
}

export default Header;
