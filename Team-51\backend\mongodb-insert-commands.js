// MongoDB Insert Commands for Team Leader Authentication
// Run these in MongoDB Compass or MongoDB Shell

// 1. Insert Team Leader User (for authentication)
db.users.insertOne({
  "_id": ObjectId("507f1f77bcf86cd799439013"),
  "username": "<EMAIL>",
  "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password123
  "email": "<EMAIL>",
  "phone_number": "+91-9876543212",
  "type": "team leader",
  "user_id": "teamlead-tl001",
  "verified": true,
  "__v": 0
});

// 2. Insert Additional Team Leaders
db.users.insertMany([
  {
    "_id": ObjectId("507f1f77bcf86cd799439014"),
    "username": "<EMAIL>",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543213",
    "type": "team leader",
    "user_id": "teamlead-tl002",
    "verified": true,
    "__v": 0
  },
  {
    "_id": ObjectId("507f1f77bcf86cd799439015"),
    "username": "<EMAIL>",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543214",
    "type": "team leader",
    "user_id": "teamlead-tl003",
    "verified": true,
    "__v": 0
  }
]);

// 3. Insert Artisan Users
db.users.insertMany([
  {
    "_id": ObjectId("507f1f77bcf86cd799439016"),
    "username": "priya.sharma",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543215",
    "type": "artisan",
    "user_id": "artisan-art001",
    "verified": true,
    "__v": 0
  },
  {
    "_id": ObjectId("507f1f77bcf86cd799439017"),
    "username": "rajesh.kumar",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543216",
    "type": "artisan",
    "user_id": "artisan-art002",
    "verified": true,
    "__v": 0
  },
  {
    "_id": ObjectId("507f1f77bcf86cd799439018"),
    "username": "meera.patel",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543217",
    "type": "artisan",
    "user_id": "artisan-art003",
    "verified": true,
    "__v": 0
  },
  {
    "_id": ObjectId("507f1f77bcf86cd799439019"),
    "username": "amit.singh",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543218",
    "type": "artisan",
    "user_id": "artisan-art004",
    "verified": true,
    "__v": 0
  },
  {
    "_id": ObjectId("507f1f77bcf86cd799439020"),
    "username": "sunita.devi",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543219",
    "type": "artisan",
    "user_id": "artisan-art005",
    "verified": true,
    "__v": 0
  },
  {
    "_id": ObjectId("507f1f77bcf86cd799439021"),
    "username": "kavita.devi",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543220",
    "type": "artisan",
    "user_id": "artisan-art006",
    "verified": true,
    "__v": 0
  }
]);

// 4. Insert Admin Users
db.users.insertMany([
  {
    "_id": ObjectId("507f1f77bcf86cd799439011"),
    "username": "<EMAIL>",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543210",
    "type": "admin",
    "user_id": "admin-12345678",
    "verified": true,
    "__v": 0
  },
  {
    "_id": ObjectId("507f1f77bcf86cd799439012"),
    "username": "<EMAIL>",
    "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
    "email": "<EMAIL>",
    "phone_number": "+91-9876543211",
    "type": "admin-pan",
    "user_id": "admin-pan-87654321",
    "verified": true,
    "__v": 0
  }
]);

// 5. Verify the insertions
db.users.find({"type": "team leader"}).pretty();

// Expected output should show 3 team leaders:
// - <EMAIL> (teamlead-tl001)
// - <EMAIL> (teamlead-tl002)  
// - <EMAIL> (teamlead-tl003)
