import axios from 'axios';

// Base API URL
const API_BASE_URL = 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, clear auth data
      localStorage.removeItem('token');
      localStorage.removeItem('username');
      localStorage.removeItem('user_id');
      localStorage.removeItem('user_type');
      localStorage.removeItem('verified');
      window.location.reload();
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  signup: (userData) => api.post('/auth/signup', userData),
  signin: (credentials) => api.post('/auth/signin', credentials),
  googleAuth: (googleData) => api.post('/auth/google-auth', googleData),
  enroll: (enrollmentData) => api.post('/auth/enroll', enrollmentData),
};

// Orders API functions
export const ordersAPI = {
  getAll: () => api.get('/orders'),
  getById: (id) => api.get(`/orders/${id}`),
  create: (orderData) => api.post('/orders', orderData),
  update: (id, orderData) => api.put(`/orders/${id}`, orderData),
  delete: (id) => api.delete(`/orders/${id}`),
  assignArtisans: (orderId, assignmentData) => api.post(`/orders/${orderId}/assign-artisans`, assignmentData),
  getArtisanAssignments: (orderId, teamLeaderId) => api.get(`/orders/${orderId}/artisan-assignments/${teamLeaderId}`),
  updateArtisanProgress: (orderId, progressData) => api.put(`/orders/${orderId}/artisan-progress`, progressData),
};

// Artisans API functions
export const artisansAPI = {
  getAll: () => api.get('/artisans'),
  getByTeamLeader: (teamLeaderId) => api.get(`/artisans/team-leader/${teamLeaderId}`),
  getAvailable: (teamLeaderId) => api.get(`/artisans/available/${teamLeaderId}`),
  create: (artisanData) => api.post('/artisans', artisanData),
  update: (id, artisanData) => api.put(`/artisans/${id}`, artisanData),
  delete: (id) => api.delete(`/artisans/${id}`),
};

// User utility functions
export const userUtils = {
  isAuthenticated: () => {
    const token = localStorage.getItem('token');
    const username = localStorage.getItem('username');
    return !!(token && username);
  },
  
  getUserInfo: () => {
    return {
      username: localStorage.getItem('username'),
      userId: localStorage.getItem('user_id'),
      userType: localStorage.getItem('user_type'),
      verified: localStorage.getItem('verified') === 'true',
      token: localStorage.getItem('token'),
    };
  },
  
  setUserInfo: (userData) => {
    localStorage.setItem('token', userData.token);
    localStorage.setItem('username', userData.username);
    localStorage.setItem('user_id', userData.user_id);
    localStorage.setItem('user_type', userData.type);
    localStorage.setItem('verified', userData.verified);
  },
  
  clearUserInfo: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('username');
    localStorage.removeItem('user_id');
    localStorage.removeItem('user_type');
    localStorage.removeItem('verified');
  },
  
  canAccessDashboard: () => {
    const userInfo = userUtils.getUserInfo();
    return userInfo.verified && 
           ['team leader', 'admin-pan', 'admin'].includes(userInfo.userType);
  }
};

export default api;
