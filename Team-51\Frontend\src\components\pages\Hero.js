import React from 'react';
import './Hero.css';

function Hero() {
  return (
    <section className="hero" id="home">
      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Building a Better Tomorrow
              <span className="hero-highlight"> Together</span>
            </h1>
            <p className="hero-description">
              PANS NGO is dedicated to creating sustainable change in communities 
              through education, healthcare, and environmental initiatives. 
              Join us in our mission to empower lives and build a brighter future.
            </p>
            <div className="hero-stats">
              <div className="hero-stat">
                <h3>50,000+</h3>
                <p>Lives Impacted</p>
              </div>
              <div className="hero-stat">
                <h3>200+</h3>
                <p>Projects Completed</p>
              </div>
              <div className="hero-stat">
                <h3>15+</h3>
                <p>Years of Service</p>
              </div>
            </div>
            <div className="hero-buttons">
              <button className="hero-btn hero-btn-primary">
                Donate Now
              </button>
              <button className="hero-btn hero-btn-secondary">
                Learn More
              </button>
            </div>
          </div>
          <div className="hero-image">
            <img 
              src="https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?auto=format&fit=crop&w=800&q=80" 
              alt="PANS NGO community work"
              className="hero-img"
            />
            <div className="hero-image-overlay">
              <div className="hero-badge">
                <span className="hero-badge-text">Making a Difference</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="hero-wave">
        <svg viewBox="0 0 1200 120" xmlns="http://www.w3.org/2000/svg">
          <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" fill="#ffffff"></path>
        </svg>
      </div>
    </section>
  );
}

export default Hero;
