{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Team 51\\\\Team-51\\\\Frontend\\\\src\\\\components\\\\dashboard\\\\AgreementPDF.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport jsPDF from 'jspdf';\nimport './AgreementPDF.css';\n\n// Language translations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst translations = {\n  english: {\n    title: 'ARTISAN PRODUCTION AGREEMENT',\n    subtitle: 'Between Team Leader and Artisan',\n    agreementDate: 'Agreement Date:',\n    teamLeaderDetails: 'TEAM LEADER DETAILS:',\n    artisanDetails: 'ARTISAN DETAILS:',\n    orderDetails: 'ORDER DETAILS:',\n    paymentTerms: 'PAYMENT TERMS:',\n    termsConditions: 'TERMS AND CONDITIONS:',\n    signatures: 'SIGNATURES:',\n    teamLeader: 'Team Leader:',\n    artisan: 'Artisan:',\n    date: 'Date:',\n    name: 'Name:',\n    village: 'Village:',\n    clusterId: 'Cluster ID:',\n    phone: 'Phone:',\n    email: 'Email:',\n    address: 'Address:',\n    experience: 'Experience:',\n    orderId: 'Order ID:',\n    product: 'Product:',\n    quantity: 'Quantity:',\n    pricePerUnit: 'Price per Unit:',\n    totalAmount: 'Total Amount:',\n    deadline: 'Delivery Deadline:',\n    qualityStandards: 'Quality Standards:',\n    advancePayment: 'Advance Payment:',\n    finalPayment: 'Final Payment:',\n    pieces: 'pieces',\n    uponDelivery: '(Upon delivery and quality approval)',\n    footer: 'This agreement is generated electronically and is valid without physical signatures for record purposes.'\n  },\n  hindi: {\n    title: 'कारीगर उत्पादन समझौता',\n    subtitle: 'टीम लीडर और कारीगर के बीच',\n    agreementDate: 'समझौता दिनांक:',\n    teamLeaderDetails: 'टीम लीडर विवरण:',\n    artisanDetails: 'कारीगर विवरण:',\n    orderDetails: 'ऑर्डर विवरण:',\n    paymentTerms: 'भुगतान शर्तें:',\n    termsConditions: 'नियम और शर्तें:',\n    signatures: 'हस्ताक्षर:',\n    teamLeader: 'टीम लीडर:',\n    artisan: 'कारीगर:',\n    date: 'दिनांक:',\n    name: 'नाम:',\n    village: 'गांव:',\n    clusterId: 'क्लस्टर आईडी:',\n    phone: 'फोन:',\n    email: 'ईमेल:',\n    address: 'पता:',\n    experience: 'अनुभव:',\n    orderId: 'ऑर्डर आईडी:',\n    product: 'उत्पाद:',\n    quantity: 'मात्रा:',\n    pricePerUnit: 'प्रति यूनिट मूल्य:',\n    totalAmount: 'कुल राशि:',\n    deadline: 'डिलीवरी की अंतिम तिथि:',\n    qualityStandards: 'गुणवत्ता मानक:',\n    advancePayment: 'अग्रिम भुगतान:',\n    finalPayment: 'अंतिम भुगतान:',\n    pieces: 'टुकड़े',\n    uponDelivery: '(डिलीवरी और गुणवत्ता अनुमोदन पर)',\n    footer: 'यह समझौता इलेक्ट्रॉनिक रूप से तैयार किया गया है और रिकॉर्ड उद्देश्यों के लिए भौतिक हस्ताक्षर के बिना वैध है।'\n  },\n  tamil: {\n    title: 'கைவினைஞர் உற்பத்தி ஒப்பந்தம்',\n    subtitle: 'குழு தலைவர் மற்றும் கைவினைஞர் இடையே',\n    agreementDate: 'ஒப்பந்த தேதி:',\n    teamLeaderDetails: 'குழு தலைவர் விவரங்கள்:',\n    artisanDetails: 'கைவினைஞர் விவரங்கள்:',\n    orderDetails: 'ஆர்டர் விவரங்கள்:',\n    paymentTerms: 'பணம் செலுத்தும் நிபந்தனைகள்:',\n    termsConditions: 'விதிமுறைகள் மற்றும் நிபந்தனைகள்:',\n    signatures: 'கையொப்பங்கள்:',\n    teamLeader: 'குழு தலைவர்:',\n    artisan: 'கைவினைஞர்:',\n    date: 'தேதி:',\n    name: 'பெயர்:',\n    village: 'கிராமம்:',\n    clusterId: 'கிளஸ்டர் ஐடி:',\n    phone: 'தொலைபேசி:',\n    email: 'மின்னஞ்சல்:',\n    address: 'முகவரி:',\n    experience: 'அனுபவம்:',\n    orderId: 'ஆர்டர் ஐடி:',\n    product: 'தயாரிப்பு:',\n    quantity: 'அளவு:',\n    pricePerUnit: 'ஒரு யூனிட் விலை:',\n    totalAmount: 'மொத்த தொகை:',\n    deadline: 'டெலிவரி கடைசி தேதி:',\n    qualityStandards: 'தர தரநிலைகள்:',\n    advancePayment: 'முன்பணம்:',\n    finalPayment: 'இறுதி பணம்:',\n    pieces: 'துண்டுகள்',\n    uponDelivery: '(டெலிவரி மற்றும் தர ஒப்புதலின் மீது)',\n    footer: 'இந்த ஒப்பந்தம் மின்னணு முறையில் உருவாக்கப்பட்டது மற்றும் பதிவு நோக்கங்களுக்காக உடல் கையொப்பங்கள் இல்லாமல் செல்லுபடியாகும்.'\n  },\n  telugu: {\n    title: 'కళాకారుల ఉత్పादన ఒప్పందం',\n    subtitle: 'టీమ్ లీడర్ మరియు కళాకారుల మధ్య',\n    agreementDate: 'ఒప్పంద తేదీ:',\n    teamLeaderDetails: 'టీమ్ లీడర్ వివరాలు:',\n    artisanDetails: 'కళాకారుల వివరాలు:',\n    orderDetails: 'ఆర్డర్ వివరాలు:',\n    paymentTerms: 'చెల్లింపు నిబంధనలు:',\n    termsConditions: 'నిబంధనలు మరియు షరతులు:',\n    signatures: 'సంతకాలు:',\n    teamLeader: 'టీమ్ లీడర్:',\n    artisan: 'కళాకారుడు:',\n    date: 'తేదీ:',\n    name: 'పేరు:',\n    village: 'గ్రామం:',\n    clusterId: 'క్లస్టర్ ఐడి:',\n    phone: 'ఫోన్:',\n    email: 'ఇమెయిల్:',\n    address: 'చిరునామా:',\n    experience: 'అనుభవం:',\n    orderId: 'ఆర్డర్ ఐడి:',\n    product: 'ఉత్పత్తి:',\n    quantity: 'పరిమాణం:',\n    pricePerUnit: 'యూనిట్ ధర:',\n    totalAmount: 'మొత్తం మొత్తం:',\n    deadline: 'డెలివరీ గడువు:',\n    qualityStandards: 'నాణ్యత ప్రమాణాలు:',\n    advancePayment: 'అడ్వాన్స్ చెల్లింపు:',\n    finalPayment: 'చివరి చెల్లింపు:',\n    pieces: 'ముక్కలు',\n    uponDelivery: '(డెలివరీ మరియు నాణ్యత ఆమోదంపై)',\n    footer: 'ఈ ఒప్పందం ఎలక్ట్రానిక్‌గా రూపొందించబడింది మరియు రికార్డ్ ప్రయోజనాల కోసం భౌతిక సంతకాలు లేకుండా చెల్లుబాటు అవుతుంది.'\n  }\n};\nfunction AgreementPDF() {\n  _s();\n  const [selectedLanguage, setSelectedLanguage] = useState('english');\n  const [agreementData, setAgreementData] = useState({\n    teamLeader: {\n      name: 'Rajesh Kumar Sharma',\n      village: 'Kumargram Village',\n      clusterId: 'TL-001',\n      phone: '+91-9876543212',\n      email: '<EMAIL>'\n    },\n    artisan: {\n      name: '',\n      phone: '',\n      address: '',\n      experience: ''\n    },\n    order: {\n      orderId: '',\n      product: 'Handwoven Sarees',\n      quantity: '',\n      pricePerUnit: '',\n      totalAmount: '',\n      deadline: '',\n      qualityStandards: 'Premium handloom quality with traditional patterns'\n    },\n    terms: {\n      advancePayment: '',\n      finalPayment: '',\n      deliveryTerms: 'Delivery to be made at the designated collection center',\n      qualityCheck: 'All products subject to quality inspection before final payment',\n      penaltyClause: '2% deduction for each day of delay beyond agreed deadline'\n    }\n  });\n  const [showForm, setShowForm] = useState(false);\n  const handleInputChange = (section, field, value) => {\n    setAgreementData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n  const calculateTotalAmount = () => {\n    const quantity = parseFloat(agreementData.order.quantity) || 0;\n    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;\n    const total = quantity * pricePerUnit;\n    setAgreementData(prev => ({\n      ...prev,\n      order: {\n        ...prev.order,\n        totalAmount: total.toFixed(2)\n      }\n    }));\n  };\n  const generatePDF = () => {\n    const doc = new jsPDF();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    let yPosition = 30;\n\n    // Get current language translations\n    const t = translations[selectedLanguage];\n\n    // Set up Unicode font support for non-English languages\n    if (selectedLanguage !== 'english') {\n      // For non-English languages, we'll use a Unicode-compatible approach\n      // Note: In production, you would load custom fonts here\n      doc.setFont('helvetica'); // Fallback to helvetica for now\n    }\n\n    // Header\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.title, pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(t.subtitle, pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n    yPosition += 20;\n\n    // Agreement Date\n    doc.setFontSize(10);\n    doc.text(`${t.agreementDate} ${new Date().toLocaleDateString()}`, margin, yPosition);\n    yPosition += 15;\n\n    // Team Leader Details\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.teamLeaderDetails, margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.name} ${agreementData.teamLeader.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.village} ${agreementData.teamLeader.village}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.clusterId} ${agreementData.teamLeader.clusterId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.phone} ${agreementData.teamLeader.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.email} ${agreementData.teamLeader.email}`, margin, yPosition);\n    yPosition += 15;\n\n    // Artisan Details\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.artisanDetails, margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.name} ${agreementData.artisan.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.phone} ${agreementData.artisan.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.address} ${agreementData.artisan.address}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.experience} ${agreementData.artisan.experience}`, margin, yPosition);\n    yPosition += 15;\n\n    // Order Details\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.orderDetails, margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.orderId} ${agreementData.order.orderId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.product} ${agreementData.order.product}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.quantity} ${agreementData.order.quantity} ${t.pieces}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.pricePerUnit} ₹${agreementData.order.pricePerUnit}`, margin, yPosition);\n    yPosition += 6;\n    doc.setFont('helvetica', 'bold');\n    doc.text(`${t.totalAmount} ₹${agreementData.order.totalAmount}`, margin, yPosition);\n    doc.setFont('helvetica', 'normal');\n    yPosition += 6;\n    doc.text(`${t.deadline} ${agreementData.order.deadline}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.qualityStandards} ${agreementData.order.qualityStandards}`, margin, yPosition);\n    yPosition += 15;\n\n    // Payment Terms\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.paymentTerms, margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.advancePayment} ₹${agreementData.terms.advancePayment} (${(parseFloat(agreementData.terms.advancePayment) / parseFloat(agreementData.order.totalAmount) * 100).toFixed(1)}%)`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.finalPayment} ₹${agreementData.terms.finalPayment} ${t.uponDelivery}`, margin, yPosition);\n    yPosition += 15;\n\n    // Terms and Conditions\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.termsConditions, margin, yPosition);\n    yPosition += 10;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    const terms = [`1. Delivery: ${agreementData.terms.deliveryTerms}`, `2. Quality: ${agreementData.terms.qualityCheck}`, `3. Penalty: ${agreementData.terms.penaltyClause}`, '4. The artisan agrees to maintain the specified quality standards throughout production.', '5. Any defective products will be replaced at the artisan\\'s cost.', '6. This agreement is valid for the specified order only.', '7. Both parties agree to resolve disputes through mutual discussion.'];\n    terms.forEach(term => {\n      doc.text(term, margin, yPosition, {\n        maxWidth: pageWidth - 2 * margin\n      });\n      yPosition += 8;\n    });\n    yPosition += 20;\n\n    // Signatures\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.signatures, margin, yPosition);\n    yPosition += 20;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.teamLeader}`, margin, yPosition);\n    doc.text(`${t.artisan}`, pageWidth - margin - 60, yPosition);\n    yPosition += 15;\n    doc.text('_____________________', margin, yPosition);\n    doc.text('_____________________', pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);\n    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n    doc.text(`${t.date} _______________`, margin, yPosition);\n    doc.text(`${t.date} _______________`, pageWidth - margin - 60, yPosition);\n\n    // Footer\n    yPosition += 20;\n    doc.setFontSize(8);\n    doc.setFont('helvetica', 'italic');\n    doc.text(t.footer, pageWidth / 2, yPosition, {\n      align: 'center'\n    });\n\n    // Save the PDF\n    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"agreement-pdf-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"agreement-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Generate Artisan Agreement\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Create a formal agreement between Team Leader and Artisan with pricing details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"language-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Select Language / \\u092D\\u093E\\u0937\\u093E \\u091A\\u0941\\u0928\\u0947\\u0902 / \\u0BAE\\u0BCA\\u0BB4\\u0BBF\\u0BAF\\u0BC8\\u0BA4\\u0BCD \\u0BA4\\u0BC7\\u0BB0\\u0BCD\\u0BA8\\u0BCD\\u0BA4\\u0BC6\\u0B9F\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BB5\\u0BC1\\u0BAE\\u0BCD / \\u0C2D\\u0C3E\\u0C37\\u0C28\\u0C41 \\u0C0E\\u0C02\\u0C1A\\u0C41\\u0C15\\u0C4B\\u0C02\\u0C21\\u0C3F:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLanguage,\n          onChange: e => setSelectedLanguage(e.target.value),\n          className: \"language-dropdown\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"english\",\n            children: \"English\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"hindi\",\n            children: \"\\u0939\\u093F\\u0902\\u0926\\u0940 (Hindi)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"tamil\",\n            children: \"\\u0BA4\\u0BAE\\u0BBF\\u0BB4\\u0BCD (Tamil)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"telugu\",\n            children: \"\\u0C24\\u0C46\\u0C32\\u0C41\\u0C17\\u0C41 (Telugu)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"toggle-form-btn\",\n        onClick: () => setShowForm(!showForm),\n        children: showForm ? 'Hide Form' : 'Create New Agreement'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"agreement-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Artisan Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Artisan Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.artisan.name,\n              onChange: e => handleInputChange('artisan', 'name', e.target.value),\n              placeholder: \"Enter artisan's full name\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Phone Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              value: agreementData.artisan.phone,\n              onChange: e => handleInputChange('artisan', 'phone', e.target.value),\n              placeholder: \"+91-XXXXXXXXXX\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group full-width\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: agreementData.artisan.address,\n              onChange: e => handleInputChange('artisan', 'address', e.target.value),\n              placeholder: \"Enter complete address\",\n              rows: \"2\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Experience\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.artisan.experience,\n              onChange: e => handleInputChange('artisan', 'experience', e.target.value),\n              placeholder: \"e.g., 5 years in handloom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Order Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Order ID *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.order.orderId,\n              onChange: e => handleInputChange('order', 'orderId', e.target.value),\n              placeholder: \"e.g., ORD001\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Quantity *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: agreementData.order.quantity,\n              onChange: e => {\n                handleInputChange('order', 'quantity', e.target.value);\n                setTimeout(calculateTotalAmount, 100);\n              },\n              placeholder: \"Number of pieces\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Price per Unit (\\u20B9) *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              step: \"0.01\",\n              value: agreementData.order.pricePerUnit,\n              onChange: e => {\n                handleInputChange('order', 'pricePerUnit', e.target.value);\n                setTimeout(calculateTotalAmount, 100);\n              },\n              placeholder: \"Price per saree\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Total Amount (\\u20B9)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.order.totalAmount,\n              readOnly: true,\n              className: \"readonly-field\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Delivery Deadline *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: agreementData.order.deadline,\n              onChange: e => handleInputChange('order', 'deadline', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Payment Terms\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Advance Payment (\\u20B9) *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              step: \"0.01\",\n              value: agreementData.terms.advancePayment,\n              onChange: e => {\n                handleInputChange('terms', 'advancePayment', e.target.value);\n                const remaining = parseFloat(agreementData.order.totalAmount) - parseFloat(e.target.value);\n                handleInputChange('terms', 'finalPayment', remaining.toFixed(2));\n              },\n              placeholder: \"Advance amount\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Final Payment (\\u20B9)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: agreementData.terms.finalPayment,\n              readOnly: true,\n              className: \"readonly-field\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"cancel-btn\",\n          onClick: () => setShowForm(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"generate-btn\",\n          onClick: generatePDF,\n          disabled: !agreementData.artisan.name || !agreementData.order.orderId || !agreementData.order.quantity,\n          children: \"Generate PDF Agreement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sample-agreements\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Recent Agreements\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"agreements-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"agreement-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"agreement-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Agreement with Priya Sharma\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Order: ORD001 | 40 Handwoven Sarees | \\u20B932,000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"agreement-date\",\n              children: \"Created: 2024-01-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"download-btn\",\n            children: \"Download PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"agreement-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"agreement-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Agreement with Meera Patel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Order: ORD001 | 45 Handwoven Sarees | \\u20B936,000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"agreement-date\",\n              children: \"Created: 2024-01-08\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"download-btn\",\n            children: \"Download PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 368,\n    columnNumber: 5\n  }, this);\n}\n_s(AgreementPDF, \"NqUKyVDlbOKP34qMNso9FLI8zKI=\");\n_c = AgreementPDF;\nexport default AgreementPDF;\nvar _c;\n$RefreshReg$(_c, \"AgreementPDF\");", "map": {"version": 3, "names": ["React", "useState", "jsPDF", "jsxDEV", "_jsxDEV", "translations", "english", "title", "subtitle", "agreementDate", "teamLeaderDetails", "artisanDetails", "orderDetails", "paymentTerms", "termsConditions", "signatures", "<PERSON><PERSON><PERSON><PERSON>", "artisan", "date", "name", "village", "clusterId", "phone", "email", "address", "experience", "orderId", "product", "quantity", "pricePerUnit", "totalAmount", "deadline", "qualityStandards", "advancePayment", "finalPayment", "pieces", "uponDelivery", "footer", "hindi", "tamil", "telugu", "AgreementPDF", "_s", "selectedLanguage", "setSelectedLanguage", "agreementData", "setAgreementData", "order", "terms", "deliveryTerms", "qualityCheck", "penalty<PERSON><PERSON><PERSON>", "showForm", "setShowForm", "handleInputChange", "section", "field", "value", "prev", "calculateTotalAmount", "parseFloat", "total", "toFixed", "generatePDF", "doc", "pageWidth", "internal", "pageSize", "width", "margin", "yPosition", "t", "setFont", "setFontSize", "text", "align", "Date", "toLocaleDateString", "for<PERSON>ach", "term", "max<PERSON><PERSON><PERSON>", "fileName", "replace", "toISOString", "split", "save", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "onClick", "type", "placeholder", "required", "rows", "setTimeout", "step", "readOnly", "remaining", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Team 51/Team-51/Frontend/src/components/dashboard/AgreementPDF.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport jsPDF from 'jspdf';\nimport './AgreementPDF.css';\n\n// Language translations\nconst translations = {\n  english: {\n    title: 'ARTISAN PRODUCTION AGREEMENT',\n    subtitle: 'Between Team Leader and Artisan',\n    agreementDate: 'Agreement Date:',\n    teamLeaderDetails: 'TEAM LEADER DETAILS:',\n    artisanDetails: 'ARTISAN DETAILS:',\n    orderDetails: 'ORDER DETAILS:',\n    paymentTerms: 'PAYMENT TERMS:',\n    termsConditions: 'TERMS AND CONDITIONS:',\n    signatures: 'SIGNATURES:',\n    teamLeader: 'Team Leader:',\n    artisan: 'Artisan:',\n    date: 'Date:',\n    name: 'Name:',\n    village: 'Village:',\n    clusterId: 'Cluster ID:',\n    phone: 'Phone:',\n    email: 'Email:',\n    address: 'Address:',\n    experience: 'Experience:',\n    orderId: 'Order ID:',\n    product: 'Product:',\n    quantity: 'Quantity:',\n    pricePerUnit: 'Price per Unit:',\n    totalAmount: 'Total Amount:',\n    deadline: 'Delivery Deadline:',\n    qualityStandards: 'Quality Standards:',\n    advancePayment: 'Advance Payment:',\n    finalPayment: 'Final Payment:',\n    pieces: 'pieces',\n    uponDelivery: '(Upon delivery and quality approval)',\n    footer: 'This agreement is generated electronically and is valid without physical signatures for record purposes.'\n  },\n  hindi: {\n    title: 'कारीगर उत्पादन समझौता',\n    subtitle: 'टीम लीडर और कारीगर के बीच',\n    agreementDate: 'समझौता दिनांक:',\n    teamLeaderDetails: 'टीम लीडर विवरण:',\n    artisanDetails: 'कारीगर विवरण:',\n    orderDetails: 'ऑर्डर विवरण:',\n    paymentTerms: 'भुगतान शर्तें:',\n    termsConditions: 'नियम और शर्तें:',\n    signatures: 'हस्ताक्षर:',\n    teamLeader: 'टीम लीडर:',\n    artisan: 'कारीगर:',\n    date: 'दिनांक:',\n    name: 'नाम:',\n    village: 'गांव:',\n    clusterId: 'क्लस्टर आईडी:',\n    phone: 'फोन:',\n    email: 'ईमेल:',\n    address: 'पता:',\n    experience: 'अनुभव:',\n    orderId: 'ऑर्डर आईडी:',\n    product: 'उत्पाद:',\n    quantity: 'मात्रा:',\n    pricePerUnit: 'प्रति यूनिट मूल्य:',\n    totalAmount: 'कुल राशि:',\n    deadline: 'डिलीवरी की अंतिम तिथि:',\n    qualityStandards: 'गुणवत्ता मानक:',\n    advancePayment: 'अग्रिम भुगतान:',\n    finalPayment: 'अंतिम भुगतान:',\n    pieces: 'टुकड़े',\n    uponDelivery: '(डिलीवरी और गुणवत्ता अनुमोदन पर)',\n    footer: 'यह समझौता इलेक्ट्रॉनिक रूप से तैयार किया गया है और रिकॉर्ड उद्देश्यों के लिए भौतिक हस्ताक्षर के बिना वैध है।'\n  },\n  tamil: {\n    title: 'கைவினைஞர் உற்பத்தி ஒப்பந்தம்',\n    subtitle: 'குழு தலைவர் மற்றும் கைவினைஞர் இடையே',\n    agreementDate: 'ஒப்பந்த தேதி:',\n    teamLeaderDetails: 'குழு தலைவர் விவரங்கள்:',\n    artisanDetails: 'கைவினைஞர் விவரங்கள்:',\n    orderDetails: 'ஆர்டர் விவரங்கள்:',\n    paymentTerms: 'பணம் செலுத்தும் நிபந்தனைகள்:',\n    termsConditions: 'விதிமுறைகள் மற்றும் நிபந்தனைகள்:',\n    signatures: 'கையொப்பங்கள்:',\n    teamLeader: 'குழு தலைவர்:',\n    artisan: 'கைவினைஞர்:',\n    date: 'தேதி:',\n    name: 'பெயர்:',\n    village: 'கிராமம்:',\n    clusterId: 'கிளஸ்டர் ஐடி:',\n    phone: 'தொலைபேசி:',\n    email: 'மின்னஞ்சல்:',\n    address: 'முகவரி:',\n    experience: 'அனுபவம்:',\n    orderId: 'ஆர்டர் ஐடி:',\n    product: 'தயாரிப்பு:',\n    quantity: 'அளவு:',\n    pricePerUnit: 'ஒரு யூனிட் விலை:',\n    totalAmount: 'மொத்த தொகை:',\n    deadline: 'டெலிவரி கடைசி தேதி:',\n    qualityStandards: 'தர தரநிலைகள்:',\n    advancePayment: 'முன்பணம்:',\n    finalPayment: 'இறுதி பணம்:',\n    pieces: 'துண்டுகள்',\n    uponDelivery: '(டெலிவரி மற்றும் தர ஒப்புதலின் மீது)',\n    footer: 'இந்த ஒப்பந்தம் மின்னணு முறையில் உருவாக்கப்பட்டது மற்றும் பதிவு நோக்கங்களுக்காக உடல் கையொப்பங்கள் இல்லாமல் செல்லுபடியாகும்.'\n  },\n  telugu: {\n    title: 'కళాకారుల ఉత్పादన ఒప్పందం',\n    subtitle: 'టీమ్ లీడర్ మరియు కళాకారుల మధ్య',\n    agreementDate: 'ఒప్పంద తేదీ:',\n    teamLeaderDetails: 'టీమ్ లీడర్ వివరాలు:',\n    artisanDetails: 'కళాకారుల వివరాలు:',\n    orderDetails: 'ఆర్డర్ వివరాలు:',\n    paymentTerms: 'చెల్లింపు నిబంధనలు:',\n    termsConditions: 'నిబంధనలు మరియు షరతులు:',\n    signatures: 'సంతకాలు:',\n    teamLeader: 'టీమ్ లీడర్:',\n    artisan: 'కళాకారుడు:',\n    date: 'తేదీ:',\n    name: 'పేరు:',\n    village: 'గ్రామం:',\n    clusterId: 'క్లస్టర్ ఐడి:',\n    phone: 'ఫోన్:',\n    email: 'ఇమెయిల్:',\n    address: 'చిరునామా:',\n    experience: 'అనుభవం:',\n    orderId: 'ఆర్డర్ ఐడి:',\n    product: 'ఉత్పత్తి:',\n    quantity: 'పరిమాణం:',\n    pricePerUnit: 'యూనిట్ ధర:',\n    totalAmount: 'మొత్తం మొత్తం:',\n    deadline: 'డెలివరీ గడువు:',\n    qualityStandards: 'నాణ్యత ప్రమాణాలు:',\n    advancePayment: 'అడ్వాన్స్ చెల్లింపు:',\n    finalPayment: 'చివరి చెల్లింపు:',\n    pieces: 'ముక్కలు',\n    uponDelivery: '(డెలివరీ మరియు నాణ్యత ఆమోదంపై)',\n    footer: 'ఈ ఒప్పందం ఎలక్ట్రానిక్‌గా రూపొందించబడింది మరియు రికార్డ్ ప్రయోజనాల కోసం భౌతిక సంతకాలు లేకుండా చెల్లుబాటు అవుతుంది.'\n  }\n};\n\nfunction AgreementPDF() {\n  const [selectedLanguage, setSelectedLanguage] = useState('english');\n  const [agreementData, setAgreementData] = useState({\n    teamLeader: {\n      name: 'Rajesh Kumar Sharma',\n      village: 'Kumargram Village',\n      clusterId: 'TL-001',\n      phone: '+91-9876543212',\n      email: '<EMAIL>'\n    },\n    artisan: {\n      name: '',\n      phone: '',\n      address: '',\n      experience: ''\n    },\n    order: {\n      orderId: '',\n      product: 'Handwoven Sarees',\n      quantity: '',\n      pricePerUnit: '',\n      totalAmount: '',\n      deadline: '',\n      qualityStandards: 'Premium handloom quality with traditional patterns'\n    },\n    terms: {\n      advancePayment: '',\n      finalPayment: '',\n      deliveryTerms: 'Delivery to be made at the designated collection center',\n      qualityCheck: 'All products subject to quality inspection before final payment',\n      penaltyClause: '2% deduction for each day of delay beyond agreed deadline'\n    }\n  });\n\n  const [showForm, setShowForm] = useState(false);\n\n  const handleInputChange = (section, field, value) => {\n    setAgreementData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n\n  const calculateTotalAmount = () => {\n    const quantity = parseFloat(agreementData.order.quantity) || 0;\n    const pricePerUnit = parseFloat(agreementData.order.pricePerUnit) || 0;\n    const total = quantity * pricePerUnit;\n    setAgreementData(prev => ({\n      ...prev,\n      order: {\n        ...prev.order,\n        totalAmount: total.toFixed(2)\n      }\n    }));\n  };\n\n  const generatePDF = () => {\n    const doc = new jsPDF();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    let yPosition = 30;\n\n    // Get current language translations\n    const t = translations[selectedLanguage];\n\n    // Set up Unicode font support for non-English languages\n    if (selectedLanguage !== 'english') {\n      // For non-English languages, we'll use a Unicode-compatible approach\n      // Note: In production, you would load custom fonts here\n      doc.setFont('helvetica'); // Fallback to helvetica for now\n    }\n\n    // Header\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.title, pageWidth / 2, yPosition, { align: 'center' });\n\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(t.subtitle, pageWidth / 2, yPosition, { align: 'center' });\n\n    yPosition += 20;\n\n    // Agreement Date\n    doc.setFontSize(10);\n    doc.text(`${t.agreementDate} ${new Date().toLocaleDateString()}`, margin, yPosition);\n    yPosition += 15;\n\n    // Team Leader Details\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.teamLeaderDetails, margin, yPosition);\n    yPosition += 10;\n\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.name} ${agreementData.teamLeader.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.village} ${agreementData.teamLeader.village}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.clusterId} ${agreementData.teamLeader.clusterId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.phone} ${agreementData.teamLeader.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.email} ${agreementData.teamLeader.email}`, margin, yPosition);\n    yPosition += 15;\n\n    // Artisan Details\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.artisanDetails, margin, yPosition);\n    yPosition += 10;\n\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.name} ${agreementData.artisan.name}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.phone} ${agreementData.artisan.phone}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.address} ${agreementData.artisan.address}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.experience} ${agreementData.artisan.experience}`, margin, yPosition);\n    yPosition += 15;\n\n    // Order Details\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.orderDetails, margin, yPosition);\n    yPosition += 10;\n\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.orderId} ${agreementData.order.orderId}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.product} ${agreementData.order.product}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.quantity} ${agreementData.order.quantity} ${t.pieces}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.pricePerUnit} ₹${agreementData.order.pricePerUnit}`, margin, yPosition);\n    yPosition += 6;\n    doc.setFont('helvetica', 'bold');\n    doc.text(`${t.totalAmount} ₹${agreementData.order.totalAmount}`, margin, yPosition);\n    doc.setFont('helvetica', 'normal');\n    yPosition += 6;\n    doc.text(`${t.deadline} ${agreementData.order.deadline}`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.qualityStandards} ${agreementData.order.qualityStandards}`, margin, yPosition);\n    yPosition += 15;\n\n    // Payment Terms\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.paymentTerms, margin, yPosition);\n    yPosition += 10;\n\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.advancePayment} ₹${agreementData.terms.advancePayment} (${((parseFloat(agreementData.terms.advancePayment) / parseFloat(agreementData.order.totalAmount)) * 100).toFixed(1)}%)`, margin, yPosition);\n    yPosition += 6;\n    doc.text(`${t.finalPayment} ₹${agreementData.terms.finalPayment} ${t.uponDelivery}`, margin, yPosition);\n    yPosition += 15;\n\n    // Terms and Conditions\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.termsConditions, margin, yPosition);\n    yPosition += 10;\n    \n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    \n    const terms = [\n      `1. Delivery: ${agreementData.terms.deliveryTerms}`,\n      `2. Quality: ${agreementData.terms.qualityCheck}`,\n      `3. Penalty: ${agreementData.terms.penaltyClause}`,\n      '4. The artisan agrees to maintain the specified quality standards throughout production.',\n      '5. Any defective products will be replaced at the artisan\\'s cost.',\n      '6. This agreement is valid for the specified order only.',\n      '7. Both parties agree to resolve disputes through mutual discussion.'\n    ];\n\n    terms.forEach(term => {\n      doc.text(term, margin, yPosition, { maxWidth: pageWidth - 2 * margin });\n      yPosition += 8;\n    });\n\n    yPosition += 20;\n\n    // Signatures\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text(t.signatures, margin, yPosition);\n    yPosition += 20;\n\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`${t.teamLeader}`, margin, yPosition);\n    doc.text(`${t.artisan}`, pageWidth - margin - 60, yPosition);\n    yPosition += 15;\n\n    doc.text('_____________________', margin, yPosition);\n    doc.text('_____________________', pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n\n    doc.text(`${agreementData.teamLeader.name}`, margin, yPosition);\n    doc.text(`${agreementData.artisan.name}`, pageWidth - margin - 60, yPosition);\n    yPosition += 8;\n\n    doc.text(`${t.date} _______________`, margin, yPosition);\n    doc.text(`${t.date} _______________`, pageWidth - margin - 60, yPosition);\n\n    // Footer\n    yPosition += 20;\n    doc.setFontSize(8);\n    doc.setFont('helvetica', 'italic');\n    doc.text(t.footer, pageWidth / 2, yPosition, { align: 'center' });\n\n    // Save the PDF\n    const fileName = `Agreement_${agreementData.order.orderId}_${agreementData.artisan.name.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  };\n\n  return (\n    <div className=\"agreement-pdf-container\">\n      <div className=\"agreement-header\">\n        <h2>Generate Artisan Agreement</h2>\n        <p>Create a formal agreement between Team Leader and Artisan with pricing details</p>\n\n        {/* Language Selector */}\n        <div className=\"language-selector\">\n          <label>Select Language / भाषा चुनें / மொழியைத் தேர்ந்தெடுக்கவும் / భాషను ఎంచుకోండి:</label>\n          <select\n            value={selectedLanguage}\n            onChange={(e) => setSelectedLanguage(e.target.value)}\n            className=\"language-dropdown\"\n          >\n            <option value=\"english\">English</option>\n            <option value=\"hindi\">हिंदी (Hindi)</option>\n            <option value=\"tamil\">தமிழ் (Tamil)</option>\n            <option value=\"telugu\">తెలుగు (Telugu)</option>\n          </select>\n        </div>\n\n        <button\n          className=\"toggle-form-btn\"\n          onClick={() => setShowForm(!showForm)}\n        >\n          {showForm ? 'Hide Form' : 'Create New Agreement'}\n        </button>\n      </div>\n\n      {showForm && (\n        <div className=\"agreement-form\">\n          <div className=\"form-section\">\n            <h3>Artisan Information</h3>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Artisan Name *</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.artisan.name}\n                  onChange={(e) => handleInputChange('artisan', 'name', e.target.value)}\n                  placeholder=\"Enter artisan's full name\"\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Phone Number *</label>\n                <input\n                  type=\"tel\"\n                  value={agreementData.artisan.phone}\n                  onChange={(e) => handleInputChange('artisan', 'phone', e.target.value)}\n                  placeholder=\"+91-XXXXXXXXXX\"\n                  required\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group full-width\">\n                <label>Address *</label>\n                <textarea\n                  value={agreementData.artisan.address}\n                  onChange={(e) => handleInputChange('artisan', 'address', e.target.value)}\n                  placeholder=\"Enter complete address\"\n                  rows=\"2\"\n                  required\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Experience</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.artisan.experience}\n                  onChange={(e) => handleInputChange('artisan', 'experience', e.target.value)}\n                  placeholder=\"e.g., 5 years in handloom\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h3>Order Details</h3>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Order ID *</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.order.orderId}\n                  onChange={(e) => handleInputChange('order', 'orderId', e.target.value)}\n                  placeholder=\"e.g., ORD001\"\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Quantity *</label>\n                <input\n                  type=\"number\"\n                  value={agreementData.order.quantity}\n                  onChange={(e) => {\n                    handleInputChange('order', 'quantity', e.target.value);\n                    setTimeout(calculateTotalAmount, 100);\n                  }}\n                  placeholder=\"Number of pieces\"\n                  required\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Price per Unit (₹) *</label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={agreementData.order.pricePerUnit}\n                  onChange={(e) => {\n                    handleInputChange('order', 'pricePerUnit', e.target.value);\n                    setTimeout(calculateTotalAmount, 100);\n                  }}\n                  placeholder=\"Price per saree\"\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Total Amount (₹)</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.order.totalAmount}\n                  readOnly\n                  className=\"readonly-field\"\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Delivery Deadline *</label>\n                <input\n                  type=\"date\"\n                  value={agreementData.order.deadline}\n                  onChange={(e) => handleInputChange('order', 'deadline', e.target.value)}\n                  required\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h3>Payment Terms</h3>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Advance Payment (₹) *</label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={agreementData.terms.advancePayment}\n                  onChange={(e) => {\n                    handleInputChange('terms', 'advancePayment', e.target.value);\n                    const remaining = parseFloat(agreementData.order.totalAmount) - parseFloat(e.target.value);\n                    handleInputChange('terms', 'finalPayment', remaining.toFixed(2));\n                  }}\n                  placeholder=\"Advance amount\"\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Final Payment (₹)</label>\n                <input\n                  type=\"text\"\n                  value={agreementData.terms.finalPayment}\n                  readOnly\n                  className=\"readonly-field\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-actions\">\n            <button \n              type=\"button\" \n              className=\"cancel-btn\"\n              onClick={() => setShowForm(false)}\n            >\n              Cancel\n            </button>\n            <button \n              type=\"button\" \n              className=\"generate-btn\"\n              onClick={generatePDF}\n              disabled={!agreementData.artisan.name || !agreementData.order.orderId || !agreementData.order.quantity}\n            >\n              Generate PDF Agreement\n            </button>\n          </div>\n        </div>\n      )}\n\n      <div className=\"sample-agreements\">\n        <h3>Recent Agreements</h3>\n        <div className=\"agreements-list\">\n          <div className=\"agreement-item\">\n            <div className=\"agreement-info\">\n              <h4>Agreement with Priya Sharma</h4>\n              <p>Order: ORD001 | 40 Handwoven Sarees | ₹32,000</p>\n              <span className=\"agreement-date\">Created: 2024-01-10</span>\n            </div>\n            <button className=\"download-btn\">Download PDF</button>\n          </div>\n          <div className=\"agreement-item\">\n            <div className=\"agreement-info\">\n              <h4>Agreement with Meera Patel</h4>\n              <p>Order: ORD001 | 45 Handwoven Sarees | ₹36,000</p>\n              <span className=\"agreement-date\">Created: 2024-01-08</span>\n            </div>\n            <button className=\"download-btn\">Download PDF</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default AgreementPDF;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;;AAE3B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE;IACPC,KAAK,EAAE,8BAA8B;IACrCC,QAAQ,EAAE,iCAAiC;IAC3CC,aAAa,EAAE,iBAAiB;IAChCC,iBAAiB,EAAE,sBAAsB;IACzCC,cAAc,EAAE,kBAAkB;IAClCC,YAAY,EAAE,gBAAgB;IAC9BC,YAAY,EAAE,gBAAgB;IAC9BC,eAAe,EAAE,uBAAuB;IACxCC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,cAAc;IAC1BC,OAAO,EAAE,UAAU;IACnBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,aAAa;IACxBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE,aAAa;IACzBC,OAAO,EAAE,WAAW;IACpBC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAE,iBAAiB;IAC/BC,WAAW,EAAE,eAAe;IAC5BC,QAAQ,EAAE,oBAAoB;IAC9BC,gBAAgB,EAAE,oBAAoB;IACtCC,cAAc,EAAE,kBAAkB;IAClCC,YAAY,EAAE,gBAAgB;IAC9BC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,sCAAsC;IACpDC,MAAM,EAAE;EACV,CAAC;EACDC,KAAK,EAAE;IACL/B,KAAK,EAAE,uBAAuB;IAC9BC,QAAQ,EAAE,2BAA2B;IACrCC,aAAa,EAAE,gBAAgB;IAC/BC,iBAAiB,EAAE,iBAAiB;IACpCC,cAAc,EAAE,eAAe;IAC/BC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,gBAAgB;IAC9BC,eAAe,EAAE,iBAAiB;IAClCC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,WAAW;IACvBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,eAAe;IAC1BC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,aAAa;IACtBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,SAAS;IACnBC,YAAY,EAAE,oBAAoB;IAClCC,WAAW,EAAE,WAAW;IACxBC,QAAQ,EAAE,wBAAwB;IAClCC,gBAAgB,EAAE,gBAAgB;IAClCC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE,eAAe;IAC7BC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,kCAAkC;IAChDC,MAAM,EAAE;EACV,CAAC;EACDE,KAAK,EAAE;IACLhC,KAAK,EAAE,8BAA8B;IACrCC,QAAQ,EAAE,qCAAqC;IAC/CC,aAAa,EAAE,eAAe;IAC9BC,iBAAiB,EAAE,wBAAwB;IAC3CC,cAAc,EAAE,sBAAsB;IACtCC,YAAY,EAAE,mBAAmB;IACjCC,YAAY,EAAE,8BAA8B;IAC5CC,eAAe,EAAE,kCAAkC;IACnDC,UAAU,EAAE,eAAe;IAC3BC,UAAU,EAAE,cAAc;IAC1BC,OAAO,EAAE,YAAY;IACrBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,eAAe;IAC1BC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,UAAU;IACtBC,OAAO,EAAE,aAAa;IACtBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,OAAO;IACjBC,YAAY,EAAE,kBAAkB;IAChCC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,qBAAqB;IAC/BC,gBAAgB,EAAE,eAAe;IACjCC,cAAc,EAAE,WAAW;IAC3BC,YAAY,EAAE,aAAa;IAC3BC,MAAM,EAAE,WAAW;IACnBC,YAAY,EAAE,sCAAsC;IACpDC,MAAM,EAAE;EACV,CAAC;EACDG,MAAM,EAAE;IACNjC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,gCAAgC;IAC1CC,aAAa,EAAE,cAAc;IAC7BC,iBAAiB,EAAE,qBAAqB;IACxCC,cAAc,EAAE,mBAAmB;IACnCC,YAAY,EAAE,iBAAiB;IAC/BC,YAAY,EAAE,qBAAqB;IACnCC,eAAe,EAAE,wBAAwB;IACzCC,UAAU,EAAE,UAAU;IACtBC,UAAU,EAAE,aAAa;IACzBC,OAAO,EAAE,YAAY;IACrBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,eAAe;IAC1BC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE,WAAW;IACpBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,aAAa;IACtBC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,YAAY;IAC1BC,WAAW,EAAE,gBAAgB;IAC7BC,QAAQ,EAAE,gBAAgB;IAC1BC,gBAAgB,EAAE,mBAAmB;IACrCC,cAAc,EAAE,sBAAsB;IACtCC,YAAY,EAAE,kBAAkB;IAChCC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,gCAAgC;IAC9CC,MAAM,EAAE;EACV;AACF,CAAC;AAED,SAASI,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,SAAS,CAAC;EACnE,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC;IACjDe,UAAU,EAAE;MACVG,IAAI,EAAE,qBAAqB;MAC3BC,OAAO,EAAE,mBAAmB;MAC5BC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE;IACT,CAAC;IACDN,OAAO,EAAE;MACPE,IAAI,EAAE,EAAE;MACRG,KAAK,EAAE,EAAE;MACTE,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC;IACDsB,KAAK,EAAE;MACLrB,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,gBAAgB,EAAE;IACpB,CAAC;IACDgB,KAAK,EAAE;MACLf,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,EAAE;MAChBe,aAAa,EAAE,yDAAyD;MACxEC,YAAY,EAAE,iEAAiE;MAC/EC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMqD,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACnDX,gBAAgB,CAACY,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACH,OAAO,GAAG;QACT,GAAGG,IAAI,CAACH,OAAO,CAAC;QAChB,CAACC,KAAK,GAAGC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAM/B,QAAQ,GAAGgC,UAAU,CAACf,aAAa,CAACE,KAAK,CAACnB,QAAQ,CAAC,IAAI,CAAC;IAC9D,MAAMC,YAAY,GAAG+B,UAAU,CAACf,aAAa,CAACE,KAAK,CAAClB,YAAY,CAAC,IAAI,CAAC;IACtE,MAAMgC,KAAK,GAAGjC,QAAQ,GAAGC,YAAY;IACrCiB,gBAAgB,CAACY,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPX,KAAK,EAAE;QACL,GAAGW,IAAI,CAACX,KAAK;QACbjB,WAAW,EAAE+B,KAAK,CAACC,OAAO,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,GAAG,GAAG,IAAI9D,KAAK,CAAC,CAAC;IACvB,MAAM+D,SAAS,GAAGD,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACC,KAAK;IAC7C,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,EAAE;;IAElB;IACA,MAAMC,CAAC,GAAGlE,YAAY,CAACsC,gBAAgB,CAAC;;IAExC;IACA,IAAIA,gBAAgB,KAAK,SAAS,EAAE;MAClC;MACA;MACAqB,GAAG,CAACQ,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;IAC5B;;IAEA;IACAR,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAAChE,KAAK,EAAE0D,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEK,KAAK,EAAE;IAAS,CAAC,CAAC;IAEhEL,SAAS,IAAI,EAAE;IACfN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAAC/D,QAAQ,EAAEyD,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEK,KAAK,EAAE;IAAS,CAAC,CAAC;IAEnEL,SAAS,IAAI,EAAE;;IAEf;IACAN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAC9D,aAAa,IAAI,IAAImE,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAER,MAAM,EAAEC,SAAS,CAAC;IACpFA,SAAS,IAAI,EAAE;;IAEf;IACAN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAAC7D,iBAAiB,EAAE2D,MAAM,EAAEC,SAAS,CAAC;IAChDA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACpD,IAAI,IAAI0B,aAAa,CAAC7B,UAAU,CAACG,IAAI,EAAE,EAAEkD,MAAM,EAAEC,SAAS,CAAC;IACzEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACnD,OAAO,IAAIyB,aAAa,CAAC7B,UAAU,CAACI,OAAO,EAAE,EAAEiD,MAAM,EAAEC,SAAS,CAAC;IAC/EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAClD,SAAS,IAAIwB,aAAa,CAAC7B,UAAU,CAACK,SAAS,EAAE,EAAEgD,MAAM,EAAEC,SAAS,CAAC;IACnFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACjD,KAAK,IAAIuB,aAAa,CAAC7B,UAAU,CAACM,KAAK,EAAE,EAAE+C,MAAM,EAAEC,SAAS,CAAC;IAC3EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAChD,KAAK,IAAIsB,aAAa,CAAC7B,UAAU,CAACO,KAAK,EAAE,EAAE8C,MAAM,EAAEC,SAAS,CAAC;IAC3EA,SAAS,IAAI,EAAE;;IAEf;IACAN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAAC5D,cAAc,EAAE0D,MAAM,EAAEC,SAAS,CAAC;IAC7CA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACpD,IAAI,IAAI0B,aAAa,CAAC5B,OAAO,CAACE,IAAI,EAAE,EAAEkD,MAAM,EAAEC,SAAS,CAAC;IACtEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACjD,KAAK,IAAIuB,aAAa,CAAC5B,OAAO,CAACK,KAAK,EAAE,EAAE+C,MAAM,EAAEC,SAAS,CAAC;IACxEA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAC/C,OAAO,IAAIqB,aAAa,CAAC5B,OAAO,CAACO,OAAO,EAAE,EAAE6C,MAAM,EAAEC,SAAS,CAAC;IAC5EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAC9C,UAAU,IAAIoB,aAAa,CAAC5B,OAAO,CAACQ,UAAU,EAAE,EAAE4C,MAAM,EAAEC,SAAS,CAAC;IAClFA,SAAS,IAAI,EAAE;;IAEf;IACAN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAAC3D,YAAY,EAAEyD,MAAM,EAAEC,SAAS,CAAC;IAC3CA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAC7C,OAAO,IAAImB,aAAa,CAACE,KAAK,CAACrB,OAAO,EAAE,EAAE2C,MAAM,EAAEC,SAAS,CAAC;IAC1EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAC5C,OAAO,IAAIkB,aAAa,CAACE,KAAK,CAACpB,OAAO,EAAE,EAAE0C,MAAM,EAAEC,SAAS,CAAC;IAC1EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAC3C,QAAQ,IAAIiB,aAAa,CAACE,KAAK,CAACnB,QAAQ,IAAI2C,CAAC,CAACpC,MAAM,EAAE,EAAEkC,MAAM,EAAEC,SAAS,CAAC;IACxFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAAC1C,YAAY,KAAKgB,aAAa,CAACE,KAAK,CAAClB,YAAY,EAAE,EAAEwC,MAAM,EAAEC,SAAS,CAAC;IACrFA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACzC,WAAW,KAAKe,aAAa,CAACE,KAAK,CAACjB,WAAW,EAAE,EAAEuC,MAAM,EAAEC,SAAS,CAAC;IACnFN,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCF,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACxC,QAAQ,IAAIc,aAAa,CAACE,KAAK,CAAChB,QAAQ,EAAE,EAAEsC,MAAM,EAAEC,SAAS,CAAC;IAC5EA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACvC,gBAAgB,IAAIa,aAAa,CAACE,KAAK,CAACf,gBAAgB,EAAE,EAAEqC,MAAM,EAAEC,SAAS,CAAC;IAC5FA,SAAS,IAAI,EAAE;;IAEf;IACAN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAAC1D,YAAY,EAAEwD,MAAM,EAAEC,SAAS,CAAC;IAC3CA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACtC,cAAc,KAAKY,aAAa,CAACG,KAAK,CAACf,cAAc,KAAK,CAAE2B,UAAU,CAACf,aAAa,CAACG,KAAK,CAACf,cAAc,CAAC,GAAG2B,UAAU,CAACf,aAAa,CAACE,KAAK,CAACjB,WAAW,CAAC,GAAI,GAAG,EAAEgC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAEO,MAAM,EAAEC,SAAS,CAAC;IACjNA,SAAS,IAAI,CAAC;IACdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACrC,YAAY,KAAKW,aAAa,CAACG,KAAK,CAACd,YAAY,IAAIqC,CAAC,CAACnC,YAAY,EAAE,EAAEiC,MAAM,EAAEC,SAAS,CAAC;IACvGA,SAAS,IAAI,EAAE;;IAEf;IACAN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAACzD,eAAe,EAAEuD,MAAM,EAAEC,SAAS,CAAC;IAC9CA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAElC,MAAMxB,KAAK,GAAG,CACZ,gBAAgBH,aAAa,CAACG,KAAK,CAACC,aAAa,EAAE,EACnD,eAAeJ,aAAa,CAACG,KAAK,CAACE,YAAY,EAAE,EACjD,eAAeL,aAAa,CAACG,KAAK,CAACG,aAAa,EAAE,EAClD,0FAA0F,EAC1F,oEAAoE,EACpE,0DAA0D,EAC1D,sEAAsE,CACvE;IAEDH,KAAK,CAAC8B,OAAO,CAACC,IAAI,IAAI;MACpBf,GAAG,CAACU,IAAI,CAACK,IAAI,EAAEV,MAAM,EAAEC,SAAS,EAAE;QAAEU,QAAQ,EAAEf,SAAS,GAAG,CAAC,GAAGI;MAAO,CAAC,CAAC;MACvEC,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IAEFA,SAAS,IAAI,EAAE;;IAEf;IACAN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAACxD,UAAU,EAAEsD,MAAM,EAAEC,SAAS,CAAC;IACzCA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACS,WAAW,CAAC,EAAE,CAAC;IACnBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACvD,UAAU,EAAE,EAAEqD,MAAM,EAAEC,SAAS,CAAC;IAC9CN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACtD,OAAO,EAAE,EAAEgD,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAC5DA,SAAS,IAAI,EAAE;IAEfN,GAAG,CAACU,IAAI,CAAC,uBAAuB,EAAEL,MAAM,EAAEC,SAAS,CAAC;IACpDN,GAAG,CAACU,IAAI,CAAC,uBAAuB,EAAET,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IACrEA,SAAS,IAAI,CAAC;IAEdN,GAAG,CAACU,IAAI,CAAC,GAAG7B,aAAa,CAAC7B,UAAU,CAACG,IAAI,EAAE,EAAEkD,MAAM,EAAEC,SAAS,CAAC;IAC/DN,GAAG,CAACU,IAAI,CAAC,GAAG7B,aAAa,CAAC5B,OAAO,CAACE,IAAI,EAAE,EAAE8C,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;IAC7EA,SAAS,IAAI,CAAC;IAEdN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACrD,IAAI,kBAAkB,EAAEmD,MAAM,EAAEC,SAAS,CAAC;IACxDN,GAAG,CAACU,IAAI,CAAC,GAAGH,CAAC,CAACrD,IAAI,kBAAkB,EAAE+C,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEC,SAAS,CAAC;;IAEzE;IACAA,SAAS,IAAI,EAAE;IACfN,GAAG,CAACS,WAAW,CAAC,CAAC,CAAC;IAClBT,GAAG,CAACQ,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCR,GAAG,CAACU,IAAI,CAACH,CAAC,CAAClC,MAAM,EAAE4B,SAAS,GAAG,CAAC,EAAEK,SAAS,EAAE;MAAEK,KAAK,EAAE;IAAS,CAAC,CAAC;;IAEjE;IACA,MAAMM,QAAQ,GAAG,aAAapC,aAAa,CAACE,KAAK,CAACrB,OAAO,IAAImB,aAAa,CAAC5B,OAAO,CAACE,IAAI,CAAC+D,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,IAAIN,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC5JpB,GAAG,CAACqB,IAAI,CAACJ,QAAQ,CAAC;EACpB,CAAC;EAED,oBACE7E,OAAA;IAAKkF,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCnF,OAAA;MAAKkF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BnF,OAAA;QAAAmF,QAAA,EAAI;MAA0B;QAAAN,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCtF,OAAA;QAAAmF,QAAA,EAAG;MAA8E;QAAAN,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAGrFtF,OAAA;QAAKkF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnF,OAAA;UAAAmF,QAAA,EAAO;QAA4E;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3FtF,OAAA;UACEqD,KAAK,EAAEd,gBAAiB;UACxBgD,QAAQ,EAAGC,CAAC,IAAKhD,mBAAmB,CAACgD,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;UACrD6B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BnF,OAAA;YAAQqD,KAAK,EAAC,SAAS;YAAA8B,QAAA,EAAC;UAAO;YAAAN,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtF,OAAA;YAAQqD,KAAK,EAAC,OAAO;YAAA8B,QAAA,EAAC;UAAa;YAAAN,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CtF,OAAA;YAAQqD,KAAK,EAAC,OAAO;YAAA8B,QAAA,EAAC;UAAa;YAAAN,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CtF,OAAA;YAAQqD,KAAK,EAAC,QAAQ;YAAA8B,QAAA,EAAC;UAAe;YAAAN,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtF,OAAA;QACEkF,SAAS,EAAC,iBAAiB;QAC3BQ,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAAC,CAACD,QAAQ,CAAE;QAAAmC,QAAA,EAErCnC,QAAQ,GAAG,WAAW,GAAG;MAAsB;QAAA6B,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELtC,QAAQ,iBACPhD,OAAA;MAAKkF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BnF,OAAA;QAAKkF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnF,OAAA;UAAAmF,QAAA,EAAI;QAAmB;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BtF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBnF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAc;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BtF,OAAA;cACE2F,IAAI,EAAC,MAAM;cACXtC,KAAK,EAAEZ,aAAa,CAAC5B,OAAO,CAACE,IAAK;cAClCwE,QAAQ,EAAGC,CAAC,IAAKtC,iBAAiB,CAAC,SAAS,EAAE,MAAM,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cACtEuC,WAAW,EAAC,2BAA2B;cACvCC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAc;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BtF,OAAA;cACE2F,IAAI,EAAC,KAAK;cACVtC,KAAK,EAAEZ,aAAa,CAAC5B,OAAO,CAACK,KAAM;cACnCqE,QAAQ,EAAGC,CAAC,IAAKtC,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cACvEuC,WAAW,EAAC,gBAAgB;cAC5BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnF,OAAA;YAAKkF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCnF,OAAA;cAAAmF,QAAA,EAAO;YAAS;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBtF,OAAA;cACEqD,KAAK,EAAEZ,aAAa,CAAC5B,OAAO,CAACO,OAAQ;cACrCmE,QAAQ,EAAGC,CAAC,IAAKtC,iBAAiB,CAAC,SAAS,EAAE,SAAS,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cACzEuC,WAAW,EAAC,wBAAwB;cACpCE,IAAI,EAAC,GAAG;cACRD,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAU;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBtF,OAAA;cACE2F,IAAI,EAAC,MAAM;cACXtC,KAAK,EAAEZ,aAAa,CAAC5B,OAAO,CAACQ,UAAW;cACxCkE,QAAQ,EAAGC,CAAC,IAAKtC,iBAAiB,CAAC,SAAS,EAAE,YAAY,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cAC5EuC,WAAW,EAAC;YAA2B;cAAAf,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtF,OAAA;QAAKkF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnF,OAAA;UAAAmF,QAAA,EAAI;QAAa;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBtF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBnF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAU;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBtF,OAAA;cACE2F,IAAI,EAAC,MAAM;cACXtC,KAAK,EAAEZ,aAAa,CAACE,KAAK,CAACrB,OAAQ;cACnCiE,QAAQ,EAAGC,CAAC,IAAKtC,iBAAiB,CAAC,OAAO,EAAE,SAAS,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cACvEuC,WAAW,EAAC,cAAc;cAC1BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAU;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBtF,OAAA;cACE2F,IAAI,EAAC,QAAQ;cACbtC,KAAK,EAAEZ,aAAa,CAACE,KAAK,CAACnB,QAAS;cACpC+D,QAAQ,EAAGC,CAAC,IAAK;gBACftC,iBAAiB,CAAC,OAAO,EAAE,UAAU,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC;gBACtD0C,UAAU,CAACxC,oBAAoB,EAAE,GAAG,CAAC;cACvC,CAAE;cACFqC,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBnF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAoB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnCtF,OAAA;cACE2F,IAAI,EAAC,QAAQ;cACbK,IAAI,EAAC,MAAM;cACX3C,KAAK,EAAEZ,aAAa,CAACE,KAAK,CAAClB,YAAa;cACxC8D,QAAQ,EAAGC,CAAC,IAAK;gBACftC,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC;gBAC1D0C,UAAU,CAACxC,oBAAoB,EAAE,GAAG,CAAC;cACvC,CAAE;cACFqC,WAAW,EAAC,iBAAiB;cAC7BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAgB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/BtF,OAAA;cACE2F,IAAI,EAAC,MAAM;cACXtC,KAAK,EAAEZ,aAAa,CAACE,KAAK,CAACjB,WAAY;cACvCuE,QAAQ;cACRf,SAAS,EAAC;YAAgB;cAAAL,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAmB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClCtF,OAAA;cACE2F,IAAI,EAAC,MAAM;cACXtC,KAAK,EAAEZ,aAAa,CAACE,KAAK,CAAChB,QAAS;cACpC4D,QAAQ,EAAGC,CAAC,IAAKtC,iBAAiB,CAAC,OAAO,EAAE,UAAU,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cACxEwC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtF,OAAA;QAAKkF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnF,OAAA;UAAAmF,QAAA,EAAI;QAAa;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBtF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBnF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAqB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCtF,OAAA;cACE2F,IAAI,EAAC,QAAQ;cACbK,IAAI,EAAC,MAAM;cACX3C,KAAK,EAAEZ,aAAa,CAACG,KAAK,CAACf,cAAe;cAC1C0D,QAAQ,EAAGC,CAAC,IAAK;gBACftC,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,EAAEsC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC;gBAC5D,MAAM6C,SAAS,GAAG1C,UAAU,CAACf,aAAa,CAACE,KAAK,CAACjB,WAAW,CAAC,GAAG8B,UAAU,CAACgC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC;gBAC1FH,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAEgD,SAAS,CAACxC,OAAO,CAAC,CAAC,CAAC,CAAC;cAClE,CAAE;cACFkC,WAAW,EAAC,gBAAgB;cAC5BC,QAAQ;YAAA;cAAAhB,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtF,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnF,OAAA;cAAAmF,QAAA,EAAO;YAAiB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChCtF,OAAA;cACE2F,IAAI,EAAC,MAAM;cACXtC,KAAK,EAAEZ,aAAa,CAACG,KAAK,CAACd,YAAa;cACxCmE,QAAQ;cACRf,SAAS,EAAC;YAAgB;cAAAL,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtF,OAAA;QAAKkF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnF,OAAA;UACE2F,IAAI,EAAC,QAAQ;UACbT,SAAS,EAAC,YAAY;UACtBQ,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAAC,KAAK,CAAE;UAAAkC,QAAA,EACnC;QAED;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtF,OAAA;UACE2F,IAAI,EAAC,QAAQ;UACbT,SAAS,EAAC,cAAc;UACxBQ,OAAO,EAAE/B,WAAY;UACrBwC,QAAQ,EAAE,CAAC1D,aAAa,CAAC5B,OAAO,CAACE,IAAI,IAAI,CAAC0B,aAAa,CAACE,KAAK,CAACrB,OAAO,IAAI,CAACmB,aAAa,CAACE,KAAK,CAACnB,QAAS;UAAA2D,QAAA,EACxG;QAED;UAAAN,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDtF,OAAA;MAAKkF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCnF,OAAA;QAAAmF,QAAA,EAAI;MAAiB;QAAAN,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BtF,OAAA;QAAKkF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnF,OAAA;UAAKkF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnF,OAAA;YAAKkF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnF,OAAA;cAAAmF,QAAA,EAAI;YAA2B;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpCtF,OAAA;cAAAmF,QAAA,EAAG;YAA6C;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpDtF,OAAA;cAAMkF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAmB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNtF,OAAA;YAAQkF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAY;YAAAN,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNtF,OAAA;UAAKkF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnF,OAAA;YAAKkF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnF,OAAA;cAAAmF,QAAA,EAAI;YAA0B;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnCtF,OAAA;cAAAmF,QAAA,EAAG;YAA6C;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpDtF,OAAA;cAAMkF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAmB;cAAAN,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNtF,OAAA;YAAQkF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAY;YAAAN,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAT,QAAA,EAAAO,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChD,EAAA,CA5bQD,YAAY;AAAA+D,EAAA,GAAZ/D,YAAY;AA8brB,eAAeA,YAAY;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}