{"version": 3, "file": "cache-storage.js", "sourceRoot": "", "sources": ["../../../src/core/cache-storage.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAoC;AAGpC;IAAA;IAuBA,CAAC;IAnBU,sBAAS,GAAhB,UAAiB,GAAW;QACxB,IAAM,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC;QAChC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,aAAa,CAAC;SACxB;QAED,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,kDAAkD;QACzE,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;IACrD,CAAC;IAEM,yBAAY,GAAnB,UAAoB,GAAW;QAC3B,OAAO,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC;IAChE,CAAC;IAEM,uBAAU,GAAjB,UAAkB,MAAc;QAC5B,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACxD,YAAY,CAAC,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxE,CAAC;IApBc,oBAAO,GAAG,aAAa,CAAC;IAqB3C,mBAAC;CAAA,AAvBD,IAuBC;AAvBY,oCAAY;AAgCzB;IAII,eAA6B,OAAgB,EAAmB,QAAyB;QAA5D,YAAO,GAAP,OAAO,CAAS;QAAmB,aAAQ,GAAR,QAAQ,CAAiB;QAHzF,8DAA8D;QAC7C,WAAM,GAAkC,EAAE,CAAC;IAEgC,CAAC;IAE7F,wBAAQ,GAAR,UAAS,GAAW;QAChB,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,MAAM,CAAC;SACjB;QAED,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;YACvC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC3C,8BAA8B;YAClC,CAAC,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;SACjB;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,8DAA8D;IAC9D,qBAAK,GAAL,UAAM,GAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEa,yBAAS,GAAvB,UAAwB,GAAW;;;;;;;wBACzB,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;wBAC9C,OAAO,GACT,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,IAAI,IAAI,mBAAQ,CAAC,mBAAmB,IAAI,CAAC,YAAY,CAAC;wBACrG,QAAQ,GACV,CAAC,aAAa,CAAC,GAAG,CAAC;4BACnB,CAAC,YAAY;4BACb,CAAC,WAAW,CAAC,GAAG,CAAC;4BACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ;4BACvC,mBAAQ,CAAC,gBAAgB;4BACzB,CAAC,OAAO,CAAC;wBACb,IACI,CAAC,YAAY;4BACb,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,KAAK;4BAClC,CAAC,aAAa,CAAC,GAAG,CAAC;4BACnB,CAAC,WAAW,CAAC,GAAG,CAAC;4BACjB,CAAC,QAAQ;4BACT,CAAC,OAAO,EACV;4BACE,sBAAO;yBACV;wBAEG,GAAG,GAAG,GAAG,CAAC;6BACV,QAAQ,EAAR,wBAAQ;wBACF,qBAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAA;;wBAA3B,GAAG,GAAG,SAAqB,CAAC;;;wBAGhC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAe,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAG,CAAC,CAAC;wBAE3D,qBAAM,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gCACrC,IAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;gCACxB,GAAG,CAAC,MAAM,GAAG,cAAM,OAAA,OAAO,CAAC,GAAG,CAAC,EAAZ,CAAY,CAAC;gCAChC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC;gCACrB,qFAAqF;gCACrF,IAAI,mBAAmB,CAAC,GAAG,CAAC,IAAI,OAAO,EAAE;oCACrC,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;iCACjC;gCACD,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;gCACd,IAAI,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE;oCACvB,kEAAkE;oCAClE,UAAU,CAAC,cAAM,OAAA,OAAO,CAAC,GAAG,CAAC,EAAZ,CAAY,EAAE,GAAG,CAAC,CAAC;iCACvC;gCACD,IAAI,KAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE;oCAChC,UAAU,CACN,cAAM,OAAA,MAAM,CAAC,gBAAc,KAAI,CAAC,QAAQ,CAAC,YAAY,sBAAmB,CAAC,EAAnE,CAAmE,EACzE,KAAI,CAAC,QAAQ,CAAC,YAAY,CAC7B,CAAC;iCACL;4BACL,CAAC,CAAC,EAAA;4BAnBF,sBAAO,SAmBL,EAAC;;;;KACN;IAEO,mBAAG,GAAX,UAAY,GAAW;QACnB,OAAO,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW,CAAC;IACnD,CAAC;IAED,oBAAI,GAAJ;QACI,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACrD,CAAC;IAEO,qBAAK,GAAb,UAAc,GAAW;QAAzB,iBA2CC;QA1CG,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAElC,IAAI,CAAC,KAAK,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QAED,IAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAElC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,IAAM,YAAY,GAAG,mBAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;YACtE,IAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;YACjC,GAAG,CAAC,MAAM,GAAG;gBACT,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE;oBACpB,IAAI,YAAY,KAAK,MAAM,EAAE;wBACzB,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;qBACzB;yBAAM;wBACH,IAAM,QAAM,GAAG,IAAI,UAAU,EAAE,CAAC;wBAChC,QAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,cAAM,OAAA,OAAO,CAAC,QAAM,CAAC,MAAgB,CAAC,EAAhC,CAAgC,EAAE,KAAK,CAAC,CAAC;wBAC/E,QAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,CAAC,IAAK,OAAA,MAAM,CAAC,CAAC,CAAC,EAAT,CAAS,EAAE,KAAK,CAAC,CAAC;wBAC1D,QAAM,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;qBACtC;iBACJ;qBAAM;oBACH,MAAM,CAAC,8BAA4B,GAAG,0BAAqB,GAAG,CAAC,MAAQ,CAAC,CAAC;iBAC5E;YACL,CAAC,CAAC;YAEF,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC;YACrB,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACxD,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAG,KAAK,GAAG,WAAW,YAAO,kBAAkB,CAAC,GAAG,CAAC,sBAAiB,YAAc,CAAC,CAAC;YAErG,IAAI,YAAY,KAAK,MAAM,IAAI,GAAG,YAAY,cAAc,EAAE;gBAC1D,GAAG,CAAC,YAAY,GAAG,YAAY,CAAC;aACnC;YAED,IAAI,KAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBAC5B,IAAM,SAAO,GAAG,KAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAC3C,GAAG,CAAC,OAAO,GAAG,SAAO,CAAC;gBACtB,GAAG,CAAC,SAAS,GAAG,cAAM,OAAA,MAAM,CAAC,gBAAc,SAAO,qBAAgB,GAAK,CAAC,EAAlD,CAAkD,CAAC;aAC5E;YAED,GAAG,CAAC,IAAI,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACP,CAAC;IACL,YAAC;AAAD,CAAC,AAlID,IAkIC;AAlIY,sBAAK;AAoIlB,IAAM,UAAU,GAAG,wBAAwB,CAAC;AAC5C,IAAM,aAAa,GAAG,0BAA0B,CAAC;AACjD,IAAM,UAAU,GAAG,kBAAkB,CAAC;AAEtC,IAAM,YAAY,GAAG,UAAC,GAAW,IAAc,OAAA,mBAAQ,CAAC,mBAAmB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAA3C,CAA2C,CAAC;AAC3F,IAAM,aAAa,GAAG,UAAC,GAAW,IAAc,OAAA,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAApB,CAAoB,CAAC;AACrE,IAAM,mBAAmB,GAAG,UAAC,GAAW,IAAc,OAAA,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAvB,CAAuB,CAAC;AAC9E,IAAM,WAAW,GAAG,UAAC,GAAW,IAAc,OAAA,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,EAA3B,CAA2B,CAAC;AAE1E,IAAM,KAAK,GAAG,UAAC,GAAW,IAAc,OAAA,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAA9D,CAA8D,CAAC"}